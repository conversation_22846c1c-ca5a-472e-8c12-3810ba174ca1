# Quick Start Guide: Database Export Feature

## 🚀 How to Use the Database Export Feature

### Access the Feature
1. Open the app
2. Tap the **hamburger menu** (☰) 
3. Navigate to **Database** → **Export Schema**

### Export Options

#### 📋 Schema Only Export
- **What it does**: Exports table structures, column definitions, and constraints
- **Use case**: Database documentation, structure analysis, migration planning
- **File size**: Small (typically < 50KB)
- **Click**: Blue "Schema Only" button

#### 💾 Schema + Data Export  
- **What it does**: Exports complete database with all data
- **Use case**: Full backup, data migration, recovery
- **File size**: Larger (depends on data volume)
- **Click**: Green "Schema + Data" button

### Custom Filename (Optional)
- Enter a custom name in the text field
- Example: `my_backup`, `production_schema`, `test_data`
- Leave empty for automatic date-stamped filename

### Automatic Filenames
When no custom name is provided, files are automatically named:
```
coffee_pos_schema_2024-01-15_14-30-45.sql
```
Format: `coffee_pos_schema_YYYY-MM-DD_HH-MM-SS.sql`

## 📁 Managing Exported Files

### View Exported Files
- All exported files are listed below the export options
- Shows: filename, file size, modification date
- Sorted by newest first

### File Operations
- **Copy Path**: Tap the menu (⋮) → "Copy Path" to copy file location
- **Delete**: Tap the menu (⋮) → "Delete" to remove the file
- **Refresh**: Tap the floating refresh button to update the list

## 📄 Export File Contents

### Header Information
Every export includes:
```sql
-- Coffee POS Database Schema Export
-- Generated on: 2024-01-15T14:30:45.123Z
-- Export Date: 2024-01-15_14-30-45
-- Include Data: true/false
-- Database Version: 2
-- Total Tables: 11
```

### Table Structure
For each table:
```sql
-- Table: Products
CREATE TABLE Products (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  shopId TEXT NOT NULL,
  productId TEXT UNIQUE NOT NULL,
  -- ... more columns
);

-- Column details:
-- Column: id | Type: INTEGER | NotNull: 0 | Default: null | PK: 1
-- Column: shopId | Type: TEXT | NotNull: 1 | Default: null | PK: 0
```

### Data (if included)
```sql
-- Data for table Products (5 rows)
INSERT INTO Products (id, shopId, productId, productName) VALUES (1, 'shop1', 'prod1', 'Coffee');
-- ... more data
```

## 💡 Use Cases

### 🔧 For Developers
- **Database Documentation**: Export schema for documentation
- **Migration Scripts**: Generate SQL for database migrations  
- **Debugging**: Analyze database structure and data
- **Version Control**: Track database changes over time

### 👥 For Business Users
- **Data Backup**: Regular backups before major changes
- **Data Recovery**: Restore data if needed
- **System Migration**: Move data to new systems
- **Compliance**: Maintain data records for auditing

## 📍 File Storage Location

Exported files are saved to:
- **Android/iOS**: `Application Documents/database_exports/`
- **Desktop**: `Application Documents/database_exports/`

## ⚡ Quick Tips

### Best Practices
1. **Regular Backups**: Export schema + data weekly
2. **Before Updates**: Always backup before app updates
3. **Custom Names**: Use descriptive names for important exports
4. **Clean Up**: Delete old exports to save space

### Troubleshooting
- **Export Failed**: Check device storage space
- **File Not Found**: Use refresh button to update file list
- **Large Files**: Schema-only exports are faster for large databases

## 🔄 Programmatic Usage

For developers who want to use the export feature in code:

```dart
import 'package:coffee_cofe/database/database_export_utility.dart';

// Export schema only
final schemaPath = await DatabaseExportUtility.exportSchemaOnly();

// Export with custom filename
final customPath = await DatabaseExportUtility.exportSchemaOnly(
  customFileName: 'my_backup.sql'
);

// Export complete database
final fullPath = await DatabaseExportUtility.exportSchemaWithData();

// List exported files
final files = await DatabaseExportUtility.getExportedFiles();

// Delete a file
await DatabaseExportUtility.deleteExportedFile(filePath);
```

## 🎯 Next Steps

After exporting:
1. **Verify Export**: Check the exported file contains expected data
2. **Store Safely**: Copy important exports to secure storage
3. **Test Restore**: Verify you can use the export if needed
4. **Schedule Regular**: Set up regular export routine

## 🆘 Support

If you encounter issues:
1. Check device storage space
2. Ensure app has file permissions
3. Try schema-only export first
4. Contact support with error details

---

**🎉 You're all set!** The database export feature helps you maintain, backup, and analyze your Coffee POS database with ease.
