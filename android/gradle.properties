# Set JVM max heap size to 2GB to reduce RAM usage (you had 4G)
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError

# Ensure Gradle uses Java 17
org.gradle.java.home=/usr/lib/jvm/java-17-openjdk-amd64

# Use AndroidX support libraries
android.useAndroidX=true
android.enableJetifier=true

# Enable Gradle daemon and parallel execution for better performance
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# Limit number of Gradle workers (reduces Java processes)
org.gradle.workers.max=2

# Optional: Enable R8 for code shrinking (Uncomment if needed)
# android.enableR8=true
