# ========================================
# GRADLE BUILD OPTIMIZATION SETTINGS
# ========================================

# JVM Memory Settings - Optimized for faster builds
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC -XX:+UseStringDeduplication

# Use AndroidX support libraries
android.useAndroidX=true
android.enableJetifier=true

# ========================================
# PERFORMANCE OPTIMIZATIONS
# ========================================

# Enable Gradle daemon for faster subsequent builds
org.gradle.daemon=true

# Enable parallel execution of tasks
org.gradle.parallel=true

# Configure on demand - only configure projects that are needed
org.gradle.configureondemand=true

# Enable build cache for faster incremental builds
org.gradle.caching=true

# Optimize Gradle workers (adjust based on CPU cores)
org.gradle.workers.max=4

# Enable file system watching for faster incremental builds
org.gradle.vfs.watch=true

# ========================================
# ANDROID BUILD OPTIMIZATIONS
# ========================================

# Enable R8 code shrinking and obfuscation
android.enableR8=true

# Enable incremental annotation processing
android.enableIncrementalDesugaring=true

# Enable non-transitive R class generation
android.nonTransitiveRClass=true

# Enable resource shrinking
android.enableResourceOptimizations=true

# ========================================
# KOTLIN OPTIMIZATIONS
# ========================================

# Enable Kotlin incremental compilation
kotlin.incremental=true
kotlin.incremental.android=true

# Use Kotlin compiler daemon
kotlin.compiler.execution.strategy=daemon

# ========================================
# FLUTTER SPECIFIC OPTIMIZATIONS
# ========================================

# Enable Flutter build cache
flutter.buildMode=debug
