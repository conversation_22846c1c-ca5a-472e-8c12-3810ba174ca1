plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode', '1')
def flutterVersionName = localProperties.getProperty('flutter.versionName', '1.0')
 

android {
        namespace = "com.example.coffee_cofe"
  compileSdk = 35
    ndkVersion = flutter.ndkVersion
 

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }


    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
 
    defaultConfig {
        applicationId = "com.example.coffee_cofe"
        minSdk = 22
        targetSdk = 35 // ✅ Remove flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName 
        multiDexEnabled true
    }

    buildTypes {
        release { 
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}
dependencies {  
    implementation 'androidx.multidex:multidex:2.0.1'  
}