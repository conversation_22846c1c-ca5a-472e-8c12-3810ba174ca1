plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode', '1')
def flutterVersionName = localProperties.getProperty('flutter.versionName', '1.0')
 

android {
    namespace = "com.example.coffee_cofe"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    // ========================================
    // JAVA/KOTLIN COMPATIBILITY
    // ========================================
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        // Enable incremental compilation
        incremental = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
        // Enable incremental compilation for Kotlin
        incremental = true
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    // ========================================
    // APP CONFIGURATION
    // ========================================
    defaultConfig {
        applicationId = "com.example.coffee_cofe"
        minSdk = 22
        targetSdk = 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true

        // Enable vector drawable support
        vectorDrawables.useSupportLibrary = true
    }

    // ========================================
    // BUILD TYPES OPTIMIZATION
    // ========================================
    buildTypes {
        debug {
            // Disable debugging for faster builds
            debuggable = true
            minifyEnabled = false
            shrinkResources = false
            // Use debug signing for faster builds
            signingConfig = signingConfigs.debug
        }

        release {
            // Enable optimizations for release builds
            minifyEnabled = true
            shrinkResources = true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig = signingConfigs.debug
        }
    }

    // ========================================
    // BUILD FEATURES OPTIMIZATION
    // ========================================
    buildFeatures {
        // Disable unused features for faster builds
        aidl = false
        renderScript = false
        shaders = false
    }

    // ========================================
    // PACKAGING OPTIONS
    // ========================================
    packagingOptions {
        // Exclude duplicate files
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}

flutter {
    source = "../.."
}
dependencies {  
    implementation 'androidx.multidex:multidex:2.0.1'  
}