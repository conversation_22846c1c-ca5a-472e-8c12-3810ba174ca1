// ========================================
// REPOSITORY CONFIGURATION
// ========================================
allprojects {
    repositories {
        // Use Google's repository first for Android dependencies
        google()
        mavenCentral()
        // Add JitPack for additional libraries if needed
        maven { url 'https://jitpack.io' }
    }
}

// ========================================
# BUILD DIRECTORY CONFIGURATION
// ========================================
rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

// ========================================
# PROJECT EVALUATION OPTIMIZATION
// ========================================
subprojects {
    // Only evaluate app project to speed up configuration
    project.evaluationDependsOn(":app")

    // Apply common configurations to all subprojects
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                // Common compile options for all modules
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
            }
        }
    }
}

// ========================================
# TASK CONFIGURATION
// ========================================
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

// Add a task to clean Flutter build cache
tasks.register("cleanFlutter", Exec) {
    commandLine 'flutter', 'clean'
    workingDir '..'
}
