import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../core/routes/app_routes.dart';
import '../models/user_model.dart';
import '../repositories/auth_repository.dart';
import '../utils/user_services.dart';
import '../utils/workspace_functions.dart';

/// Enhanced authentication provider using repository pattern
class AuthProvider with ChangeNotifier {
  final AuthRepository _authRepository = AuthRepository();
  final WorkspaceFunctions _workspace = WorkspaceFunctions();

  // Controllers
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  // State variables
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  User? _currentUser;
  String? _errorMessage;

  // Getters
  bool get isLoading => _isLoading;
  bool get isPasswordVisible => _isPasswordVisible;
  bool get isConfirmPasswordVisible => _isConfirmPasswordVisible;
  User? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;

  /// Toggle password visibility
  void togglePasswordVisibility() {
    _isPasswordVisible = !_isPasswordVisible;
    notifyListeners();
  }

  /// Toggle confirm password visibility
  void toggleConfirmPasswordVisibility() {
    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    notifyListeners();
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Login with API
  Future<bool> login() async {
    final mobile = mobileController.text.trim();
    final password = passwordController.text.trim();

    if (mobile.isEmpty || password.isEmpty) {
      _setError('Please fill in all fields');
      return false;
    }

    _setLoading(true);
    _setError(null);

    try {
      final response = await _authRepository.login(
        identifier: mobile,
        password: password,
      );

      if (response.success && response.data?.response != null) {
        _currentUser = response.data!.response!;

        // Save user session using existing SessionManager
        await SessionManager.saveUserSession({
          'userId': _currentUser!.userId ?? '',
          'name': _currentUser!.userName ?? '',
          'mobile': _currentUser!.mobileNo ?? mobile,
          'email': _currentUser!.emailAddress ?? '',
          'token': _currentUser!.token ?? '',
          'workspaceId': _currentUser!.workspaceId ?? '',
        });

        // Create workspace for new user using workspace ID from login response
        await _workspace.workSpaceCreationNewUser(Workspace(
          userRole: _currentUser!.role ?? '1',
          workspaceId: _currentUser!.workspaceId ?? 'default_workspace',
          workspaceName: _currentUser!.workspaceName ?? 'Default Workspace',
          workspaceUserId: _currentUser!.userId ?? '',
          workspaceUserName: _currentUser!.userName ?? '',
        ));

        Get.snackbar(
          'Login Success',
          'Welcome ${_currentUser!.userName ?? 'User'}',
          backgroundColor: Colors.greenAccent,
          colorText: Colors.black,
        );

        Get.offAllNamed(AppRoutes.billingScreen);
        return true;
      } else {
        _setError(response.message);
        Get.snackbar(
          'Login Failed',
          response.message,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      Get.snackbar(
        'Login Failed',
        'An error occurred. Please try again.',
        backgroundColor: Colors.redAccent,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Register with API
  Future<bool> register() async {
    final fullName = fullNameController.text.trim();
    final email = emailController.text.trim();
    final mobile = mobileController.text.trim();
    final password = passwordController.text.trim();
    final confirmPassword = confirmPasswordController.text.trim();

    // Validation
    if (fullName.isEmpty || email.isEmpty || mobile.isEmpty || password.isEmpty) {
      _setError('Please fill in all fields');
      return false;
    }

    if (password != confirmPassword) {
      _setError('Passwords do not match');
      return false;
    }

    if (password.length < 6) {
      _setError('Password must be at least 6 characters');
      return false;
    }

    if (!GetUtils.isEmail(email)) {
      _setError('Please enter a valid email address');
      return false;
    }

    if (!GetUtils.isPhoneNumber(mobile)) {
      _setError('Please enter a valid mobile number');
      return false;
    }

    _setLoading(true);
    _setError(null);

    try {
      final response = await _authRepository.register(
        userName: fullName,
        email: email,
        password: password,
        phoneNumber: mobile,
        role: 'user',
      );

      if (response.success && response.data?.response != null) {
        _currentUser = response.data!.response!;

        // Save user session
        await SessionManager.saveUserSession({
          'userId': _currentUser!.userId ?? '',
          'name': _currentUser!.userName ?? '',
          'mobile': _currentUser!.mobileNo ?? mobile,
          'email': _currentUser!.emailAddress ?? email,
          'token': _currentUser!.token ?? '',
          'workspaceId': _currentUser!.workspaceId ?? '',
        });

        // Create workspace for new user using workspace ID from register response
        await _workspace.workSpaceCreationNewUser(Workspace(
          userRole: _currentUser!.role ?? '1',
          workspaceId: _currentUser!.workspaceId ?? 'default_workspace',
          workspaceName: _currentUser!.workspaceName ?? 'Default Workspace',
          workspaceUserId: _currentUser!.userId ?? '',
          workspaceUserName: _currentUser!.userName ?? '',
        ));

        Get.snackbar(
          'Registration Success',
          'Welcome ${_currentUser!.userName ?? 'User'}! Your account has been created.',
          backgroundColor: Colors.greenAccent,
          colorText: Colors.black,
        );

        Get.offAllNamed(AppRoutes.billingScreen);
        return true;
      } else {
        _setError(response.message);
        Get.snackbar(
          'Registration Failed',
          response.message,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      Get.snackbar(
        'Registration Failed',
        'An error occurred. Please try again.',
        backgroundColor: Colors.redAccent,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _authRepository.logout();
      await SessionManager.clearUserSession();

      _currentUser = null;
      _clearControllers();

      Get.snackbar(
        'Logout Success',
        'You have been logged out successfully',
        backgroundColor: Colors.greenAccent,
        colorText: Colors.black,
      );

      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      Get.snackbar(
        'Logout',
        'Logged out successfully',
        backgroundColor: Colors.greenAccent,
        colorText: Colors.black,
      );
      Get.offAllNamed(AppRoutes.login);
    } finally {
      _setLoading(false);
    }
  }

  /// Check authentication status
  Future<bool> checkAuthStatus() async {
    try {
      final isLoggedIn = await SessionManager.isLoggedIn();
      final isAuthenticated = await _authRepository.isAuthenticated();

      return isLoggedIn && isAuthenticated;
    } catch (e) {
      return false;
    }
  }

  /// Get current user profile from API
  Future<void> fetchUserProfile() async {
    try {
      final response = await _authRepository.getProfile();
      if (response.success && response.data != null) {
        _currentUser = response.data!;
        notifyListeners();
      }
    } catch (e) {
      // Handle error silently or show message
    }
  }

  /// Clear all controllers
  void _clearControllers() {
    mobileController.clear();
    passwordController.clear();
    emailController.clear();
    fullNameController.clear();
    confirmPasswordController.clear();
  }

  /// Dispose controllers
  void disposeControllers() {
    mobileController.dispose();
    passwordController.dispose();
    emailController.dispose();
    fullNameController.dispose();
    confirmPasswordController.dispose();
  }

  @override
  void dispose() {
    disposeControllers();
    super.dispose();
  }
}
