import 'package:flutter/material.dart';
import 'database/immediate_fix.dart';

/// SIMPLE DATABASE FIX - Add this to your main app to fix database issues
/// 
/// Usage:
/// 1. Add this import to your main.dart: import 'fix_database_now.dart';
/// 2. Call FixDatabaseNow.fix() in your main() function before runApp()
/// 3. Or add FixDatabaseNow.fixButton() to any screen to manually fix

class FixDatabaseNow {
  
  /// Fix database immediately - call this in main() function
  static Future<void> fix() async {
    try {
      print('🔧 Fixing database schema...');
      await ImmediateDatabaseFix.fixNow();
      print('✅ Database fixed successfully!');
    } catch (e) {
      print('❌ Database fix failed: $e');
    }
  }
  
  /// Check if database needs fixing
  static Future<bool> needsFix() async {
    try {
      return await ImmediateDatabaseFix.isFixNeeded();
    } catch (e) {
      print('Error checking database: $e');
      return true;
    }
  }
  
  /// Get a button widget to fix database manually
  static Widget fixButton() {
    return _DatabaseFixButton();
  }
  
  /// Show database schemas for debugging
  static Future<void> showSchemas() async {
    await ImmediateDatabaseFix.printTableSchemas();
  }
}

class _DatabaseFixButton extends StatefulWidget {
  @override
  _DatabaseFixButtonState createState() => _DatabaseFixButtonState();
}

class _DatabaseFixButtonState extends State<_DatabaseFixButton> {
  bool _isFixing = false;
  String _status = '';
  
  Future<void> _fix() async {
    setState(() {
      _isFixing = true;
      _status = 'Fixing...';
    });
    
    try {
      await ImmediateDatabaseFix.fixNow();
      setState(() {
        _status = '✅ Fixed!';
      });
      
      // Clear status after 3 seconds
      Future.delayed(Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _status = '';
          });
        }
      });
    } catch (e) {
      setState(() {
        _status = '❌ Failed: $e';
      });
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton(
          onPressed: _isFixing ? null : _fix,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: _isFixing 
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 8),
                  Text('Fixing...'),
                ],
              )
            : Text('🔧 Fix Database'),
        ),
        if (_status.isNotEmpty) ...[
          SizedBox(height: 8),
          Text(
            _status,
            style: TextStyle(
              fontSize: 12,
              color: _status.startsWith('✅') ? Colors.green : Colors.red,
            ),
          ),
        ],
      ],
    );
  }
}

/// Auto-fix widget that fixes database on app start
class AutoFixDatabase extends StatefulWidget {
  final Widget child;
  
  const AutoFixDatabase({Key? key, required this.child}) : super(key: key);
  
  @override
  _AutoFixDatabaseState createState() => _AutoFixDatabaseState();
}

class _AutoFixDatabaseState extends State<AutoFixDatabase> {
  bool _isChecking = true;
  bool _isFixing = false;
  String _status = 'Checking database...';
  
  @override
  void initState() {
    super.initState();
    _checkAndFix();
  }
  
  Future<void> _checkAndFix() async {
    try {
      // Check if fix is needed
      final needsFix = await ImmediateDatabaseFix.isFixNeeded();
      
      if (needsFix) {
        setState(() {
          _isFixing = true;
          _status = 'Fixing database...';
        });
        
        await ImmediateDatabaseFix.fixNow();
        
        setState(() {
          _status = 'Database fixed!';
        });
        
        await Future.delayed(Duration(seconds: 1));
      }
      
      setState(() {
        _isChecking = false;
        _isFixing = false;
      });
      
    } catch (e) {
      print('Auto-fix error: $e');
      setState(() {
        _isChecking = false;
        _isFixing = false;
        _status = 'Fix failed: $e';
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isChecking || _isFixing) {
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(_status),
              ],
            ),
          ),
        ),
      );
    }
    
    return widget.child;
  }
}
