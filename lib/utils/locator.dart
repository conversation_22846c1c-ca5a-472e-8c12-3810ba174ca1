import 'package:get/get.dart';
import '../features/profile/profile_settings_controller.dart';
import 'cache_storeage_services.dart';

Future setupLocator() async {
  // Register StorageService and call init() manually
  Get.put<StorageService>(StorageService()); // Register the service
  await Get.find<StorageService>().init(); // Manually call init() on the registered instance

  // Register ProfileController
  Get.put(ProfileController());
}
