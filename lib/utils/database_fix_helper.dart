import 'dart:developer';
import '../database/database_repair_utility.dart';

/// Simple helper to fix database issues
class DatabaseFixHelper {
  
  /// Quick fix for database schema issues
  /// Call this method when you encounter database column errors
  static Future<bool> quickFix() async {
    try {
      log('🔧 Starting database quick fix...');
      
      // Check current health
      log('📊 Checking database health...');
      await DatabaseRepairUtility.printHealthReport();
      
      // Run repair
      log('🛠️ Running database repair...');
      await DatabaseRepairUtility.runCompleteRepair();
      
      // Verify fix
      log('✅ Verifying repair...');
      final missingColumns = await DatabaseRepairUtility.checkDatabaseHealth();
      
      if (missingColumns.isEmpty) {
        log('🎉 Database repair completed successfully!');
        return true;
      } else {
        log('⚠️ Some issues remain after repair:');
        missingColumns.forEach((table, columns) {
          log('  Table $table still missing: ${columns.join(', ')}');
        });
        return false;
      }
    } catch (e) {
      log('❌ Database repair failed: $e');
      return false;
    }
  }
  
  /// Fix specific table issues
  static Future<void> fixSalesTable() async {
    try {
      log('🔧 Fixing Sales table...');
      await DatabaseRepairUtility.repairSalesTable();
      log('✅ Sales table fixed');
    } catch (e) {
      log('❌ Failed to fix Sales table: $e');
    }
  }
  
  static Future<void> fixDiscountTable() async {
    try {
      log('🔧 Fixing Discount table...');
      await DatabaseRepairUtility.repairDiscountTable();
      log('✅ Discount table fixed');
    } catch (e) {
      log('❌ Failed to fix Discount table: $e');
    }
  }
  
  static Future<void> fixSalesTransactionTable() async {
    try {
      log('🔧 Fixing SalesTransaction table...');
      await DatabaseRepairUtility.repairSalesTransactionTable();
      log('✅ SalesTransaction table fixed');
    } catch (e) {
      log('❌ Failed to fix SalesTransaction table: $e');
    }
  }
}
