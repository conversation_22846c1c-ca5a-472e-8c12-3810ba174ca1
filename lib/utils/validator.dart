class Validator {
  // Validate if string is not empty
  static String? validateRequired(String? value, {String fieldName = 'This field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    return null;
  }

  // Validate email format
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required.';
    }

    final emailRegex = RegExp(r"^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$");
    if (!emailRegex.hasMatch(value)) {
      return 'Enter a valid email address.';
    }
    return null;
  }

  // Validate number only (e.g., for user ID, role ID)
  static String? validateNumeric(String? value, {String fieldName = 'This field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    final numericRegex = RegExp(r'^[0-9]+$');
    if (!numericRegex.hasMatch(value)) {
      return '$fieldName must be numeric.';
    }
    return null;
  }

  // Validate boolean flag (0 or 1)
  static String? validateBooleanFlag(dynamic value, {String fieldName = 'This field'}) {
    if (value != 0 && value != 1) {
      return '$fieldName must be 0 or 1.';
    }
    return null;
  }

  // Validate workspace ID
  static String? validateWorkspaceId(String? value) {
    return validateRequired(value, fieldName: 'Workspace ID');
  }

  // Validate date (YYYY-MM-DD)
  static String? validateDate(String? value, {String fieldName = 'Date'}) {
    if (value == null || value.isEmpty) return '$fieldName is required.';
    final dateRegex = RegExp(r'^\d{4}-\d{2}-\d{2}$');
    if (!dateRegex.hasMatch(value)) return 'Invalid $fieldName format. Use YYYY-MM-DD.';
    return null;
  }

  // Validate workspace name
  static String? validateWorkspaceName(String? value) {
    return validateRequired(value, fieldName: 'Workspace Name');
  }

  // Validate username
  static String? validateUserName(String? value) {
    return validateRequired(value, fieldName: 'Username');
  }

  // Validate list not empty (e.g., permissions or settings)
  static String? validateListNotEmpty(List<dynamic>? list, {String fieldName = 'List'}) {
    if (list == null || list.isEmpty) {
      return '$fieldName cannot be empty.';
    }
    return null;
  }
}
