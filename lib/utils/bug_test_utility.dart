import 'dart:developer';
import '../database/app_database.dart';
import '../models/discounts.dart';
import '../database/entities/product.dart';
import '../database/entities/sales_transaction_dto.dart';
import '../services/simple_gst_service.dart';
import '../features/settings/settings_provider.dart';

/// Utility class to test and identify bugs in the system
class BugTestUtility {
  /// Test discount functionality
  static Future<TestResult> testDiscountFunctionality() async {
    try {
      log('Testing discount functionality...');

      final enhancedDiscountDao = await AppDatabase().enhancedDiscountDao;

      // Test 1: Create a test discount
      final testDiscount = Discounts(
        discountID: 'test-discount-001',
        type: 'S',
        mode: 'C',
        discountOn: 'P',
        discount: '10',
        minDiscount: '100',
        maxDiscount: '500',
        couponName: 'TEST10',
        fromDate: DateTime.now().toString(),
        toDate: DateTime.now().add(const Duration(days: 30)).toString(),
        discountActive: 1,
        discountSync: 0,
        workspaceID: 'test-workspace',
        createdDate: DateTime.now().toString(),
        rowStatus: 0,
      );

      // Test insertion
      await enhancedDiscountDao.insertDiscount(testDiscount);
      log('✓ Discount insertion successful');

      // Test retrieval
      final retrievedDiscount = await enhancedDiscountDao.getDiscountById('test-discount-001');
      if (retrievedDiscount == null) {
        return TestResult(false, 'Failed to retrieve inserted discount');
      }
      log('✓ Discount retrieval successful');

      // Test coupon lookup
      final couponDiscount = await enhancedDiscountDao.getDiscountByCouponCode('TEST10');
      if (couponDiscount == null) {
        return TestResult(false, 'Failed to retrieve discount by coupon code');
      }
      log('✓ Coupon code lookup successful');

      // Test pagination
      final discounts = await enhancedDiscountDao.getDiscountsWithPagination(limit: 10);
      if (discounts.isEmpty) {
        return TestResult(false, 'Failed to retrieve discounts with pagination');
      }
      log('✓ Discount pagination successful');

      // Cleanup
      await enhancedDiscountDao.deleteDiscount('test-discount-001');
      log('✓ Discount cleanup successful');

      return TestResult(true, 'All discount tests passed');
    } catch (e) {
      log('✗ Discount test failed: $e');
      return TestResult(false, 'Discount test failed: $e');
    }
  }

  /// Test GST functionality
  static Future<TestResult> testGSTFunctionality() async {
    try {
      log('Testing GST functionality...');

      final productDao = await AppDatabase().productDao;
      final gstService = SimpleGSTService();

      // Create test product with GST
      final testProduct = ProductDto(
        shopId: 'test-shop',
        productId: 'test-product-001',
        productName: 'Test Product',
        price: '100.00',
        cgst: '9.0',
        sgst: '9.0',
        createdDate: DateTime.now().toIso8601String(),
        rowStatus: 0,
      );

      await productDao.insertProduct(testProduct);
      log('✓ Test product created');

      // Create test sales transaction
      final testTransaction = SalesTransactionDto(
        salesTransactionId: 'test-txn-001',
        productId: 'test-product-001',
        productQty: 2,
        productAmount: 200.0,
        createdAt: DateTime.now().toIso8601String(),
        status: 1,
      );

      // Create mock settings provider
      final mockSettings = MockSettingsProvider();
      mockSettings.isTaxEnabled = true;
      mockSettings.applyTaxAllProducts = true;

      // Test GST calculation
      final gstResult = await gstService.calculateGST(
        cartItems: [testTransaction],
        discountAmount: 0,
        settingsProvider: mockSettings,
      );

      if (gstResult.totalGST <= 0) {
        return TestResult(false, 'GST calculation returned zero or negative value');
      }

      log('✓ GST calculation successful: Total GST = ${gstResult.totalGST}');

      // Cleanup - find the product ID first
      final allProducts = await productDao.getAllProducts();
      final testProductToDelete = allProducts.firstWhere(
        (p) => p.productId == 'test-product-001',
        orElse: () => throw Exception('Test product not found for cleanup'),
      );

      if (testProductToDelete.id != null) {
        await productDao.deleteProduct(testProductToDelete.id!);
        log('✓ GST test cleanup successful');
      } else {
        log('⚠ Test product cleanup skipped - no ID found');
      }

      return TestResult(true, 'All GST tests passed');
    } catch (e) {
      log('✗ GST test failed: $e');
      return TestResult(false, 'GST test failed: $e');
    }
  }

  /// Test report functionality
  static Future<TestResult> testReportFunctionality() async {
    try {
      log('Testing report functionality...');

      final salesTransactionDao = await AppDatabase().salesTransactionDao;

      // Create test transactions
      final testTransactions = [
        SalesTransactionDto(
          salesTransactionId: 'test-report-txn-001',
          productId: 'test-product-001',
          productName: 'Test Product 1',
          productQty: 1,
          productAmount: 100.0,
          createdAt: DateTime.now().toIso8601String(),
          status: 1,
        ),
        SalesTransactionDto(
          salesTransactionId: 'test-report-txn-002',
          productId: 'test-product-002',
          productName: 'Test Product 2',
          productQty: 2,
          productAmount: 200.0,
          createdAt: DateTime.now().toIso8601String(),
          status: 1,
        ),
      ];

      // Insert test transactions
      for (final txn in testTransactions) {
        await salesTransactionDao.insertTransaction(txn);
      }
      log('✓ Test transactions created');

      // Test retrieval
      final allTransactions = await salesTransactionDao.getAllTransactions();
      if (allTransactions.length < 2) {
        return TestResult(false, 'Failed to retrieve all test transactions');
      }
      log('✓ Transaction retrieval successful');

      // Test date filtering
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));
      final tomorrow = today.add(const Duration(days: 1));

      final filteredTransactions = await salesTransactionDao.getTransactionsBetweenDates(yesterday, tomorrow);
      if (filteredTransactions.isEmpty) {
        log('⚠ Date filtering returned no results (this might be expected)');
      } else {
        log('✓ Date filtering successful: ${filteredTransactions.length} transactions');
      }

      // Cleanup - find the transaction IDs first
      final allStoredTransactions = await salesTransactionDao.getAllTransactions();
      for (final txn in testTransactions) {
        final transactionToDelete = allStoredTransactions.firstWhere(
          (t) => t.salesTransactionId == txn.salesTransactionId,
          orElse: () => throw Exception('Test transaction not found for cleanup'),
        );

        if (transactionToDelete.id != null) {
          await salesTransactionDao.deleteTransaction(transactionToDelete.id!);
        } else {
          log('⚠ Test transaction cleanup skipped - no ID found for ${txn.salesTransactionId}');
        }
      }
      log('✓ Report test cleanup successful');

      return TestResult(true, 'All report tests passed');
    } catch (e) {
      log('✗ Report test failed: $e');
      return TestResult(false, 'Report test failed: $e');
    }
  }

  /// Run all tests
  static Future<List<TestResult>> runAllTests() async {
    log('=== Starting Bug Test Suite ===');

    final results = <TestResult>[];

    // Test discount functionality
    results.add(await testDiscountFunctionality());

    // Test GST functionality
    results.add(await testGSTFunctionality());

    // Test report functionality
    results.add(await testReportFunctionality());

    log('=== Bug Test Suite Completed ===');

    final passedTests = results.where((r) => r.success).length;
    final totalTests = results.length;

    log('Test Results: $passedTests/$totalTests passed');

    for (int i = 0; i < results.length; i++) {
      final result = results[i];
      final testName = ['Discount', 'GST', 'Report'][i];
      log('$testName Test: ${result.success ? "PASSED" : "FAILED"} - ${result.message}');
    }

    return results;
  }

  /// Test database schema integrity
  static Future<TestResult> testDatabaseSchema() async {
    try {
      log('Testing database schema integrity...');

      final database = await AppDatabase().database;

      // Test discount table
      final discountColumns = await database.rawQuery("PRAGMA table_info(Discount)");
      final requiredDiscountColumns = ['discountID', 'discountCouponName', 'discountActive'];

      for (final required in requiredDiscountColumns) {
        final found = discountColumns.any((col) => col['name'] == required);
        if (!found) {
          return TestResult(false, 'Missing required discount column: $required');
        }
      }

      log('✓ Discount table schema OK');

      // Test product table
      final productColumns = await database.rawQuery("PRAGMA table_info(Products)");
      final requiredProductColumns = ['productId', 'productName', 'cgst', 'sgst'];

      for (final required in requiredProductColumns) {
        final found = productColumns.any((col) => col['name'] == required);
        if (!found) {
          return TestResult(false, 'Missing required product column: $required');
        }
      }

      log('✓ Product table schema OK');

      return TestResult(true, 'Database schema integrity check passed');
    } catch (e) {
      log('✗ Database schema test failed: $e');
      return TestResult(false, 'Database schema test failed: $e');
    }
  }
}

/// Test result class
class TestResult {
  final bool success;
  final String message;

  TestResult(this.success, this.message);
}

/// Mock settings provider for testing
class MockSettingsProvider extends SettingsProvider {
  bool _isTaxEnabled = false;
  bool _applyTaxAllProducts = false;
  bool _applyTaxSpecificProduct = false;

  @override
  bool get isTaxEnabled => _isTaxEnabled;

  @override
  set isTaxEnabled(bool value) => _isTaxEnabled = value;

  @override
  bool get applyTaxAllProducts => _applyTaxAllProducts;

  @override
  set applyTaxAllProducts(bool value) => _applyTaxAllProducts = value;

  @override
  bool get applyTaxSpecificProduct => _applyTaxSpecificProduct;

  @override
  set applyTaxSpecificProduct(bool value) => _applyTaxSpecificProduct = value;
}
