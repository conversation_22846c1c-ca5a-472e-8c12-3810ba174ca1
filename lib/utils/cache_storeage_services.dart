import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../core/constants/images.dart';

class StorageService extends GetxService {
  static const String _keyUserName = 'userName';
  static const String _keyProfilePath = 'profilePath';

  late SharedPreferences _prefs;

  // Initialize SharedPreferences
  Future init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Save user name to SharedPreferences
  Future<void> saveUserName(String name) async {
    await _prefs.setString(_keyUserName, name);
  }

  // Get the user name from SharedPreferences, with a default value if not found
  String get userName => _prefs.getString(_keyUserName) ?? 'Dinesh';

  // Save profile path to SharedPreferences
  Future<void> saveProfilePath(String path) async {
    await _prefs.setString(_keyProfilePath, path);
  }

  // Get the profile path from SharedPreferences, with a default value if not found
  String get profilePath => _prefs.getString(_keyProfilePath) ?? defaultProfileSVG;
}
