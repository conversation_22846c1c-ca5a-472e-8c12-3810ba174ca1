import 'dart:developer' as developer;
import 'dart:io';
import 'package:stack_trace/stack_trace.dart';

void logError(Object error, StackTrace? stack) {
  final trace = Trace.from(stack ?? StackTrace.current).terse;

  // Define color codes for terminal output (visible in IDEs & console)
  const red = '\x1B[31m';
  const yellow = '\x1B[33m';
  const blue = '\x1B[34m';
  const green = '\x1B[32m';
  const cyan = '\x1B[36m';
  const reset = '\x1B[0m';

  String typeColor;
  String tag;

  // Classify and color-code the error type
  if (error is SocketException) {
    typeColor = blue;
    tag = '[NETWORK ERROR]';
  } else if (error is FormatException) {
    typeColor = yellow;
    tag = '[FORMAT ERROR]';
  } else if (error is FileSystemException) {
    typeColor = cyan;
    tag = '[FILE SYSTEM ERROR]';
  } else if (error is AssertionError) {
    typeColor = green;
    tag = '[ASSERTION ERROR]';
  } else {
    typeColor = red;
    tag = '[UNCAUGHT ERROR]';
  }

  final message = '''
$typeColor$tag $error$reset
📍 Stack trace:
$trace
''';

  // Use log() for IDE integration
  developer.log(
    message,
    name: 'GlobalErrorHandler',
    error: error,
    stackTrace: trace,
  );
}
