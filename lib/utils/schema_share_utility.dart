import 'dart:io';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path/path.dart' as path;
import '../database/database_export_utility.dart';

/// Utility class for sharing database schemas
class SchemaShareUtility {
  /// Share schema file with multiple options
  static Future<void> shareSchema({
    required BuildContext context,
    bool includeData = false,
    String? customFileName,
    String? customMessage,
  }) async {
    try {
      // Show loading dialog
      _showLoadingDialog(context);

      // Export schema to file
      final filePath = await DatabaseExportUtility.exportSchemaWithDate(
        includeData: includeData,
        customFileName: customFileName,
      );

      // Hide loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Share the file
      await _shareFile(
        filePath: filePath,
        includeData: includeData,
        customMessage: customMessage,
      );
    } catch (e) {
      // Hide loading dialog if still showing
      if (context.mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      log('Error sharing schema: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'Failed to share schema: $e');
      }
    }
  }

  /// Share existing schema file
  static Future<void> shareExistingFile({
    required String filePath,
    String? customMessage,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }

      final fileName = path.basename(filePath);
      final includeData =
          fileName.toLowerCase().contains('data') || fileName.toLowerCase().contains('complete');

      await _shareFile(
        filePath: filePath,
        includeData: includeData,
        customMessage: customMessage,
      );
    } catch (e) {
      log('Error sharing existing file: $e');
      throw Exception('Failed to share file: $e');
    }
  }

  /// Share schema content as text
  static Future<void> shareSchemaAsText({
    bool includeData = false,
    String? customMessage,
  }) async {
    try {
      // Generate schema content
      final content = await _generateSchemaText(includeData);

      final message = customMessage ?? _getDefaultMessage(includeData);
      final fullText = '$message\n\n$content';

      await Share.share(
        fullText,
        subject: 'Coffee POS Database Schema',
      );
    } catch (e) {
      log('Error sharing schema as text: $e');
      throw Exception('Failed to share schema as text: $e');
    }
  }

  /// Show share options dialog
  static Future<void> showShareOptionsDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.share, color: Colors.blue),
              SizedBox(width: 8),
              Text('Share Schema'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Choose what to share:'),
              const SizedBox(height: 16),
              _buildShareOption(
                context: context,
                icon: Icons.table_chart,
                title: 'Schema Only',
                subtitle: 'Share table structures without data',
                onTap: () {
                  Navigator.of(context).pop();
                  shareSchema(context: context, includeData: false);
                },
              ),
              const SizedBox(height: 8),
              _buildShareOption(
                context: context,
                icon: Icons.storage,
                title: 'Schema + Data',
                subtitle: 'Share complete database with all data',
                onTap: () {
                  Navigator.of(context).pop();
                  shareSchema(context: context, includeData: true);
                },
              ),
              const SizedBox(height: 8),
              _buildShareOption(
                context: context,
                icon: Icons.text_fields,
                title: 'As Text',
                subtitle: 'Share schema structure as text message',
                onTap: () {
                  Navigator.of(context).pop();
                  shareSchemaAsText();
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Private helper methods

  static Future<void> _shareFile({
    required String filePath,
    required bool includeData,
    String? customMessage,
  }) async {
    final fileName = path.basename(filePath);

    final message = customMessage ?? _getDefaultMessage(includeData);

    await Share.shareXFiles(
      [XFile(filePath)],
      text: message,
      subject: 'Coffee POS Database Schema - $fileName',
    );
  }

  static String _getDefaultMessage(bool includeData) {
    if (includeData) {
      return '📊 Coffee POS Complete Database Export\n\n'
          'This file contains the complete database schema and all data from Coffee POS system.\n'
          'Generated on: ${DateTime.now().toString().split('.')[0]}';
    } else {
      return '🗂️ Coffee POS Database Schema\n\n'
          'This file contains the database table structures for Coffee POS system.\n'
          'Generated on: ${DateTime.now().toString().split('.')[0]}';
    }
  }

  static Future<String> _generateSchemaText(bool includeData) async {
    // This would generate a simplified text version of the schema
    // For now, we'll create a basic summary
    final timestamp = DateTime.now().toString().split('.')[0];

    return '''
Coffee POS Database Schema Summary
Generated: $timestamp
Type: ${includeData ? 'Schema + Data' : 'Schema Only'}

Tables:
- Products
- Sales
- Sales Transactions
- Inventory
- Settings
- Workspace Settings
- Discounts
- GST Configuration
- And more...

This is a ${includeData ? 'complete database export' : 'schema-only export'} from Coffee POS system.
''';
  }

  static Widget _buildShareOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.blue, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  static void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Preparing schema for sharing...'),
            ],
          ),
        );
      },
    );
  }

  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 8),
              Text('Error'),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
