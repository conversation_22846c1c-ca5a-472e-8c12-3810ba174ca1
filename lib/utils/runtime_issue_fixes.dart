import 'dart:developer';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sqflite/sqflite.dart';
import '../database/app_database.dart';

/// Runtime issue fixes for common problems
class RuntimeIssueFixes {
  /// Fix potential memory leaks in providers
  static void fixProviderMemoryLeaks() {
    // Ensure providers are properly disposed
    log('🔧 Applying provider memory leak fixes...');

    // This would be implemented in individual providers
    // by ensuring proper disposal of controllers and streams
  }

  /// Fix async/await deadlock issues
  static Future<T> safeAsyncOperation<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
    String operationName = 'Unknown',
  }) async {
    try {
      log('🔄 Starting safe async operation: $operationName');

      final result = await operation().timeout(
        timeout,
        onTimeout: () {
          throw TimeoutException('Operation $operationName timed out after ${timeout.inSeconds}s');
        },
      );

      log('✅ Safe async operation completed: $operationName');
      return result;
    } catch (e) {
      log('❌ Safe async operation failed: $operationName - $e');
      rethrow;
    }
  }

  /// Fix database connection issues
  static Future<Database> getSafeDatabase({int maxRetries = 3}) async {
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        log('🔄 Attempting database connection (attempt ${retryCount + 1}/$maxRetries)');

        final database = await AppDatabase().database;

        // Test the connection
        await database.rawQuery('SELECT 1');

        log('✅ Database connection successful');
        return database;
      } catch (e) {
        retryCount++;
        log('❌ Database connection failed (attempt $retryCount): $e');

        if (retryCount >= maxRetries) {
          throw DatabaseConnectionException('Failed to connect to database after $maxRetries attempts: $e');
        }

        // Wait before retry with exponential backoff
        await Future.delayed(Duration(milliseconds: 500 * retryCount));
      }
    }

    throw DatabaseConnectionException('Unexpected error in database connection');
  }

  /// Fix null pointer exceptions in database operations
  static Future<T?> safeDbOperation<T>(
    Future<T> Function() operation, {
    String operationName = 'Database Operation',
  }) async {
    try {
      log('🔄 Starting safe DB operation: $operationName');

      // Ensure database is available
      await getSafeDatabase();

      final result = await operation();
      log('✅ Safe DB operation completed: $operationName');
      return result;
    } catch (e) {
      log('❌ Safe DB operation failed: $operationName - $e');

      // Return null instead of throwing for non-critical operations
      if (e is DatabaseConnectionException) {
        rethrow;
      }

      return null;
    }
  }

  /// Fix widget state management issues - use as extension method
  /// Example: state.safeSetState(() => _isLoading = false);
  // This method is now available as an extension on State class

  /// Fix potential race conditions in async operations
  static Future<T> raceConditionSafeOperation<T>(
    Future<T> Function() operation,
    String operationId,
  ) async {
    final completer = Completer<T>();
    bool isCompleted = false;

    try {
      final result = await operation();

      if (!isCompleted) {
        isCompleted = true;
        completer.complete(result);
      }

      return result;
    } catch (e) {
      if (!isCompleted) {
        isCompleted = true;
        completer.completeError(e);
      }
      rethrow;
    }
  }

  /// Fix potential issues with concurrent database access
  static final Map<String, Completer<dynamic>> _operationQueue = {};

  static Future<T> queuedDbOperation<T>(
    String operationKey,
    Future<T> Function() operation,
  ) async {
    // If operation is already in progress, wait for it
    if (_operationQueue.containsKey(operationKey)) {
      log('⏳ Waiting for queued operation: $operationKey');
      await _operationQueue[operationKey]!.future;
    }

    // Start new operation
    final completer = Completer<T>();
    _operationQueue[operationKey] = completer;

    try {
      log('🔄 Starting queued operation: $operationKey');
      final result = await operation();
      completer.complete(result);
      return result;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      _operationQueue.remove(operationKey);
      log('✅ Completed queued operation: $operationKey');
    }
  }

  /// Fix potential issues with stream subscriptions
  static StreamSubscription<T> safeStreamSubscription<T>(
    Stream<T> stream,
    void Function(T) onData, {
    Function? onError,
    void Function()? onDone,
    String subscriptionName = 'Unknown',
  }) {
    log('🔄 Creating safe stream subscription: $subscriptionName');

    return stream.listen(
      onData,
      onError: (error) {
        log('❌ Stream error in $subscriptionName: $error');
        if (onError != null) {
          onError(error);
        }
      },
      onDone: () {
        log('✅ Stream completed: $subscriptionName');
        if (onDone != null) {
          onDone();
        }
      },
      cancelOnError: false, // Don't cancel on error
    );
  }

  /// Fix potential issues with text controllers
  static void safeDisposeController(TextEditingController? controller, String controllerName) {
    try {
      if (controller != null) {
        controller.dispose();
        log('✅ Safely disposed controller: $controllerName');
      }
    } catch (e) {
      log('❌ Error disposing controller $controllerName: $e');
    }
  }

  /// Fix potential issues with navigation
  static void safeNavigate(
    BuildContext context,
    Widget destination, {
    bool replace = false,
    String routeName = 'Unknown',
  }) {
    try {
      if (context.mounted) {
        log('🔄 Safe navigation to: $routeName');

        if (replace) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => destination),
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => destination),
          );
        }

        log('✅ Safe navigation completed: $routeName');
      } else {
        log('⚠️ Attempted navigation with unmounted context: $routeName');
      }
    } catch (e) {
      log('❌ Navigation error to $routeName: $e');
    }
  }

  /// Fix potential issues with shared preferences
  static Future<T?> safePrefsOperation<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    try {
      log('🔄 Safe prefs operation: $operationName');
      final result = await operation();
      log('✅ Safe prefs operation completed: $operationName');
      return result;
    } catch (e) {
      log('❌ Safe prefs operation failed: $operationName - $e');
      return null;
    }
  }

  /// Apply all runtime fixes
  static Future<void> applyAllFixes() async {
    log('🔧 Applying all runtime issue fixes...');

    try {
      // Test database connection
      await getSafeDatabase();

      // Apply provider fixes
      fixProviderMemoryLeaks();

      log('✅ All runtime fixes applied successfully');
    } catch (e) {
      log('❌ Error applying runtime fixes: $e');
    }
  }

  /// Get system health status
  static Future<Map<String, dynamic>> getSystemHealth() async {
    final health = <String, dynamic>{};

    try {
      // Test database
      final dbStart = DateTime.now();
      await getSafeDatabase();
      final dbTime = DateTime.now().difference(dbStart).inMilliseconds;
      health['database'] = {'status': 'healthy', 'responseTime': '${dbTime}ms'};
    } catch (e) {
      health['database'] = {'status': 'unhealthy', 'error': e.toString()};
    }

    // Test memory usage (simplified)
    health['memory'] = {'status': 'unknown', 'note': 'Memory monitoring not implemented'};

    // Overall status
    final hasErrors = health.values.any((v) => v['status'] == 'unhealthy');
    health['overall'] = hasErrors ? 'unhealthy' : 'healthy';

    return health;
  }
}

/// Custom exception for database connection issues
class DatabaseConnectionException implements Exception {
  final String message;
  DatabaseConnectionException(this.message);

  @override
  String toString() => 'DatabaseConnectionException: $message';
}

/// Widget mixin for safe state management
mixin SafeStateMixin<T extends StatefulWidget> on State<T> {
  void safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    } else {
      log('⚠️ Attempted to call setState on unmounted widget: ${T.toString()}');
    }
  }

  /// Safe context access - checks if widget is still mounted
  BuildContext? get safeContext => mounted ? context : null;

  /// Safe navigation - only navigates if widget is mounted
  void safeNavigate(String route, {Object? arguments}) {
    if (mounted) {
      Navigator.of(context).pushNamed(route, arguments: arguments);
    } else {
      log('⚠️ Attempted navigation on unmounted widget: ${T.toString()}');
    }
  }

  /// Safe pop - only pops if widget is mounted and can pop
  void safePop([Object? result]) {
    if (mounted && Navigator.of(context).canPop()) {
      Navigator.of(context).pop(result);
    } else {
      log('⚠️ Attempted pop on unmounted widget or empty stack: ${T.toString()}');
    }
  }

  /// Safe provider access
  P? safeProvider<P extends ChangeNotifier>({bool listen = false}) {
    try {
      if (mounted) {
        return Provider.of<P>(context, listen: listen);
      }
      return null;
    } catch (e) {
      log('⚠️ Error accessing provider ${P.toString()}: $e');
      return null;
    }
  }

  void safeDispose() {
    // Override this in your widgets to safely dispose resources
  }

  @override
  void dispose() {
    safeDispose();
    super.dispose();
  }
}

/// Extension for safe setState operations
/// Note: Use this pattern in your widgets:
/// if (mounted) setState(() => _isLoading = false);

/// Global error handler for widget lifecycle issues
class WidgetLifecycleErrorHandler {
  static void handleError(FlutterErrorDetails details) {
    final error = details.exception.toString().toLowerCase();

    if (error.contains('looking up a deactivated widget') ||
        error.contains('ancestor is unsafe') ||
        error.contains('widget\'s element tree is no longer stable')) {
      log('🔧 Widget lifecycle error caught and handled: ${details.exception}');

      // Don't crash the app for these errors
      return;
    }

    // For other errors, use default handling
    FlutterError.presentError(details);
  }

  static void initialize() {
    FlutterError.onError = handleError;
  }
}
