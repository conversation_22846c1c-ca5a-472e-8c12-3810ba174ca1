import 'dart:developer';
import 'dart:async';
import '../database/app_database.dart';
import '../features/settings/settings_provider.dart';
import '../services/simple_gst_service.dart';

/// Comprehensive bug detector to identify specific runtime issues
class ComprehensiveBugDetector {
  /// Run comprehensive bug detection
  static Future<List<BugReport>> detectAllBugs() async {
    final bugs = <BugReport>[];

    log('🔍 Starting comprehensive bug detection...');

    // Test 1: Database connectivity and schema
    bugs.addAll(await _testDatabaseIssues());

    // Test 2: Provider initialization issues
    bugs.addAll(await _testProviderIssues());

    // Test 3: Navigation and context issues
    bugs.addAll(await _testNavigationIssues());

    // Test 4: GST calculation issues
    bugs.addAll(await _testGSTIssues());

    // Test 5: Discount functionality issues
    bugs.addAll(await _testDiscountIssues());

    // Test 6: Report generation issues
    bugs.addAll(await _testReportIssues());

    // Test 7: State management issues
    bugs.addAll(await _testStateManagementIssues());

    log('🔍 Bug detection completed. Found ${bugs.length} potential issues.');

    return bugs;
  }

  /// Test database-related issues
  static Future<List<BugReport>> _testDatabaseIssues() async {
    final bugs = <BugReport>[];

    try {
      log('🔍 Testing database issues...');

      // Test database connection
      final database = await AppDatabase().database;

      // Test if critical tables exist
      final tables = ['Discount', 'Products', 'SalesTransaction', 'Sales'];
      for (final table in tables) {
        try {
          await database.rawQuery('SELECT COUNT(*) FROM $table LIMIT 1');
        } catch (e) {
          bugs.add(BugReport(
            severity: BugSeverity.critical,
            category: 'Database',
            title: 'Missing or corrupted table: $table',
            description: 'Table $table is missing or corrupted: $e',
            solution: 'Run database schema fix or recreate database',
          ));
        }
      }

      // Test if required columns exist in Discount table
      try {
        final discountColumns = await database.rawQuery("PRAGMA table_info(Discount)");
        final columnNames = discountColumns.map((col) => col['name'] as String).toSet();

        final requiredColumns = ['discountID', 'discountCouponName', 'discountActive'];
        for (final required in requiredColumns) {
          if (!columnNames.contains(required)) {
            bugs.add(BugReport(
              severity: BugSeverity.high,
              category: 'Database Schema',
              title: 'Missing discount column: $required',
              description: 'Required column $required is missing from Discount table',
              solution: 'Run schema fix to add missing columns',
            ));
          }
        }
      } catch (e) {
        bugs.add(BugReport(
          severity: BugSeverity.critical,
          category: 'Database Schema',
          title: 'Cannot check Discount table schema',
          description: 'Failed to check Discount table schema: $e',
          solution: 'Check if Discount table exists and is accessible',
        ));
      }
    } catch (e) {
      bugs.add(BugReport(
        severity: BugSeverity.critical,
        category: 'Database Connection',
        title: 'Database connection failed',
        description: 'Cannot connect to database: $e',
        solution: 'Check database initialization and file permissions',
      ));
    }

    return bugs;
  }

  /// Test provider-related issues
  static Future<List<BugReport>> _testProviderIssues() async {
    final bugs = <BugReport>[];

    try {
      log('🔍 Testing provider issues...');

      // Test SettingsProvider initialization
      try {
        final settingsProvider = SettingsProvider();
        // Test basic properties
        settingsProvider.isTaxEnabled;
        settingsProvider.applyTaxAllProducts;
      } catch (e) {
        bugs.add(BugReport(
          severity: BugSeverity.high,
          category: 'Provider',
          title: 'SettingsProvider initialization failed',
          description: 'SettingsProvider cannot be initialized: $e',
          solution: 'Check SettingsProvider constructor and dependencies',
        ));
      }
    } catch (e) {
      bugs.add(BugReport(
        severity: BugSeverity.medium,
        category: 'Provider',
        title: 'Provider testing failed',
        description: 'Cannot test providers: $e',
        solution: 'Check provider implementations',
      ));
    }

    return bugs;
  }

  /// Test navigation-related issues
  static Future<List<BugReport>> _testNavigationIssues() async {
    final bugs = <BugReport>[];

    // Navigation issues are typically runtime-specific
    // We can only detect potential issues here

    log('🔍 Testing navigation issues...');

    // Check for common navigation anti-patterns
    // This would require static analysis of the codebase

    return bugs;
  }

  /// Test GST calculation issues
  static Future<List<BugReport>> _testGSTIssues() async {
    final bugs = <BugReport>[];

    try {
      log('🔍 Testing GST issues...');

      final gstService = SimpleGSTService();
      final mockSettings = _createMockSettings();

      // Test with empty cart
      try {
        final result = await gstService.calculateGST(
          cartItems: [],
          discountAmount: 0,
          settingsProvider: mockSettings,
        );

        if (result.totalGST < 0) {
          bugs.add(BugReport(
            severity: BugSeverity.medium,
            category: 'GST Calculation',
            title: 'Negative GST calculation',
            description: 'GST calculation returned negative value for empty cart',
            solution: 'Check GST calculation logic for edge cases',
          ));
        }
      } catch (e) {
        bugs.add(BugReport(
          severity: BugSeverity.high,
          category: 'GST Calculation',
          title: 'GST calculation failed for empty cart',
          description: 'GST service failed with empty cart: $e',
          solution: 'Add null checks and validation in GST service',
        ));
      }
    } catch (e) {
      bugs.add(BugReport(
        severity: BugSeverity.high,
        category: 'GST Service',
        title: 'GST service initialization failed',
        description: 'Cannot initialize GST service: $e',
        solution: 'Check GST service dependencies and constructor',
      ));
    }

    return bugs;
  }

  /// Test discount functionality issues
  static Future<List<BugReport>> _testDiscountIssues() async {
    final bugs = <BugReport>[];

    try {
      log('🔍 Testing discount issues...');

      final enhancedDiscountDao = await AppDatabase().enhancedDiscountDao;

      // Test discount retrieval
      try {
        await enhancedDiscountDao.getDiscountsWithPagination(limit: 1);
        // If this succeeds, basic discount functionality is working
      } catch (e) {
        bugs.add(BugReport(
          severity: BugSeverity.high,
          category: 'Discount',
          title: 'Discount retrieval failed',
          description: 'Cannot retrieve discounts from database: $e',
          solution: 'Check discount table schema and DAO implementation',
        ));
      }
    } catch (e) {
      bugs.add(BugReport(
        severity: BugSeverity.critical,
        category: 'Discount DAO',
        title: 'Discount DAO initialization failed',
        description: 'Cannot initialize discount DAO: $e',
        solution: 'Check database connection and DAO dependencies',
      ));
    }

    return bugs;
  }

  /// Test report generation issues
  static Future<List<BugReport>> _testReportIssues() async {
    final bugs = <BugReport>[];

    try {
      log('🔍 Testing report issues...');

      final salesTransactionDao = await AppDatabase().salesTransactionDao;

      // Test transaction retrieval
      try {
        await salesTransactionDao.getAllTransactions();
        // If this succeeds, basic report functionality should work
      } catch (e) {
        bugs.add(BugReport(
          severity: BugSeverity.high,
          category: 'Reports',
          title: 'Transaction retrieval failed',
          description: 'Cannot retrieve transactions for reports: $e',
          solution: 'Check SalesTransaction table and DAO implementation',
        ));
      }
    } catch (e) {
      bugs.add(BugReport(
        severity: BugSeverity.high,
        category: 'Reports DAO',
        title: 'Sales transaction DAO failed',
        description: 'Cannot initialize sales transaction DAO: $e',
        solution: 'Check database connection and DAO dependencies',
      ));
    }

    return bugs;
  }

  /// Test state management issues
  static Future<List<BugReport>> _testStateManagementIssues() async {
    final bugs = <BugReport>[];

    log('🔍 Testing state management issues...');

    // State management issues are typically runtime-specific
    // We can add checks for common patterns here

    return bugs;
  }

  /// Create mock settings for testing
  static SettingsProvider _createMockSettings() {
    final settings = SettingsProvider();
    // Set some default values for testing
    return settings;
  }

  /// Generate bug report summary
  static String generateBugReportSummary(List<BugReport> bugs) {
    if (bugs.isEmpty) {
      return '✅ No bugs detected! System appears to be healthy.';
    }

    final critical = bugs.where((b) => b.severity == BugSeverity.critical).length;
    final high = bugs.where((b) => b.severity == BugSeverity.high).length;
    final medium = bugs.where((b) => b.severity == BugSeverity.medium).length;
    final low = bugs.where((b) => b.severity == BugSeverity.low).length;

    final summary = StringBuffer();
    summary.writeln('🐛 Bug Detection Summary:');
    summary.writeln('Total Issues: ${bugs.length}');
    if (critical > 0) summary.writeln('🔴 Critical: $critical');
    if (high > 0) summary.writeln('🟠 High: $high');
    if (medium > 0) summary.writeln('🟡 Medium: $medium');
    if (low > 0) summary.writeln('🟢 Low: $low');

    summary.writeln('\n📋 Issues by Category:');
    final categories = bugs.map((b) => b.category).toSet();
    for (final category in categories) {
      final categoryBugs = bugs.where((b) => b.category == category).length;
      summary.writeln('• $category: $categoryBugs issues');
    }

    return summary.toString();
  }
}

/// Bug report data class
class BugReport {
  final BugSeverity severity;
  final String category;
  final String title;
  final String description;
  final String solution;

  BugReport({
    required this.severity,
    required this.category,
    required this.title,
    required this.description,
    required this.solution,
  });
}

/// Bug severity levels
enum BugSeverity {
  critical, // App crashes or major functionality broken
  high, // Important features not working
  medium, // Minor issues or performance problems
  low, // Cosmetic or minor usability issues
}
