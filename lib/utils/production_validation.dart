import 'dart:developer';

/// Production-ready validation utilities
class ProductionValidation {
  /// Validate discount data before saving
  static ValidationResult validateDiscount({
    required String? discountType,
    required String? discountMode,
    required String? discountValue,
    required String? minLimit,
    String? maxLimit,
    String? couponName,
    required String? fromDate,
    required String? toDate,
  }) {
    final errors = <String>[];

    // Validate discount type
    if (discountType == null || discountType.isEmpty) {
      errors.add('Discount type is required');
    } else if (!['S', 'P'].contains(discountType)) {
      errors.add('Invalid discount type. Must be S (Sales Wise) or P (Product Wise)');
    }

    // Validate discount mode
    if (discountMode == null || discountMode.isEmpty) {
      errors.add('Discount mode is required');
    } else if (!['A', 'C'].contains(discountMode)) {
      errors.add('Invalid discount mode. Must be A (Auto) or C (Coupon)');
    }

    // Validate discount value
    if (discountValue == null || discountValue.isEmpty) {
      errors.add('Discount value is required');
    } else {
      final value = double.tryParse(discountValue);
      if (value == null || value <= 0) {
        errors.add('Discount value must be a positive number');
      } else if (value > 100) {
        errors.add('Discount percentage cannot exceed 100%');
      }
    }

    // Validate minimum limit
    if (minLimit == null || minLimit.isEmpty) {
      errors.add('Minimum limit is required');
    } else {
      final value = double.tryParse(minLimit);
      if (value == null || value < 0) {
        errors.add('Minimum limit must be a non-negative number');
      }
    }

    // Validate maximum limit if provided
    if (maxLimit != null && maxLimit.isNotEmpty) {
      final maxValue = double.tryParse(maxLimit);
      final minValue = double.tryParse(minLimit ?? '0');
      if (maxValue == null || maxValue < 0) {
        errors.add('Maximum limit must be a non-negative number');
      } else if (minValue != null && maxValue < minValue) {
        errors.add('Maximum limit cannot be less than minimum limit');
      }
    }

    // Validate coupon name for coupon-based discounts
    if (discountMode == 'C') {
      if (couponName == null || couponName.trim().isEmpty) {
        errors.add('Coupon name is required for coupon-based discounts');
      } else if (couponName.length < 3) {
        errors.add('Coupon name must be at least 3 characters long');
      } else if (couponName.length > 50) {
        errors.add('Coupon name cannot exceed 50 characters');
      }
    }

    // Validate dates
    if (fromDate == null || fromDate.isEmpty) {
      errors.add('From date is required');
    }
    if (toDate == null || toDate.isEmpty) {
      errors.add('To date is required');
    }

    if (fromDate != null && toDate != null && fromDate.isNotEmpty && toDate.isNotEmpty) {
      try {
        final from = DateTime.parse(fromDate);
        final to = DateTime.parse(toDate);
        if (to.isBefore(from)) {
          errors.add('To date cannot be before from date');
        }
        if (from.isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
          errors.add('From date cannot be in the past');
        }
      } catch (e) {
        errors.add('Invalid date format');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Validate product data before saving
  static ValidationResult validateProduct({
    required String? productName,
    required String? price,
    String? cgst,
    String? sgst,
    String? categoryId,
  }) {
    final errors = <String>[];

    // Validate product name
    if (productName == null || productName.trim().isEmpty) {
      errors.add('Product name is required');
    } else if (productName.length < 2) {
      errors.add('Product name must be at least 2 characters long');
    } else if (productName.length > 100) {
      errors.add('Product name cannot exceed 100 characters');
    }

    // Validate price
    if (price == null || price.isEmpty) {
      errors.add('Price is required');
    } else {
      final priceValue = double.tryParse(price);
      if (priceValue == null || priceValue <= 0) {
        errors.add('Price must be a positive number');
      } else if (priceValue > 999999) {
        errors.add('Price cannot exceed ₹999,999');
      }
    }

    // Validate GST rates if provided
    if (cgst != null && cgst.isNotEmpty) {
      final cgstValue = double.tryParse(cgst);
      if (cgstValue == null || cgstValue < 0 || cgstValue > 50) {
        errors.add('CGST must be between 0% and 50%');
      }
    }

    if (sgst != null && sgst.isNotEmpty) {
      final sgstValue = double.tryParse(sgst);
      if (sgstValue == null || sgstValue < 0 || sgstValue > 50) {
        errors.add('SGST must be between 0% and 50%');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Validate sales transaction before saving
  static ValidationResult validateSalesTransaction({
    required List<dynamic> cartItems,
    required String? paymentMethod,
  }) {
    final errors = <String>[];

    // Validate cart items
    if (cartItems.isEmpty) {
      errors.add('Cart cannot be empty');
    }

    for (int i = 0; i < cartItems.length; i++) {
      final item = cartItems[i];
      if (item.productQty == null || item.productQty <= 0) {
        errors.add('Item ${i + 1}: Quantity must be greater than 0');
      }
      if (item.productAmount == null || item.productAmount <= 0) {
        errors.add('Item ${i + 1}: Amount must be greater than 0');
      }
    }

    // Validate payment method
    if (paymentMethod == null || paymentMethod.isEmpty) {
      errors.add('Payment method is required');
    } else if (!['Cash', 'Card', 'UPI', 'GPay'].contains(paymentMethod)) {
      errors.add('Invalid payment method');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Validate GST settings
  static ValidationResult validateGSTSettings({
    required bool isTaxEnabled,
    required bool applyTaxAllProducts,
    required bool applyTaxSpecificProduct,
  }) {
    final errors = <String>[];

    if (isTaxEnabled) {
      if (!applyTaxAllProducts && !applyTaxSpecificProduct) {
        errors.add('When tax is enabled, you must select either "All Products" or "Specific Products"');
      }
      if (applyTaxAllProducts && applyTaxSpecificProduct) {
        errors.add('Cannot apply tax to both "All Products" and "Specific Products" simultaneously');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Log validation errors for debugging
  static void logValidationErrors(String context, ValidationResult result) {
    if (!result.isValid) {
      log('Validation failed for $context:');
      for (final error in result.errors) {
        log('  - $error');
      }
    }
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.errors,
  });

  /// Get formatted error message
  String get errorMessage {
    if (isValid) return '';
    return errors.join('\n');
  }

  /// Get first error message
  String get firstError {
    if (isValid || errors.isEmpty) return '';
    return errors.first;
  }
}

/// Production error handler
class ProductionErrorHandler {
  /// Handle and log errors consistently
  static void handleError(String context, dynamic error, {StackTrace? stackTrace}) {
    log('Error in $context: $error');
    if (stackTrace != null) {
      log('Stack trace: $stackTrace');
    }
  }

  /// Show user-friendly error message
  static String getUserFriendlyMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('database')) {
      return 'Database error occurred. Please try again.';
    } else if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network error. Please check your connection.';
    } else if (errorString.contains('permission')) {
      return 'Permission denied. Please check your access rights.';
    } else if (errorString.contains('validation')) {
      return 'Invalid data provided. Please check your input.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
}
