import 'dart:developer';
import 'package:dio/dio.dart';

/// Enhanced API error handler for debugging 500 errors
class ApiErrorHandler {
  /// Handle and log detailed error information
  static void handleError(DioException error, {String? context}) {
    final requestId = error.response?.headers.value('x-request-id') ??
        error.response?.headers.value('request-id') ??
        'Unknown';

    log('🚨 API ERROR DETAILS 🚨');
    log('Context: ${context ?? 'Unknown'}');
    log('Request ID: $requestId');
    log('Status Code: ${error.response?.statusCode}');
    log('Error Type: ${error.type}');
    log('URL: ${error.requestOptions.uri}');
    log('Method: ${error.requestOptions.method}');

    // Log request details
    log('📤 REQUEST DETAILS:');
    log('Headers: ${error.requestOptions.headers}');
    if (error.requestOptions.data != null) {
      log('Request Data: ${error.requestOptions.data}');
    }
    if (error.requestOptions.queryParameters.isNotEmpty) {
      log('Query Params: ${error.requestOptions.queryParameters}');
    }

    // Log response details
    if (error.response != null) {
      log('📥 RESPONSE DETAILS:');
      log('Response Headers: ${error.response!.headers}');
      log('Response Data: ${error.response!.data}');
    }

    // Log error message
    log('Error Message: ${error.message}');
    log('Stack Trace: ${error.stackTrace}');
    log('🚨 END ERROR DETAILS 🚨');
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(DioException error) {
    switch (error.response?.statusCode) {
      case 400:
        return 'Invalid request. Please check your input.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission.';
      case 404:
        return 'Resource not found. Please try again.';
      case 500:
        return 'Server error. Please try again later or contact support.';
      case 502:
        return 'Server temporarily unavailable. Please try again.';
      case 503:
        return 'Service unavailable. Please try again later.';
      default:
        return 'Network error. Please check your connection.';
    }
  }

  /// Check if error is retryable
  static bool isRetryable(DioException error) {
    final statusCode = error.response?.statusCode;
    return statusCode == null ||
        statusCode >= 500 ||
        error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout;
  }

  /// Create detailed error report
  static Map<String, dynamic> createErrorReport(DioException error, {String? context}) {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'context': context,
      'requestId':
          error.response?.headers.value('x-request-id') ?? error.response?.headers.value('request-id'),
      'statusCode': error.response?.statusCode,
      'errorType': error.type.toString(),
      'url': error.requestOptions.uri.toString(),
      'method': error.requestOptions.method,
      'requestHeaders': error.requestOptions.headers,
      'requestData': error.requestOptions.data?.toString(),
      'queryParameters': error.requestOptions.queryParameters,
      'responseHeaders': error.response?.headers.map,
      'responseData': error.response?.data?.toString(),
      'errorMessage': error.message,
      'userFriendlyMessage': getUserFriendlyMessage(error),
      'isRetryable': isRetryable(error),
    };
  }
}

/// Enhanced Dio client with better error handling
class EnhancedDioClient {
  static void setupEnhancedErrorHandling(Dio dio) {
    dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) {
          // Log detailed error information
          ApiErrorHandler.handleError(error, context: 'API Request');

          // You can send error report to your error tracking service
          // final errorReport = ApiErrorHandler.createErrorReport(error);
          // crashlytics.recordError(errorReport, null);

          handler.next(error);
        },
      ),
    );
  }
}

/// Retry mechanism for failed requests
class ApiRetryHandler {
  static Future<Response<T>> retryRequest<T>(
    Dio dio,
    RequestOptions requestOptions, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        final response = await dio.fetch<T>(requestOptions);
        return response;
      } on DioException catch (e) {
        retryCount++;

        if (retryCount >= maxRetries || !ApiErrorHandler.isRetryable(e)) {
          rethrow;
        }

        log('🔄 Retrying request (attempt $retryCount/$maxRetries) after ${delay.inSeconds}s...');
        await Future.delayed(delay);

        // Exponential backoff
        delay = Duration(seconds: delay.inSeconds * 2);
      }
    }

    throw DioException(
      requestOptions: requestOptions,
      error: 'Max retries exceeded',
    );
  }
}
