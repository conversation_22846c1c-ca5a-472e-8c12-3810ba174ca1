import 'dart:developer';

import 'package:collection/collection.dart';

import '../database/app_database.dart';
import '../database/dao/workspace_dao.dart';
import '../database/entities/workspace_dto.dart';
import '../models/user_model.dart';

class WorkspaceFunctions {
  late final Future<WorkspaceSettingsDao> _daoFuture;

  WorkspaceFunctions() {
    _daoFuture = AppDatabase().workspaceSettingsDao;
  }

  Future<void> workSpaceCreationNewUser(Workspace workspace) async {
    try {
      final dao = await _daoFuture;

      final workspaceSettings = WorkspaceSettingsDto(
        workSpaceId: workspace.workspaceId,
        wSName: workspace.workspaceName,
        wSUserName: workspace.workspaceUserName,
        wSUserId: workspace.workspaceUserId,
        wSSettingKey: 'theme',
        wSSettingValue: 'dark',
        workSsettingSync: 0,
      );

      final existingSettings = await dao.getAll();
      final existing = existingSettings.firstWhereOrNull(
        (e) =>
            e.workSpaceId == workspaceSettings.workSpaceId &&
            e.wSSettingKey == workspaceSettings.wSSettingKey,
      );

      if (existing != null) {
        // Update if exists
        final updated = workspaceSettings.copyWith(id: existing.id);
        await dao.update(updated);
      } else {
        // Insert new
        await dao.insert(workspaceSettings);
      }

      log("Workspace settings saved locally.");
    } catch (e, stack) {
      log('Error in workSpaceCreationNewUser: $e\n$stack');
    }
  }
}
