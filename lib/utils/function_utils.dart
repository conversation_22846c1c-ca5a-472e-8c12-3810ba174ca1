import 'dart:developer';
import 'dart:io';

import 'package:coffee_cofe/utils/db_stubs.dart';
import '../database/app_database.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';

// Simplified FnUtilities class with essential methods only
class FnUtilities {
  bool isLoading = true;
  DateTime now = DateTime.now();

  // Essential method used in manage_discount.dart
  void splitValueFromFormula(dynamic discount) {
    // Simplified implementation - just a placeholder
    // This method was used to process discount formulas
    // Add actual implementation based on your business logic
    log('Processing discount formula for discount');
  }

  // Date formatting methods
  String convertDate(originalDate) {
    var date = DateFormat("dd-MM-yyyy").format(DateTime.parse("$originalDate"));
    return date;
  }

  String convertDateMonthYear(originalDate) {
    var date = DateFormat("MMMM-yyyy").format(DateTime.parse("$originalDate"));
    return date;
  }

  String getTime(originalDate) {
    var date = DateFormat.jm().format(DateTime.parse(originalDate));
    return date;
  }

  String getDate(originalDate) {
    var date = DateFormat.yMMMd().format(DateTime.parse(originalDate));
    return date;
  }

  // File handling methods
  Future<File?> readFileFromLocal(fileName) async {
    final Directory directory = await getApplicationDocumentsDirectory();
    if (await File('${directory.path}/$fileName.png').exists()) {
      final File file = File('${directory.path}/$fileName.png');
      return file;
    } else {
      return null;
    }
  }

  Future<String?> readFilePathFromLocal(fileName) async {
    final Directory directory = await getApplicationDocumentsDirectory();
    if (await File('${directory.path}/$fileName.png').exists()) {
      final String filePath = '${directory.path}/$fileName.png';
      return filePath;
    } else {
      return null;
    }
  }

  // URL launcher method
  openAppInPlayStore() async {
    Uri url = Uri.parse(" ");
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      showToast('Could not launch $url');
    }
  }

  // Quantity formatting
  checkQtyDoubleValue(String value) {
    try {
      if ((double.parse(value) % 1) == 0) {
        var firstValue = value.split('.');
        return firstValue[0];
      } else {
        return value;
      }
    } catch (e) {
      return value;
    }
  }

  // Settings and configuration methods
  getGeneralSetValue(String keyName) async {
    try {
      final settingsDao = await AppDatabase().settingsDao;
      final settings = await settingsDao.getAllSettings('default_shop');
      for (final setting in settings) {
        if (setting.keyName == keyName) {
          return setting.value;
        }
      }
      return null;
    } catch (e) {
      log('Error getting general setting $keyName: $e');
      return null;
    }
  }

  getDeviceSetValue(String keyName) async {
    try {
      // For now, return device-specific defaults
      final deviceSettings = {
        'device_id': 'coffee_pos_device_001',
        'printer_enabled': 'true',
        'auto_backup': 'false',
        'theme': 'light',
      };
      return deviceSettings[keyName];
    } catch (e) {
      log('Error getting device setting $keyName: $e');
      return null;
    }
  }

  getAllProductDetails() async {
    try {
      final productDao = await AppDatabase().productDao;
      return await productDao.getAllProducts();
    } catch (e) {
      log('Error getting all products: $e');
      return [];
    }
  }

  getAllWorkspaceSetting() async {
    try {
      final workspaceDao = await AppDatabase().workspaceSettingsDao;
      return await workspaceDao.getAll();
    } catch (e) {
      log('Error getting workspace settings: $e');
      return [];
    }
  }

  getAllWorkspaceUserSettings() async {
    try {
      // Return default user settings for workspace
      return [
        {'setting_key': 'user_role', 'setting_value': 'admin'},
        {'setting_key': 'permissions', 'setting_value': 'all'},
        {'setting_key': 'language', 'setting_value': 'en'},
      ];
    } catch (e) {
      log('Error getting workspace user settings: $e');
      return [];
    }
  }

  getAllWorkspaceDeviceSettings() async {
    try {
      // Return default device settings for workspace
      return [
        {'setting_key': 'auto_sync', 'setting_value': 'true'},
        {'setting_key': 'offline_mode', 'setting_value': 'false'},
        {'setting_key': 'backup_frequency', 'setting_value': 'daily'},
      ];
    } catch (e) {
      log('Error getting workspace device settings: $e');
      return [];
    }
  }

  getAllPaymentType() async {
    try {
      // Return standard payment types
      return [
        {'id': '1', 'name': 'Cash', 'active': true},
        {'id': '2', 'name': 'Card', 'active': true},
        {'id': '3', 'name': 'UPI', 'active': true},
        {'id': '4', 'name': 'Net Banking', 'active': true},
        {'id': '5', 'name': 'Wallet', 'active': true},
      ];
    } catch (e) {
      log('Error getting payment types: $e');
      return [];
    }
  }

  getAllProductKey() async {
    try {
      final productCategoryDao = await AppDatabase().productCategoryDao;
      return await productCategoryDao.getAllProductCategories();
    } catch (e) {
      log('Error getting product categories: $e');
      return [];
    }
  }

  getAllUnits() async {
    try {
      // Return standard units
      return [
        {'id': '1', 'name': 'Piece', 'symbol': 'pcs'},
        {'id': '2', 'name': 'Kilogram', 'symbol': 'kg'},
        {'id': '3', 'name': 'Gram', 'symbol': 'g'},
        {'id': '4', 'name': 'Liter', 'symbol': 'l'},
        {'id': '5', 'name': 'Milliliter', 'symbol': 'ml'},
        {'id': '6', 'name': 'Box', 'symbol': 'box'},
        {'id': '7', 'name': 'Packet', 'symbol': 'pkt'},
      ];
    } catch (e) {
      log('Error getting units: $e');
      return [];
    }
  }

  getAllShopTables() async {
    try {
      // Return default shop tables for restaurant mode
      return [
        {'id': '1', 'name': 'Table 1', 'capacity': 4, 'status': 'available'},
        {'id': '2', 'name': 'Table 2', 'capacity': 2, 'status': 'available'},
        {'id': '3', 'name': 'Table 3', 'capacity': 6, 'status': 'available'},
        {'id': '4', 'name': 'Table 4', 'capacity': 4, 'status': 'available'},
        {'id': '5', 'name': 'Counter', 'capacity': 1, 'status': 'available'},
      ];
    } catch (e) {
      log('Error getting shop tables: $e');
      return [];
    }
  }

  getAllToppings() async {
    // Placeholder implementation
  }

  getAllToppingsGroup() async {
    // Placeholder implementation
  }

  getAllExpensesCategoryList() async {
    // Placeholder implementation
  }

  getAllExpenses() async {
    // Placeholder implementation
  }

  getAllSalesCategory() async {
    // Placeholder implementation
  }

  getAllShiftDetails() async {
    // Placeholder implementation
  }

  getAllOrderNotes() async {
    // Placeholder implementation
  }

  getMasterInfoBasedOnWorkspace() async {
    // Placeholder implementation that calls other methods
    await getAllProductDetails();
    await getAllWorkspaceSetting();
    await getAllWorkspaceUserSettings();
    await getAllWorkspaceDeviceSettings();
    await getAllPaymentType();
    await getAllProductKey();
    await getAllUnits();
    await getAllShopTables();
    await getAllToppings();
    await getAllToppingsGroup();
    await getAllExpensesCategoryList();
    await getAllExpenses();
    await getAllSalesCategory();
    await getAllShiftDetails();
    await getAllOrderNotes();
  }

  // Widget helper methods
  RichText paymentInvoicewidget(BuildContext context, String invoiceFullNo) {
    List<String> parts = invoiceFullNo.split('/');
    return RichText(
      text: TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: parts[0],
            style: const TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500),
          ),
          if (parts.length > 1 && parts[1].isNotEmpty)
            TextSpan(
              text: ' / ${parts[1]}',
              style: const TextStyle(color: Colors.black, fontWeight: FontWeight.normal, fontSize: 14),
            )
        ],
      ),
    );
  }

  String getPaymentInvoiceNo(int index, dynamic model) {
    String invoiceFullNo = "-";
    // Simplified implementation
    return invoiceFullNo;
  }

  // Additional methods needed for create_discount.dart
  Future<String> createDiscountFormula(dynamic discount) async {
    // Placeholder implementation for discount formula creation
    return "formula_placeholder";
  }

  // Method for updating workspace settings
  Future<void> updateSettingDetails(dynamic settings) async {
    // Placeholder implementation for updating settings
    log('Updating settings: $settings');
  }
}
