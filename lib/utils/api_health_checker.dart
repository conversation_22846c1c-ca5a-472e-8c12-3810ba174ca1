import 'dart:developer';
import 'package:dio/dio.dart';
import '../core/network/api_endpoints.dart';

/// API Health Checker to diagnose server issues
class ApiHealthChecker {
  static final Dio _dio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    sendTimeout: const Duration(seconds: 10),
  ));

  /// Check if the API server is reachable
  static Future<ApiHealthStatus> checkServerHealth() async {
    try {
      log('🏥 Checking API server health...');

      // Basic connectivity test
      final response = await _dio.get(
        ApiEndpoints.baseUrl,
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 10),
          validateStatus: (status) => true, // Accept any status code
        ),
      );

      log('✅ Server responded with status: ${response.statusCode}');

      return ApiHealthStatus(
        isReachable: true,
        statusCode: response.statusCode,
        responseTime: DateTime.now().millisecondsSinceEpoch,
        message: 'Server is reachable',
        serverInfo: response.data?.toString(),
      );
    } on DioException catch (e) {
      log('❌ Server health check failed: ${e.message}');

      return ApiHealthStatus(
        isReachable: false,
        statusCode: e.response?.statusCode,
        responseTime: null,
        message: 'Server unreachable: ${e.message}',
        error: e.toString(),
      );
    } catch (e) {
      log('❌ Unexpected error during health check: $e');

      return ApiHealthStatus(
        isReachable: false,
        statusCode: null,
        responseTime: null,
        message: 'Unexpected error: $e',
        error: e.toString(),
      );
    }
  }

  /// Test specific endpoint
  static Future<EndpointTestResult> testEndpoint(
    String endpoint, {
    String method = 'GET',
    Map<String, dynamic>? data,
    Map<String, dynamic>? headers,
  }) async {
    try {
      log('🧪 Testing endpoint: $method ${ApiEndpoints.baseUrl}$endpoint');

      final stopwatch = Stopwatch()..start();

      Response response;
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _dio.get(
            '${ApiEndpoints.baseUrl}$endpoint',
            options: Options(
              headers: headers,
              validateStatus: (status) => true,
            ),
          );
          break;
        case 'POST':
          response = await _dio.post(
            '${ApiEndpoints.baseUrl}$endpoint',
            data: data,
            options: Options(
              headers: headers,
              validateStatus: (status) => true,
            ),
          );
          break;
        default:
          throw UnsupportedError('Method $method not supported');
      }

      stopwatch.stop();

      return EndpointTestResult(
        endpoint: endpoint,
        method: method,
        statusCode: response.statusCode,
        responseTime: stopwatch.elapsedMilliseconds,
        success: response.statusCode! >= 200 && response.statusCode! < 300,
        responseData: response.data?.toString(),
        headers: response.headers.map,
      );
    } on DioException catch (e) {
      return EndpointTestResult(
        endpoint: endpoint,
        method: method,
        statusCode: e.response?.statusCode,
        responseTime: null,
        success: false,
        error: e.toString(),
        responseData: e.response?.data?.toString(),
      );
    }
  }

  /// Run comprehensive API diagnostics
  static Future<ApiDiagnostics> runDiagnostics() async {
    log('🔍 Running comprehensive API diagnostics...');

    final diagnostics = ApiDiagnostics();

    // 1. Server health check
    diagnostics.serverHealth = await checkServerHealth();

    // 2. Test common endpoints
    final endpointsToTest = [
      '/auth/login',
      '/products',
      '/sales',
      '/workspaces',
    ];

    for (String endpoint in endpointsToTest) {
      final result = await testEndpoint(endpoint);
      diagnostics.endpointTests.add(result);
    }

    // 3. Network connectivity test
    diagnostics.networkConnectivity = await _testNetworkConnectivity();

    log('🔍 Diagnostics completed');
    return diagnostics;
  }

  static Future<bool> _testNetworkConnectivity() async {
    try {
      final response = await _dio.get(
        'https://httpbin.org/status/200',
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Print diagnostic report
  static void printDiagnosticReport(ApiDiagnostics diagnostics) {
    log('📊 API DIAGNOSTIC REPORT 📊');
    log('Timestamp: ${DateTime.now()}');
    log('');

    // Server Health
    log('🏥 SERVER HEALTH:');
    log('  Reachable: ${diagnostics.serverHealth.isReachable}');
    log('  Status Code: ${diagnostics.serverHealth.statusCode}');
    log('  Message: ${diagnostics.serverHealth.message}');
    if (diagnostics.serverHealth.error != null) {
      log('  Error: ${diagnostics.serverHealth.error}');
    }
    log('');

    // Endpoint Tests
    log('🧪 ENDPOINT TESTS:');
    for (var test in diagnostics.endpointTests) {
      log('  ${test.method} ${test.endpoint}:');
      log('    Status: ${test.statusCode}');
      log('    Success: ${test.success}');
      log('    Response Time: ${test.responseTime}ms');
      if (test.error != null) {
        log('    Error: ${test.error}');
      }
    }
    log('');

    // Network Connectivity
    log('🌐 NETWORK CONNECTIVITY: ${diagnostics.networkConnectivity ? "✅ Good" : "❌ Poor"}');
    log('📊 END DIAGNOSTIC REPORT 📊');
  }
}

class ApiHealthStatus {
  final bool isReachable;
  final int? statusCode;
  final int? responseTime;
  final String message;
  final String? error;
  final String? serverInfo;

  ApiHealthStatus({
    required this.isReachable,
    this.statusCode,
    this.responseTime,
    required this.message,
    this.error,
    this.serverInfo,
  });
}

class EndpointTestResult {
  final String endpoint;
  final String method;
  final int? statusCode;
  final int? responseTime;
  final bool success;
  final String? error;
  final String? responseData;
  final Map<String, List<String>>? headers;

  EndpointTestResult({
    required this.endpoint,
    required this.method,
    this.statusCode,
    this.responseTime,
    required this.success,
    this.error,
    this.responseData,
    this.headers,
  });
}

class ApiDiagnostics {
  late ApiHealthStatus serverHealth;
  List<EndpointTestResult> endpointTests = [];
  late bool networkConnectivity;
}
