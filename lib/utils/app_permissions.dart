import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class AppPermissions {
  /// Check and request all necessary permissions
  static Future<Map<Permission, PermissionStatus>> requestAllPermissions() async {
    final permissions = await [
      Permission.camera,
      Permission.storage,
      Permission.notification,
    ].request();

    return permissions;
  }

  /// Check camera permission
  static Future<PermissionStatus> checkCameraPermission() async {
    final status = await Permission.camera.status;
    return status;
  }

  /// Request camera permission
  static Future<PermissionStatus> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status;
  }

  /// Check storage permission
  static Future<PermissionStatus> checkStoragePermission() async {
    if (Platform.isAndroid && await isAndroid13OrHigher()) {
      // Android 13+ uses photos/videos/audio permissions
      return await Permission.photos.status;
    }
    return await Permission.storage.status;
  }

  /// Request storage permission
  static Future<PermissionStatus> requestStoragePermission() async {
    if (Platform.isAndroid && await isAndroid13OrHigher()) {
      return await Permission.photos.request();
    }
    return await Permission.storage.request();
  }

  /// Check notification permission
  static Future<PermissionStatus> checkNotificationPermission() async {
    if (Platform.isAndroid) {
      // Notification permission only needed for Android 13+
      if (await isAndroid13OrHigher()) {
        return await Permission.notification.status;
      }
      return PermissionStatus.granted;
    }
    // iOS doesn't need explicit notification permission request
    return PermissionStatus.granted;
  }

  /// Request notification permission
  static Future<PermissionStatus> requestNotificationPermission() async {
    if (Platform.isAndroid && await isAndroid13OrHigher()) {
      return await Permission.notification.request();
    }
    return PermissionStatus.granted;
  }

  /// Check if Android version is 13 or higher
  static Future<bool> isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;
      return sdkVersion >= 33; // Android 13 is SDK 33
    }
    return false;
  }

  /// Open app settings if permissions are permanently denied
  static Future<void> openAppSettingsIfDenied() async {
    final cameraStatus = await checkCameraPermission();
    final storageStatus = await checkStoragePermission();
    final notificationStatus = await checkNotificationPermission();

    if (cameraStatus.isPermanentlyDenied ||
        storageStatus.isPermanentlyDenied ||
        (Platform.isAndroid && await isAndroid13OrHigher() && notificationStatus.isPermanentlyDenied)) {
      await openAppSettings();
    }
  }
}
