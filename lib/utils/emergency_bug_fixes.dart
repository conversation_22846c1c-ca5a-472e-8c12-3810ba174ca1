import 'dart:developer';
import '../database/app_database.dart';
import '../database/schema_fix.dart';

/// Emergency bug fixes for immediate resolution
class EmergencyBugFixes {
  
  /// Apply all emergency fixes
  static Future<FixResult> applyAllEmergencyFixes() async {
    final results = <String, bool>{};
    final messages = <String>[];
    
    log('🚨 Applying emergency bug fixes...');
    
    // Fix 1: Database schema issues
    try {
      await _fixDatabaseSchema();
      results['database_schema'] = true;
      messages.add('✅ Database schema fixed');
    } catch (e) {
      results['database_schema'] = false;
      messages.add('❌ Database schema fix failed: $e');
    }
    
    // Fix 2: Missing table columns
    try {
      await _fixMissingColumns();
      results['missing_columns'] = true;
      messages.add('✅ Missing columns added');
    } catch (e) {
      results['missing_columns'] = false;
      messages.add('❌ Missing columns fix failed: $e');
    }
    
    // Fix 3: Database connection issues
    try {
      await _fixDatabaseConnection();
      results['database_connection'] = true;
      messages.add('✅ Database connection verified');
    } catch (e) {
      results['database_connection'] = false;
      messages.add('❌ Database connection fix failed: $e');
    }
    
    // Fix 4: Discount table issues
    try {
      await _fixDiscountTable();
      results['discount_table'] = true;
      messages.add('✅ Discount table fixed');
    } catch (e) {
      results['discount_table'] = false;
      messages.add('❌ Discount table fix failed: $e');
    }
    
    final successCount = results.values.where((success) => success).length;
    final totalCount = results.length;
    
    log('🚨 Emergency fixes completed: $successCount/$totalCount successful');
    
    return FixResult(
      success: successCount == totalCount,
      successCount: successCount,
      totalCount: totalCount,
      messages: messages,
      details: results,
    );
  }
  
  /// Fix database schema issues
  static Future<void> _fixDatabaseSchema() async {
    log('🔧 Fixing database schema...');
    
    final database = await AppDatabase().database;
    await SchemaFix.runAllSchemaFixes(database);
    
    log('✅ Database schema fix completed');
  }
  
  /// Fix missing columns
  static Future<void> _fixMissingColumns() async {
    log('🔧 Fixing missing columns...');
    
    final database = await AppDatabase().database;
    
    // Add missing discount columns
    final discountColumns = [
      'ALTER TABLE Discount ADD COLUMN discountCouponName TEXT',
      'ALTER TABLE Discount ADD COLUMN couponCode TEXT',
      'ALTER TABLE Discount ADD COLUMN discountPriority INTEGER DEFAULT 0',
      'ALTER TABLE Discount ADD COLUMN discountSync INTEGER DEFAULT 0',
    ];
    
    for (final sql in discountColumns) {
      try {
        await database.execute(sql);
        log('✅ Added column: $sql');
      } catch (e) {
        // Column might already exist, which is fine
        log('ℹ️ Column already exists or error: $e');
      }
    }
    
    // Add missing product columns
    final productColumns = [
      'ALTER TABLE Products ADD COLUMN cgst TEXT DEFAULT "0"',
      'ALTER TABLE Products ADD COLUMN sgst TEXT DEFAULT "0"',
      'ALTER TABLE Products ADD COLUMN igst TEXT DEFAULT "0"',
    ];
    
    for (final sql in productColumns) {
      try {
        await database.execute(sql);
        log('✅ Added product column: $sql');
      } catch (e) {
        log('ℹ️ Product column already exists or error: $e');
      }
    }
    
    log('✅ Missing columns fix completed');
  }
  
  /// Fix database connection issues
  static Future<void> _fixDatabaseConnection() async {
    log('🔧 Testing database connection...');
    
    final database = await AppDatabase().database;
    
    // Test basic operations
    await database.rawQuery('SELECT 1');
    await database.rawQuery('SELECT name FROM sqlite_master WHERE type="table"');
    
    log('✅ Database connection verified');
  }
  
  /// Fix discount table specific issues
  static Future<void> _fixDiscountTable() async {
    log('🔧 Fixing discount table...');
    
    final database = await AppDatabase().database;
    
    // Check if Discount table exists
    final tables = await database.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='Discount'"
    );
    
    if (tables.isEmpty) {
      // Create Discount table if it doesn't exist
      await database.execute('''
        CREATE TABLE IF NOT EXISTS Discount (
          discountID TEXT PRIMARY KEY,
          discountCouponName TEXT,
          couponCode TEXT,
          discountType TEXT,
          discountMode TEXT,
          discountOn TEXT,
          discount TEXT,
          minDiscount TEXT,
          maxDiscount TEXT,
          fromDate TEXT,
          toDate TEXT,
          formula TEXT,
          discountActive INTEGER DEFAULT 1,
          discountSync INTEGER DEFAULT 0,
          discountPriority INTEGER DEFAULT 0,
          workspaceID TEXT,
          categoryID TEXT,
          createdBy TEXT,
          createdDate TEXT,
          updatedDate TEXT,
          rowStatus INTEGER DEFAULT 0,
          type TEXT,
          mode TEXT
        )
      ''');
      log('✅ Created Discount table');
    }
    
    // Create indexes for better performance
    final indexes = [
      'CREATE INDEX IF NOT EXISTS idx_discount_active ON Discount(discountActive)',
      'CREATE INDEX IF NOT EXISTS idx_discount_coupon ON Discount(discountCouponName)',
      'CREATE INDEX IF NOT EXISTS idx_discount_workspace ON Discount(workspaceID)',
    ];
    
    for (final indexSql in indexes) {
      try {
        await database.execute(indexSql);
        log('✅ Created index');
      } catch (e) {
        log('ℹ️ Index creation error (might already exist): $e');
      }
    }
    
    log('✅ Discount table fix completed');
  }
  
  /// Quick fix for immediate issues
  static Future<FixResult> quickFix() async {
    log('⚡ Applying quick fixes...');
    
    try {
      // Just fix the most critical issues
      await _fixDatabaseConnection();
      await _fixMissingColumns();
      
      return FixResult(
        success: true,
        successCount: 2,
        totalCount: 2,
        messages: ['✅ Quick fix completed successfully'],
        details: {'quick_fix': true},
      );
    } catch (e) {
      return FixResult(
        success: false,
        successCount: 0,
        totalCount: 1,
        messages: ['❌ Quick fix failed: $e'],
        details: {'quick_fix': false},
      );
    }
  }
  
  /// Reset database (nuclear option)
  static Future<FixResult> resetDatabase() async {
    log('💥 Resetting database (nuclear option)...');
    
    try {
      // This would delete and recreate the entire database
      // Implementation depends on your specific needs
      
      log('⚠️ Database reset not implemented - this is a placeholder');
      
      return FixResult(
        success: false,
        successCount: 0,
        totalCount: 1,
        messages: ['⚠️ Database reset not implemented yet'],
        details: {'reset': false},
      );
    } catch (e) {
      return FixResult(
        success: false,
        successCount: 0,
        totalCount: 1,
        messages: ['❌ Database reset failed: $e'],
        details: {'reset': false},
      );
    }
  }
  
  /// Check system health after fixes
  static Future<HealthStatus> checkSystemHealth() async {
    log('🏥 Checking system health...');
    
    final issues = <String>[];
    
    try {
      // Test database
      final database = await AppDatabase().database;
      await database.rawQuery('SELECT 1');
      
      // Test critical tables
      final tables = ['Discount', 'Products', 'SalesTransaction'];
      for (final table in tables) {
        try {
          await database.rawQuery('SELECT COUNT(*) FROM $table LIMIT 1');
        } catch (e) {
          issues.add('Table $table is not accessible: $e');
        }
      }
      
      // Test discount functionality
      try {
        final enhancedDiscountDao = await AppDatabase().enhancedDiscountDao;
        await enhancedDiscountDao.getDiscountsWithPagination(limit: 1);
      } catch (e) {
        issues.add('Discount functionality issue: $e');
      }
      
    } catch (e) {
      issues.add('Database connection issue: $e');
    }
    
    final isHealthy = issues.isEmpty;
    
    log('🏥 System health check completed: ${isHealthy ? "HEALTHY" : "ISSUES FOUND"}');
    
    return HealthStatus(
      isHealthy: isHealthy,
      issues: issues,
      checkedAt: DateTime.now(),
    );
  }
}

/// Fix result data class
class FixResult {
  final bool success;
  final int successCount;
  final int totalCount;
  final List<String> messages;
  final Map<String, bool> details;
  
  FixResult({
    required this.success,
    required this.successCount,
    required this.totalCount,
    required this.messages,
    required this.details,
  });
  
  String get summary {
    return success 
      ? '✅ All fixes applied successfully ($successCount/$totalCount)'
      : '⚠️ Some fixes failed ($successCount/$totalCount)';
  }
}

/// Health status data class
class HealthStatus {
  final bool isHealthy;
  final List<String> issues;
  final DateTime checkedAt;
  
  HealthStatus({
    required this.isHealthy,
    required this.issues,
    required this.checkedAt,
  });
  
  String get summary {
    return isHealthy 
      ? '✅ System is healthy'
      : '⚠️ ${issues.length} issues found';
  }
}
