import 'dart:developer';

import 'package:coffee_cofe/features/inventory/inventory_screen_provider.dart';
import 'package:coffee_cofe/features/report/sales_report/report_provider.dart';
import 'package:coffee_cofe/features/report/enhanced_sales_report_provider.dart';
import 'package:coffee_cofe/features/settings/settings_provider.dart';
import 'package:device_preview_plus/device_preview_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:path/path.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'core/constants/theme.dart';
import 'core/routes/app_pages.dart';
import 'core/routes/app_routes.dart';
import 'features/attendance/attendance_provider.dart';
import 'features/auth/login_provider.dart';
import 'features/auth/signup/signup_provider.dart';
import 'features/bill/billing_screen_provider.dart';
import 'features/employee/create_employee/create_employee_provider.dart';
import 'features/product/product_list/product_list_provider.dart';
import 'features/product/product_provider.dart';
import 'features/report/product_report/product_report_provider.dart';
import 'providers/auth_provider.dart';
import 'utils/locator.dart';
import 'utils/runtime_issue_fixes.dart';

getPath() async {
  await Permission.manageExternalStorage.request();

  final dbPath = await getDatabasesPath();
  final fullPath = join(dbPath, 'coffee_pos.db');
  log("sqldb path : $fullPath"); // Prints full path to the database
}

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize widget lifecycle error handler
  WidgetLifecycleErrorHandler.initialize();

  sqfliteFfiInit();
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.transparent,
    ),
  );
  // getPath();
  // runZonedGuarded(() {
  //   FlutterError.onError = (FlutterErrorDetails details) {
  //     FlutterError.presentError(details); // Show error in debug
  //     logError(details.exception, details.stack);
  //   };

  runApp(
    DevicePreview(
      enabled: false,
      tools: const [...DevicePreview.defaultTools],
      builder: (BuildContext context) => const MyApp(),
    ),
  );
  // }, (Object error, StackTrace stack) {
  //   logError(error, stack);
  // });
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});
  static const String shopId = "development_shop_id";
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    rootInitialization();
  }

  rootInitialization() async {
    await setupLocator();
  }

  @override
  Widget build(BuildContext context) {
    // ErrorWidget.builder = (FlutterErrorDetails details) {
    //   return Center(child: Text('⚠️ Something went wrong\n  $details'));
    // };

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LoginProvider()),
        ChangeNotifierProvider(create: (context) => SignupProvider()),
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => ProductProvider()),
        ChangeNotifierProvider(create: (context) => SettingsProvider()),
        ChangeNotifierProvider(create: (context) => ProductListProvider()),
        ChangeNotifierProvider(create: (context) => BillingScreenProvider()),
        ChangeNotifierProvider(create: (context) => InventoryScreenProvider()),
        ChangeNotifierProvider(create: (context) => SalesReportProvider()),
        ChangeNotifierProvider(create: (context) => EnhancedSalesReportProvider()),
        ChangeNotifierProvider(create: (context) => AttendanceProvider()),
        ChangeNotifierProvider(create: (context) => EmployeeProvider()),
        ChangeNotifierProvider(create: (context) => ProductReportProvider()),
      ],
      child: GetMaterialApp(
        title: 'Coffee POS',

        debugShowCheckedModeBanner: false,
        useInheritedMediaQuery: true,
        locale: DevicePreview.locale(context),
        builder: DevicePreview.appBuilder,
        theme: AppTheme.appTheme,
        initialRoute: AppRoutes.splash,
        getPages: AppPages.routes,
        // home: SimpleCalculator(),
      ),
    );
  }
}
