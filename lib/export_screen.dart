import 'dart:io';

import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:permission_handler/permission_handler.dart';

import 'database/app_database.dart';

class ExportDbScreen extends StatefulWidget {
  const ExportDbScreen({super.key});

  @override
  State<ExportDbScreen> createState() => _ExportDbScreenState();
}

class _ExportDbScreenState extends State<ExportDbScreen> {
  Database? _db;

  @override
  void initState() {
    super.initState();
    initFun();
  }

  initFun() async {
    _db = await AppDatabase().database;
  }

  Future<void> exportDatabaseFile() async {
    final status = await Permission.manageExternalStorage.request();

    if (status.isGranted) {
      final dbPath = await getDatabasesPath();
      final dbFile = File('$dbPath/coffee_pos.db');

      final externalDir = Directory('/storage/emulated/0/Download');
      final exportFile = File('${externalDir.path}/exported_app.db');

      await dbFile.copy(exportFile.path);

      print('Exported to: ${exportFile.path}');
    } else {
      print('Permission denied');
      openAppSettings(); // Optional
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('SQLite Export Example')),
      body: Center(
        child: ElevatedButton(
          onPressed: exportDatabaseFile,
          child: const Text('Export Schema & Data'),
        ),
      ),
    );
  }
}
