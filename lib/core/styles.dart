import 'package:flutter/material.dart';

import 'constants/app_colors.dart';

TextStyle labelStyle =
    const TextStyle(color: AppColors.textPrimary, fontSize: 14, fontWeight: FontWeight.w400);
TextStyle hitStyle =
    const TextStyle(color: AppColors.textTertiary, fontSize: 14, fontWeight: FontWeight.w400);
TextStyle black15w500 =
    const TextStyle(color: AppColors.textPrimary, fontSize: 15, fontWeight: FontWeight.w500);
TextStyle black16w500 =
    const TextStyle(color: AppColors.textPrimary, fontSize: 16, fontWeight: FontWeight.w500);
TextStyle gray15w500 =
    const TextStyle(color: AppColors.textTertiary, fontSize: 15, fontWeight: FontWeight.w500);
TextStyle black18w700 =
    const TextStyle(color: AppColors.textPrimary, fontSize: 20, fontWeight: FontWeight.bold);
TextStyle white22w500 =
    const TextStyle(color: AppColors.buttonText, fontSize: 22, fontWeight: FontWeight.w500);
TextStyle white16w500 =
    const TextStyle(color: AppColors.buttonText, fontSize: 16, fontWeight: FontWeight.w500);
TextStyle black22w500 =
    const TextStyle(color: AppColors.textPrimary, fontSize: 22, fontWeight: FontWeight.w500);
TextStyle green15w500 = const TextStyle(color: AppColors.primary, fontSize: 15, fontWeight: FontWeight.w500);
