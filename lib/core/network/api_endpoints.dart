/// API endpoints configuration
class ApiEndpoints {
  // Base URL - Update this with your actual API base URL
  static const String baseUrl = 'http://13.204.85.80:5000';

  // Authentication endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String refreshToken = '/auth/refresh';
  static const String logout = '/auth/logout';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String verifyOtp = '/auth/verify-otp';

  // User endpoints
  static const String profile = '/user/profile';
  static const String updateProfile = '/user/profile';
  static const String changePassword = '/user/change-password';

  // Product endpoints
  static const String products = '/products';
  static const String categories = '/categories';
  static const String inventory = '/inventory';

  // Sales endpoints
  static const String sales = '/sales';
  static const String salesReport = '/sales/report';

  // Employee endpoints
  static const String employees = '/employees';
  static const String attendance = '/attendance';

  // Discount endpoints
  static const String discounts = '/discounts';
  static const String coupons = '/coupons';

  // Workspace endpoints
  static const String workspaces = '/workspaces';
  static const String workspaceSettings = '/workspaces/settings';

  // Sync endpoints
  static const String syncData = '/sync';
  static const String syncStatus = '/sync/status';

  // Helper method to build full URL
  static String buildUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
}
