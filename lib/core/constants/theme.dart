import 'package:flutter/material.dart';
import 'package:pie_menu/pie_menu.dart';
import 'app_colors.dart'; // Ensure this file is correctly imported

class AppTheme {
  // Dark/Custom Theme
  static ThemeData appTheme = ThemeData(
    primaryColor: AppColors.primary,
    scaffoldBackgroundColor: Colors.white,
    textTheme: const TextTheme(
      displayLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: AppColors.textPrimary),
      titleLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppColors.textPrimary),
      bodyLarge: TextStyle(fontSize: 14, color: AppColors.textSecondary),
      bodyMedium: TextStyle(fontSize: 12, color: AppColors.textTertiary),
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColors.fabBackground,
      foregroundColor: AppColors.fabForeground,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.primary,
      titleTextStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppColors.appBarText),
    ),
    buttonTheme: const ButtonThemeData(
      buttonColor: AppColors.buttonPrimary,
      textTheme: ButtonTextTheme.primary,
    ),
    cardColor: AppColors.surface,
    colorScheme: AppColors.colorScheme,
  );
  static const PieTheme defaultPieTheme = PieTheme(
    brightness: Brightness.dark,
    buttonTheme: PieButtonTheme(
      backgroundColor: Colors.blue,
      iconColor: Colors.white,
    ),
    buttonThemeHovered: PieButtonTheme(
      backgroundColor: Colors.green,
      iconColor: Colors.white,
    ),
    iconSize: 28.0,
    radius: 96.0,
    spacing: 6.0,
    angleOffset: 0.0,
    customAngleAnchor: PieAnchor.center,
    menuDisplacement: Offset.zero,
    buttonSize: 56.0,
    pointerSize: 40.0,
    pieBounceDuration: Duration(seconds: 1),
    childBounceEnabled: true,
    childTiltEnabled: true,
    childBounceDuration: Duration(milliseconds: 150),
    childBounceFactor: 0.95,
    childBounceCurve: Curves.easeOutCubic,
    childBounceReverseCurve: Curves.easeInCubic,
    fadeDuration: Duration(milliseconds: 250),
    hoverDuration: Duration(milliseconds: 250),
    longPressDuration: Duration(milliseconds: 350),
    regularPressShowsMenu: false,
    longPressShowsMenu: true,
    leftClickShowsMenu: true,
    rightClickShowsMenu: false,
    overlayStyle: PieOverlayStyle.behind,
    childOpacityOnButtonHover: 0.5,
  );
}
