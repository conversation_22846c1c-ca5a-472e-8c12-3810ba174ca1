import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors Color(0xFF4CAF50)
  static const Color primary = Color(0xFF4CAF50); // Green
  static const Color primaryVariant = Color(0xFF388E3C); // Darker Green

  // Secondary Colors
  static const Color secondary = Colors.orange; // Amber
  static const Color secondaryVariant = Color(0xFFFFA000); // Darker Amber

  // Background & Surface Colors
  static const Color background = Color(0xFFF1F8E9); // Light Green Tint
  static const Color surface = Colors.white; // Default Card/Modal Background

  // Text Colors
  static const Color textPrimary = Colors.black87; // Main text color
  static const Color textSecondary = Colors.black54; // Secondary text color
  static const Color textTertiary = Color.fromARGB(255, 184, 181, 181);
  // Error & Warning Colors
  static const Color error = Colors.redAccent;
  static const Color warning = Color(0xFFFF9800); // Orange Warning

  // Button Colors
  static const Color buttonPrimary = Color(0xFFFFC107); // Amber
  static const Color buttonText = Colors.white;

  // AppBar Colors
  static const Color appBarBackground = Color(0xFFF1F8E9);
  static const Color appBarText = Colors.white;

  // Floating Action Button Colors
  static const Color fabBackground = Color(0xFFFF9800);
  static const Color fabForeground = Colors.white;

  // Divider & Border Colors
  static const Color divider = Colors.black26;
  static const Color border = Colors.black12;

  // Define the color scheme
  static const ColorScheme colorScheme = ColorScheme(
    primary: primary,
    secondary: secondary,
    surface: surface,
    error: error,
    onPrimary: Colors.white,
    onSecondary: Colors.black,
    onSurface: Colors.black,
    onError: Colors.white,
    brightness: Brightness.light,
  );
}
