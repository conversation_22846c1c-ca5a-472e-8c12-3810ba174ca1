import 'dart:developer';

/// Application configuration settings
class AppConfig {
  // API Configuration
  static const bool useMockAuth = true; // Set to false for production API
  static const bool enableDebugMode = true;
  static const bool showMockUserList = true; // Show mock users in debug mode

  // Network Configuration
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;

  // Mock Service Configuration
  static const Duration mockNetworkDelay = Duration(seconds: 1);
  static const bool simulateNetworkErrors = false; // For testing error handling

  // App Information
  static const String appName = 'Coffee POS';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';

  // Environment
  static const String environment = 'development'; // development, staging, production

  /// Check if app is in development mode
  static bool get isDevelopment => environment == 'development';

  /// Check if app is in production mode
  static bool get isProduction => environment == 'production';

  /// Get appropriate base URL based on environment
  static String get baseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.coffeepos.com/v1';
      case 'staging':
        return 'https://staging-api.coffeepos.com/v1';
      case 'development':
      default:
        return 'https://dev-api.coffeepos.com/v1';
    }
  }

  /// Print current configuration (for debugging)
  static void printConfig() {
    if (enableDebugMode) {
      log('=== App Configuration ===');
      log('Environment: $environment');
      log('Base URL: $baseUrl');
      log('Use Mock Auth: $useMockAuth');
      log('Debug Mode: $enableDebugMode');
      log('App Version: $appVersion');
      log('========================');
    }
  }
}
