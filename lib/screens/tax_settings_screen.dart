import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../features/settings/settings_provider.dart';

class TaxSettingsScreen extends StatefulWidget {
  const TaxSettingsScreen({super.key});

  @override
  State<TaxSettingsScreen> createState() => _TaxSettingsScreenState();
}

class _TaxSettingsScreenState extends State<TaxSettingsScreen> {
  final TextEditingController _gstController = TextEditingController();
  bool _enableTax = true;
  String _modeOfTax = 'Exclude Tax';
  String _applyTaxFor = 'All Products';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    final settings = Provider.of<SettingsProvider>(context, listen: false);
    setState(() {
      _enableTax = settings.isTaxEnabled;
      _modeOfTax = settings.excludeTax ? 'Exclude Tax' : 'Include Tax';
      _applyTaxFor = settings.applyTaxAllProducts ? 'All Products' : 'Specific Products';
      _gstController.text = '18'; // Default GST rate
    });
  }

  void _updateSettings() {
    final settings = Provider.of<SettingsProvider>(context, listen: false);

    settings.toggleTax(_enableTax);
    if (_modeOfTax == 'Include Tax') {
      settings.updateSetting('includeTax', true);
      settings.updateSetting('excludeTax', false);
    } else {
      settings.updateSetting('includeTax', false);
      settings.updateSetting('excludeTax', true);
    }

    if (_applyTaxFor == 'All Products') {
      settings.updateSetting('applyTaxAllProducts', true);
      settings.updateSetting('applyTaxSpecificProduct', false);
    } else {
      settings.updateSetting('applyTaxAllProducts', false);
      settings.updateSetting('applyTaxSpecificProduct', true);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tax settings updated successfully'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: const Color(0xFF6A1B9A),
        foregroundColor: Colors.white,
        title: const Text(
          'Tax Settings',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enable Tax Toggle
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Enable Tax',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  Switch(
                    value: _enableTax,
                    onChanged: (value) {
                      setState(() {
                        _enableTax = value;
                      });
                    },
                    activeColor: const Color(0xFF6A1B9A),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Mode of Tax Section
            const Text(
              'Mode of Tax',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),

            // Include Tax Radio
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Radio<String>(
                    value: 'Include Tax',
                    groupValue: _modeOfTax,
                    onChanged: (value) {
                      setState(() {
                        _modeOfTax = value!;
                      });
                    },
                    activeColor: const Color(0xFF6A1B9A),
                  ),
                  const Text(
                    'Include Tax',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // Exclude Tax Radio
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: Row(
                children: [
                  Radio<String>(
                    value: 'Exclude Tax',
                    groupValue: _modeOfTax,
                    onChanged: (value) {
                      setState(() {
                        _modeOfTax = value!;
                      });
                    },
                    activeColor: const Color(0xFF6A1B9A),
                  ),
                  const Text(
                    'Exclude Tax',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Apply Tax For Section
            const Text(
              'Apply Tax For',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),

            // All Products Radio
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Radio<String>(
                    value: 'All Products',
                    groupValue: _applyTaxFor,
                    onChanged: (value) {
                      setState(() {
                        _applyTaxFor = value!;
                      });
                    },
                    activeColor: const Color(0xFF6A1B9A),
                  ),
                  const Text(
                    'All Products',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // Specific Products Radio
            Container(
              margin: const EdgeInsets.only(bottom: 24),
              child: Row(
                children: [
                  Radio<String>(
                    value: 'Specific Products',
                    groupValue: _applyTaxFor,
                    onChanged: (value) {
                      setState(() {
                        _applyTaxFor = value!;
                      });
                    },
                    activeColor: const Color(0xFF6A1B9A),
                  ),
                  const Text(
                    'Specific Products',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // GST Percentage Input
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'GST (%)',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _gstController,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      hintText: '18',
                      hintStyle: TextStyle(color: Colors.grey),
                    ),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Update Button
            Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: const LinearGradient(
                  colors: [Color(0xFF6A1B9A), Color(0xFF8E24AA)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: ElevatedButton(
                onPressed: _updateSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Update',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _gstController.dispose();
    super.dispose();
  }
}
