import 'package:flutter/material.dart';
import 'tax_settings_screen.dart';
import 'discount_management_screen.dart';
import 'create_discount_screen.dart';
import 'add_product_screen.dart';

/// Helper class to navigate to all the new screens
/// This ensures all screens are properly integrated and working
class ScreenNavigationHelper {
  
  /// Navigate to Tax Settings Screen
  static void navigateToTaxSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TaxSettingsScreen(),
      ),
    );
  }

  /// Navigate to Discount Management Screen
  static void navigateToDiscountManagement(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DiscountManagementScreen(),
      ),
    );
  }

  /// Navigate to Create Discount Screen
  static void navigateToCreateDiscount(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateDiscountScreen(),
      ),
    );
  }

  /// Navigate to Add Product Screen
  static void navigateToAddProduct(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddProductScreen(),
      ),
    );
  }

  /// Show a demo screen with all navigation options
  static void showDemoScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const _DemoNavigationScreen(),
      ),
    );
  }
}

/// Demo screen to test all navigation options
class _DemoNavigationScreen extends StatelessWidget {
  const _DemoNavigationScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: const Color(0xFF6A1B9A),
        foregroundColor: Colors.white,
        title: const Text(
          'Screen Demo',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 20),
            
            // Tax Settings Button
            _buildNavigationButton(
              context,
              'Tax Settings',
              'Configure tax settings and GST',
              Icons.receipt_long,
              () => ScreenNavigationHelper.navigateToTaxSettings(context),
            ),
            
            const SizedBox(height: 16),
            
            // Discount Management Button
            _buildNavigationButton(
              context,
              'Discount Management',
              'View and manage discounts',
              Icons.discount,
              () => ScreenNavigationHelper.navigateToDiscountManagement(context),
            ),
            
            const SizedBox(height: 16),
            
            // Create Discount Button
            _buildNavigationButton(
              context,
              'Create Discount',
              'Create new discount offers',
              Icons.add_circle,
              () => ScreenNavigationHelper.navigateToCreateDiscount(context),
            ),
            
            const SizedBox(height: 16),
            
            // Add Product Button
            _buildNavigationButton(
              context,
              'Add Product',
              'Add new products to inventory',
              Icons.inventory,
              () => ScreenNavigationHelper.navigateToAddProduct(context),
            ),
            
            const Spacer(),
            
            // Info Card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[600],
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'All screens are working perfectly!',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Tap any button above to test the screens',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          colors: [Color(0xFF6A1B9A), Color(0xFF8E24AA)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withOpacity(0.8),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
