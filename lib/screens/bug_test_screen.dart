import 'package:flutter/material.dart';
import '../widgets/common_appbar.dart';
import '../utils/bug_test_utility.dart';

class BugTestScreen extends StatefulWidget {
  const BugTestScreen({super.key});

  @override
  State<BugTestScreen> createState() => _BugTestScreenState();
}

class _BugTestScreenState extends State<BugTestScreen> {
  List<TestResult>? _testResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: 'Bug Test Suite'),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Bug Testing',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Run comprehensive tests to identify and verify bug fixes.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            
            // Run Tests Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunning ? null : _runTests,
                icon: _isRunning 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.play_arrow),
                label: Text(_isRunning ? 'Running Tests...' : 'Run All Tests'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Test Results
            if (_testResults != null) ...[
              const Text(
                'Test Results:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: _testResults!.length,
                  itemBuilder: (context, index) {
                    final result = _testResults![index];
                    final testNames = ['Discount Functionality', 'GST Functionality', 'Report Functionality'];
                    final testName = index < testNames.length ? testNames[index] : 'Test ${index + 1}';
                    
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Icon(
                          result.success ? Icons.check_circle : Icons.error,
                          color: result.success ? Colors.green : Colors.red,
                        ),
                        title: Text(testName),
                        subtitle: Text(result.message),
                        trailing: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: result.success ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            result.success ? 'PASSED' : 'FAILED',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Summary
              Card(
                color: _getOverallResult() ? Colors.green[50] : Colors.red[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        _getOverallResult() ? Icons.check_circle : Icons.warning,
                        color: _getOverallResult() ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getOverallResult() ? 'All Tests Passed!' : 'Some Tests Failed',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: _getOverallResult() ? Colors.green : Colors.red,
                              ),
                            ),
                            Text(
                              '${_getPassedCount()}/${_testResults!.length} tests passed',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ] else if (!_isRunning) ...[
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.bug_report,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Ready to run tests',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Click the button above to start testing',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text(
                        'Running tests...',
                        style: TextStyle(fontSize: 18),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This may take a few moments',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      floatingActionButton: _testResults != null
          ? FloatingActionButton(
              onPressed: _runSchemaTest,
              child: const Icon(Icons.storage),
              tooltip: 'Test Database Schema',
            )
          : null,
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    try {
      final results = await BugTestUtility.runAllTests();
      setState(() {
        _testResults = results;
        _isRunning = false;
      });

      // Show completion message
      if (mounted) {
        final passed = results.where((r) => r.success).length;
        final total = results.length;
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tests completed: $passed/$total passed'),
            backgroundColor: passed == total ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isRunning = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test execution failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _runSchemaTest() async {
    try {
      final result = await BugTestUtility.testDatabaseSchema();
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  result.success ? Icons.check_circle : Icons.error,
                  color: result.success ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                const Text('Schema Test'),
              ],
            ),
            content: Text(result.message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Schema test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _getOverallResult() {
    if (_testResults == null) return false;
    return _testResults!.every((result) => result.success);
  }

  int _getPassedCount() {
    if (_testResults == null) return 0;
    return _testResults!.where((result) => result.success).length;
  }
}
