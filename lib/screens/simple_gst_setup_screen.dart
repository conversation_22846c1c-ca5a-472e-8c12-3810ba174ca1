import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/common_appbar.dart';
import '../features/settings/settings_provider.dart';

class SimpleGSTSetupScreen extends StatefulWidget {
  const SimpleGSTSetupScreen({super.key});

  @override
  State<SimpleGSTSetupScreen> createState() => _SimpleGSTSetupScreenState();
}

class _SimpleGSTSetupScreenState extends State<SimpleGSTSetupScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: 'GST Setup'),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Consumer<SettingsProvider>(
          builder: (context, settings, child) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'GST Configuration',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Configure your GST settings to enable tax calculations in your POS system.',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 32),
                
                // Enable Tax Toggle
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Enable GST/Tax',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Switch(
                              value: settings.isTaxEnabled,
                              onChanged: (value) {
                                settings.toggleTax(value);
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          settings.isTaxEnabled 
                            ? 'GST is enabled. Tax will be calculated on sales.'
                            : 'GST is disabled. No tax will be calculated.',
                          style: TextStyle(
                            color: settings.isTaxEnabled ? Colors.green : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                if (settings.isTaxEnabled) ...[
                  const SizedBox(height: 16),
                  
                  // Tax Application Settings
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Apply Tax To:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // All Products Option
                          CheckboxListTile(
                            title: const Text('All Products'),
                            subtitle: const Text('Apply default 18% GST to all products'),
                            value: settings.applyTaxAllProducts,
                            onChanged: (value) {
                              if (value == true) {
                                // If enabling all products, disable specific products
                                settings.updateSetting('applyTaxSpecificProduct', false);
                              }
                              settings.updateSetting('applyTaxAllProducts', value ?? false);
                            },
                          ),
                          
                          // Specific Products Option
                          CheckboxListTile(
                            title: const Text('Specific Products Only'),
                            subtitle: const Text('Apply tax only to products with GST rates set'),
                            value: settings.applyTaxSpecificProduct,
                            onChanged: (value) {
                              if (value == true) {
                                // If enabling specific products, disable all products
                                settings.updateSetting('applyTaxAllProducts', false);
                              }
                              settings.updateSetting('applyTaxSpecificProduct', value ?? false);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Information Card
                  Card(
                    color: Colors.blue[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info, color: Colors.blue[700]),
                              const SizedBox(width: 8),
                              Text(
                                'How it works:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '• All Products: Applies 18% GST (9% CGST + 9% SGST) to all items\n'
                            '• Specific Products: Only applies GST to products where you\'ve set GST rates\n'
                            '• You can set individual GST rates when creating/editing products',
                            style: TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Test GST Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _showGSTTestDialog(context, settings);
                      },
                      icon: const Icon(Icons.calculate),
                      label: const Text('Test GST Calculation'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  void _showGSTTestDialog(BuildContext context, SettingsProvider settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('GST Test'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Current GST Settings:'),
            const SizedBox(height: 8),
            Text('• Tax Enabled: ${settings.isTaxEnabled ? "Yes" : "No"}'),
            Text('• Apply to All Products: ${settings.applyTaxAllProducts ? "Yes" : "No"}'),
            Text('• Apply to Specific Products: ${settings.applyTaxSpecificProduct ? "Yes" : "No"}'),
            const SizedBox(height: 16),
            const Text(
              'Test by adding products to cart in the billing screen to see GST calculations.',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
