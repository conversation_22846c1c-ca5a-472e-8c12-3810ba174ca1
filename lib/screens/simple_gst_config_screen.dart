import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/simple_gst_service.dart';

/// Simplified GST Configuration Screen
/// Only includes essential GST settings
class SimpleGSTConfigScreen extends StatefulWidget {
  const SimpleGSTConfigScreen({super.key});

  @override
  State<SimpleGSTConfigScreen> createState() => _SimpleGSTConfigScreenState();
}

class _SimpleGSTConfigScreenState extends State<SimpleGSTConfigScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _businessNameController = TextEditingController();
  final _gstinController = TextEditingController();
  final _defaultGSTRateController = TextEditingController();

  // Configuration options
  bool _enableGST = true;

  bool _isLoading = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadConfiguration();
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _gstinController.dispose();
    _defaultGSTRateController.dispose();
    super.dispose();
  }

  /// Load existing GST configuration
  Future<void> _loadConfiguration() async {
    setState(() => _isLoading = true);

    try {
      // For now, we'll use SharedPreferences or a simple table
      // This is a simplified approach
      _businessNameController.text = 'My Business';
      _gstinController.text = '';
      _defaultGSTRateController.text = '18.0';
      _enableGST = true;
    } catch (e) {
      _showErrorSnackBar('Failed to load configuration: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Save GST configuration
  Future<void> _saveConfiguration() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      final config = SimpleGSTConfig(
        businessName: _businessNameController.text.trim(),
        gstin: _gstinController.text.trim().isEmpty ? null : _gstinController.text.trim(),
        defaultGSTRate: double.parse(_defaultGSTRateController.text),
        enableGST: _enableGST,
      );

      // Save configuration (simplified approach)
      // In a real implementation, this would save to database or SharedPreferences
      // For now, we just validate the config is created properly
      log('GST Config created: ${config.businessName}, GST Rate: ${config.defaultGSTRate}%');

      _showSuccessSnackBar('GST configuration saved successfully!');
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      _showErrorSnackBar('Failed to save configuration: $e');
    } finally {
      setState(() => _isSaving = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GST Configuration'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              onPressed: _saveConfiguration,
              icon: const Icon(Icons.save),
              tooltip: 'Save Configuration',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enable GST Switch
                    Card(
                      child: SwitchListTile(
                        title: const Text(
                          'Enable GST',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: const Text('Turn on/off GST calculations'),
                        value: _enableGST,
                        onChanged: (value) {
                          setState(() => _enableGST = value);
                        },
                        activeColor: Colors.green,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Business Information
                    const Text(
                      'Business Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Business Name
                    TextFormField(
                      controller: _businessNameController,
                      decoration: const InputDecoration(
                        labelText: 'Business Name *',
                        hintText: 'Enter your business name',
                        prefixIcon: Icon(Icons.business),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Business name is required';
                        }
                        return null;
                      },
                      enabled: _enableGST,
                    ),

                    const SizedBox(height: 16),

                    // GSTIN
                    TextFormField(
                      controller: _gstinController,
                      decoration: const InputDecoration(
                        labelText: 'GSTIN (Optional)',
                        hintText: 'Enter your GST identification number',
                        prefixIcon: Icon(Icons.numbers),
                        border: OutlineInputBorder(),
                        helperText: 'Format: 22AAAAA0000A1Z5',
                      ),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(15),
                        FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
                      ],
                      validator: (value) {
                        if (value != null && value.isNotEmpty && value.length != 15) {
                          return 'GSTIN must be exactly 15 characters';
                        }
                        return null;
                      },
                      enabled: _enableGST,
                    ),

                    const SizedBox(height: 24),

                    // GST Settings
                    const Text(
                      'GST Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Default GST Rate
                    TextFormField(
                      controller: _defaultGSTRateController,
                      decoration: const InputDecoration(
                        labelText: 'Default GST Rate (%)',
                        hintText: 'Enter default GST percentage',
                        prefixIcon: Icon(Icons.percent),
                        border: OutlineInputBorder(),
                        helperText: 'Common rates: 5%, 12%, 18%, 28%',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Default GST rate is required';
                        }
                        final rate = double.tryParse(value);
                        if (rate == null || rate < 0 || rate > 100) {
                          return 'Enter a valid rate between 0 and 100';
                        }
                        return null;
                      },
                      enabled: _enableGST,
                    ),

                    const SizedBox(height: 24),

                    // Information Card
                    Card(
                      color: Colors.blue.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.info, color: Colors.blue.shade700),
                                const SizedBox(width: 8),
                                Text(
                                  'How GST Works',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              '• GST is calculated on the taxable amount (after discount)\n'
                              '• Total GST = CGST + SGST (each is half of GST rate)\n'
                              '• Example: 18% GST = 9% CGST + 9% SGST\n'
                              '• You can set different GST rates for individual products',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isSaving ? null : _saveConfiguration,
                        icon: _isSaving
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.save),
                        label: Text(_isSaving ? 'Saving...' : 'Save Configuration'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
