import 'package:flutter/material.dart';
import '../widgets/common_appbar.dart';
import '../utils/comprehensive_bug_detector.dart';

class BugDetectionScreen extends StatefulWidget {
  const BugDetectionScreen({super.key});

  @override
  State<BugDetectionScreen> createState() => _BugDetectionScreenState();
}

class _BugDetectionScreenState extends State<BugDetectionScreen> {
  List<BugReport>? _bugReports;
  bool _isScanning = false;
  String _summary = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: 'Bug Detection'),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Bug Detection',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Scan your system to identify and fix bugs automatically.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            
            // Scan <PERSON>ton
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isScanning ? null : _runBugDetection,
                icon: _isScanning 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.bug_report),
                label: Text(_isScanning ? 'Scanning...' : 'Scan for Bugs'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                  backgroundColor: _isScanning ? Colors.grey : Colors.blue,
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Summary
            if (_summary.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Summary',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      Text(_summary),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Bug Reports
            if (_bugReports != null) ...[
              const Text(
                'Detected Issues:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              
              if (_bugReports!.isEmpty)
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 32),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'No Issues Found!',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.green,
                                ),
                              ),
                              Text('Your system appears to be working correctly.'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                Expanded(
                  child: ListView.builder(
                    itemCount: _bugReports!.length,
                    itemBuilder: (context, index) {
                      final bug = _bugReports![index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ExpansionTile(
                          leading: Icon(
                            _getSeverityIcon(bug.severity),
                            color: _getSeverityColor(bug.severity),
                          ),
                          title: Text(
                            bug.title,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          subtitle: Text(bug.category),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Description:',
                                    style: TextStyle(fontWeight: FontWeight.w600),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(bug.description),
                                  const SizedBox(height: 12),
                                  const Text(
                                    'Suggested Solution:',
                                    style: TextStyle(fontWeight: FontWeight.w600),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    bug.solution,
                                    style: const TextStyle(color: Colors.blue),
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    children: [
                                      Chip(
                                        label: Text(
                                          bug.severity.name.toUpperCase(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                          ),
                                        ),
                                        backgroundColor: _getSeverityColor(bug.severity),
                                      ),
                                      const SizedBox(width: 8),
                                      Chip(
                                        label: Text(bug.category),
                                        backgroundColor: Colors.grey[200],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
            ] else if (!_isScanning) ...[
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Ready to scan',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Click the scan button to detect issues',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text(
                        'Scanning system...',
                        style: TextStyle(fontSize: 18),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This may take a few moments',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runBugDetection() async {
    setState(() {
      _isScanning = true;
      _bugReports = null;
      _summary = '';
    });

    try {
      final bugs = await ComprehensiveBugDetector.detectAllBugs();
      final summary = ComprehensiveBugDetector.generateBugReportSummary(bugs);
      
      setState(() {
        _bugReports = bugs;
        _summary = summary;
        _isScanning = false;
      });

      // Show completion message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              bugs.isEmpty 
                ? 'Scan completed: No issues found!'
                : 'Scan completed: ${bugs.length} issues detected',
            ),
            backgroundColor: bugs.isEmpty ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isScanning = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Scan failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  IconData _getSeverityIcon(BugSeverity severity) {
    switch (severity) {
      case BugSeverity.critical:
        return Icons.error;
      case BugSeverity.high:
        return Icons.warning;
      case BugSeverity.medium:
        return Icons.info;
      case BugSeverity.low:
        return Icons.help_outline;
    }
  }

  Color _getSeverityColor(BugSeverity severity) {
    switch (severity) {
      case BugSeverity.critical:
        return Colors.red;
      case BugSeverity.high:
        return Colors.orange;
      case BugSeverity.medium:
        return Colors.yellow[700]!;
      case BugSeverity.low:
        return Colors.green;
    }
  }
}
