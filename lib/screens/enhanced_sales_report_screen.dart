import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../features/report/enhanced_sales_report_provider.dart';
import '../widgets/common_appbar.dart';
import '../widgets/common_drawer.dart';

class EnhancedSalesReportScreen extends StatefulWidget {
  const EnhancedSalesReportScreen({super.key});

  @override
  State<EnhancedSalesReportScreen> createState() => _EnhancedSalesReportScreenState();
}

class _EnhancedSalesReportScreenState extends State<EnhancedSalesReportScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<EnhancedSalesReportProvider>(context, listen: false).loadAllData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(title: 'Enhanced Sales & Tax Report'),
      body: Consumer<EnhancedSalesReportProvider>(
        builder: (context, reportProvider, child) {
          return DefaultTabController(
            length: 4,
            child: Column(
              children: [
                // Tab Bar
                Container(
                  color: Colors.brown.withOpacity(0.1),
                  child: const TabBar(
                    labelColor: Colors.brown,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Colors.brown,
                    tabs: [
                      Tab(text: 'Summary', icon: Icon(Icons.dashboard, size: 16)),
                      Tab(text: 'Tax Details', icon: Icon(Icons.receipt, size: 16)),
                      Tab(text: 'Products', icon: Icon(Icons.inventory, size: 16)),
                      Tab(text: 'Compliance', icon: Icon(Icons.verified, size: 16)),
                    ],
                  ),
                ),

                // Date Filter Section
                _buildDateFilterSection(reportProvider),

                // Tab Views
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildSummaryTab(reportProvider),
                      _buildTaxDetailsTab(reportProvider),
                      _buildProductsTab(reportProvider),
                      _buildComplianceTab(reportProvider),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDateFilterSection(EnhancedSalesReportProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade50,
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => _selectDate(context, true, provider),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.brown),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  provider.fromDate == null
                      ? 'From Date'
                      : DateFormat('dd/MM/yyyy').format(provider.fromDate!),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: InkWell(
              onTap: () => _selectDate(context, false, provider),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.brown),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  provider.toDate == null ? 'To Date' : DateFormat('dd/MM/yyyy').format(provider.toDate!),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () {
              if (provider.fromDate != null && provider.toDate != null) {
                provider.getDataBetweenDates(provider.fromDate!, provider.toDate!);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.brown,
              foregroundColor: Colors.white,
            ),
            child: const Text('Filter'),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => provider.clearFilters(),
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Filters',
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTab(EnhancedSalesReportProvider provider) {
    final summary = provider.taxSummary;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Key Metrics Cards
          Row(
            children: [
              Expanded(
                  child: _buildMetricCard(
                      'Total Sales', '₹${_formatAmount(summary['totalSales'] ?? 0)}', Colors.green)),
              const SizedBox(width: 12),
              Expanded(
                  child: _buildMetricCard(
                      'Total Tax', '₹${_formatAmount(summary['totalTax'] ?? 0)}', Colors.blue)),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildMetricCard('Orders', '${summary['salesCount'] ?? 0}', Colors.orange)),
              const SizedBox(width: 12),
              Expanded(
                  child: _buildMetricCard(
                      'Avg Order', '₹${_formatAmount(summary['averageOrderValue'] ?? 0)}', Colors.purple)),
            ],
          ),

          const SizedBox(height: 24),

          // Detailed Summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Detailed Breakdown',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  _buildSummaryRow('Subtotal', summary['totalSubtotal'] ?? 0),
                  _buildSummaryRow('Discount', summary['totalDiscount'] ?? 0, isNegative: true),
                  _buildSummaryRow('Parcel Charges', summary['totalParcel'] ?? 0),
                  const Divider(),
                  _buildSummaryRow('CGST', summary['totalCGST'] ?? 0),
                  _buildSummaryRow('SGST', summary['totalSGST'] ?? 0),
                  _buildSummaryRow('IGST', summary['totalIGST'] ?? 0),
                  _buildSummaryRow('Cess', summary['totalCess'] ?? 0),
                  const Divider(),
                  _buildSummaryRow('Grand Total', summary['totalSales'] ?? 0, isBold: true),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxDetailsTab(EnhancedSalesReportProvider provider) {
    final breakdown = provider.gstBreakdown;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('GST Rate-wise Breakdown', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          if (breakdown.isEmpty)
            const Center(child: Text('No tax data available'))
          else
            ...breakdown.map((item) => Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'GST ${item['rate']}%',
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            Text(
                              '${item['itemCount']} items',
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        _buildTaxRow('Taxable Amount', item['taxableAmount']),
                        if (item['cgst'] > 0) _buildTaxRow('CGST', item['cgst']),
                        if (item['sgst'] > 0) _buildTaxRow('SGST', item['sgst']),
                        if (item['igst'] > 0) _buildTaxRow('IGST', item['igst']),
                        if (item['cess'] > 0) _buildTaxRow('Cess', item['cess']),
                        const Divider(),
                        _buildTaxRow('Total Tax', item['totalTax'], isBold: true),
                      ],
                    ),
                  ),
                )),
        ],
      ),
    );
  }

  Widget _buildProductsTab(EnhancedSalesReportProvider provider) {
    final topProducts = provider.getTopProductsBySales();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Top Products by Sales', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          if (topProducts.isEmpty)
            const Center(child: Text('No product data available'))
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: topProducts.length,
              itemBuilder: (context, index) {
                final product = topProducts[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.brown,
                      child: Text('${index + 1}', style: const TextStyle(color: Colors.white)),
                    ),
                    title: Text(product['productName']),
                    subtitle:
                        Text('Qty: ${product['totalQty']} | Avg: ₹${_formatAmount(product['avgPrice'])}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text('₹${_formatAmount(product['totalSales'])}',
                            style: const TextStyle(fontWeight: FontWeight.bold)),
                        Text('Tax: ₹${_formatAmount(product['totalTax'])}',
                            style: const TextStyle(fontSize: 12, color: Colors.grey)),
                      ],
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildComplianceTab(EnhancedSalesReportProvider provider) {
    final compliance = provider.getTaxComplianceSummary();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Tax Compliance Summary', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildComplianceRow('Total Items', compliance['totalItems']),
                  _buildComplianceRow('Taxed Items', compliance['taxedItems']),
                  _buildComplianceRow('Tax-Free Items', compliance['taxFreeItems']),
                  _buildComplianceRow('Custom Tax Items', compliance['customTaxItems']),
                  const Divider(),
                  _buildComplianceRow(
                      'Tax Compliance Rate', '${compliance['taxComplianceRate'].toStringAsFixed(1)}%'),
                  _buildComplianceRow(
                      'Custom Tax Rate', '${compliance['customTaxRate'].toStringAsFixed(1)}%'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(title, style: const TextStyle(fontSize: 12, color: Colors.grey)),
            const SizedBox(height: 8),
            Text(value, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: color)),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {bool isNegative = false, bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal)),
          Text(
            '${isNegative ? '-' : ''}₹${_formatAmount(amount)}',
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: isNegative ? Colors.red : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxRow(String label, double amount, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal)),
          Text('₹${_formatAmount(amount)}',
              style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal)),
        ],
      ),
    );
  }

  Widget _buildComplianceRow(String label, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text('$value', style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(2);
  }

  Future<void> _selectDate(
      BuildContext context, bool isFromDate, EnhancedSalesReportProvider provider) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      if (isFromDate) {
        provider.fromDate = picked;
      } else {
        provider.toDate = picked;
      }
      provider.notifyListeners();
    }
  }
}
