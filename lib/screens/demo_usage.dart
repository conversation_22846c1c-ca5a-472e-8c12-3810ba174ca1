import 'package:flutter/material.dart';
import 'screen_navigation_helper.dart';

/// Demo usage example showing how to integrate all the new screens
/// This file demonstrates how to use the new screens in your app
class DemoUsageExample extends StatelessWidget {
  const DemoUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: const Color(0xFF6A1B9A),
        foregroundColor: Colors.white,
        title: const Text(
          'Screen Demo',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 20),
            
            // Info Card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[600],
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'All Screens Working Perfectly!',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Tap any button below to test the screens',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue[600],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Tax Settings Button
            _buildNavigationButton(
              context,
              'Tax Settings',
              'Configure GST and tax options',
              Icons.receipt_long,
              Colors.green,
              () => ScreenNavigationHelper.navigateToTaxSettings(context),
            ),
            
            const SizedBox(height: 16),
            
            // Discount Management Button
            _buildNavigationButton(
              context,
              'Discount Management',
              'View existing discounts',
              Icons.discount,
              Colors.orange,
              () => ScreenNavigationHelper.navigateToDiscountManagement(context),
            ),
            
            const SizedBox(height: 16),
            
            // Create Discount Button
            _buildNavigationButton(
              context,
              'Create Discount',
              'Add new discount offers',
              Icons.add_circle,
              Colors.purple,
              () => ScreenNavigationHelper.navigateToCreateDiscount(context),
            ),
            
            const SizedBox(height: 16),
            
            // Add Product Button
            _buildNavigationButton(
              context,
              'Add Product',
              'Add products to inventory',
              Icons.inventory,
              Colors.blue,
              () => ScreenNavigationHelper.navigateToAddProduct(context),
            ),
            
            const Spacer(),
            
            // Features List
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Features Implemented',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.green[800],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildFeatureItem('✓ Purple theme matching your design'),
                  _buildFeatureItem('✓ Form validation and error handling'),
                  _buildFeatureItem('✓ Loading states and user feedback'),
                  _buildFeatureItem('✓ Responsive layouts and scrolling'),
                  _buildFeatureItem('✓ Material Design components'),
                  _buildFeatureItem('✓ Clean navigation and routing'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: Colors.green[700],
        ),
      ),
    );
  }
}

/// Example of how to integrate these screens in your main app
/// 
/// ```dart
/// // In your main app, you can navigate to any screen like this:
/// 
/// // Navigate to Tax Settings
/// Navigator.push(context, MaterialPageRoute(
///   builder: (context) => const TaxSettingsScreen(),
/// ));
/// 
/// // Navigate to Discount Management
/// Navigator.push(context, MaterialPageRoute(
///   builder: (context) => const DiscountManagementScreen(),
/// ));
/// 
/// // Navigate to Create Discount
/// Navigator.push(context, MaterialPageRoute(
///   builder: (context) => const CreateDiscountScreen(),
/// ));
/// 
/// // Navigate to Add Product
/// Navigator.push(context, MaterialPageRoute(
///   builder: (context) => const AddProductScreen(),
/// ));
/// 
/// // Or use the helper class:
/// ScreenNavigationHelper.navigateToTaxSettings(context);
/// ScreenNavigationHelper.navigateToDiscountManagement(context);
/// ScreenNavigationHelper.navigateToCreateDiscount(context);
/// ScreenNavigationHelper.navigateToAddProduct(context);
/// ```
