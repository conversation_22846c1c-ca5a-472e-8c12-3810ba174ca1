import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../database/entities/gst_configuration_dto.dart';
import '../database/app_database.dart';

class GSTConfigurationScreen extends StatefulWidget {
  const GSTConfigurationScreen({super.key});

  @override
  State<GSTConfigurationScreen> createState() => _GSTConfigurationScreenState();
}

class _GSTConfigurationScreenState extends State<GSTConfigurationScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _businessNameController = TextEditingController();
  final _gstinController = TextEditingController();
  final _stateCodeController = TextEditingController();
  final _addressController = TextEditingController();
  final _defaultGSTRateController = TextEditingController();

  // Configuration options
  bool _enableIGST = true;
  bool _enableCess = false;
  bool _enableReverseCharge = false;
  bool _taxInclusivePricing = false;
  String _roundingMethod = 'ROUND_OFF';

  bool _isLoading = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadConfiguration();
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _gstinController.dispose();
    _stateCodeController.dispose();
    _addressController.dispose();
    _defaultGSTRateController.dispose();
    super.dispose();
  }

  Future<void> _loadConfiguration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dao = await AppDatabase().gstConfigurationDao;
      final config = await dao.getActiveConfiguration();

      if (config != null) {
        _businessNameController.text = config.businessName;
        _gstinController.text = config.businessGSTIN ?? '';
        _stateCodeController.text = config.businessStateCode;
        _addressController.text = config.businessAddress ?? '';
        _defaultGSTRateController.text = config.defaultGSTRate.toString();

        setState(() {
          _enableIGST = config.enableIGST;
          _enableCess = config.enableCess;
          _enableReverseCharge = config.enableReverseCharge;
          _taxInclusivePricing = config.taxInclusivePricing;
          _roundingMethod = config.roundingMethod;
        });
      } else {
        // Set default values
        _defaultGSTRateController.text = '18.0';
        _stateCodeController.text = '33'; // Default to Tamil Nadu
      }
    } catch (e) {
      _showErrorSnackBar('Error loading configuration: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveConfiguration() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final dao = await AppDatabase().gstConfigurationDao;

      final config = GSTConfigurationDto(
        workSpaceId: 'current_workspace', // Should come from session
        businessName: _businessNameController.text.trim(),
        businessGSTIN: _gstinController.text.trim().isEmpty ? null : _gstinController.text.trim(),
        businessStateCode: _stateCodeController.text.trim(),
        businessAddress: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        defaultGSTRate: double.parse(_defaultGSTRateController.text),
        enableIGST: _enableIGST,
        enableCess: _enableCess,
        enableReverseCharge: _enableReverseCharge,
        taxInclusivePricing: _taxInclusivePricing,
        roundingMethod: _roundingMethod,
        isActive: true,
        syncStatus: 0,
      );

      await dao.insertOrUpdate(config);

      _showSuccessSnackBar('GST configuration saved successfully!');
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      _showErrorSnackBar('Error saving configuration: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GST Configuration'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              onPressed: _saveConfiguration,
              icon: const Icon(Icons.save),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Business Information Section
                    _buildSectionHeader('Business Information'),
                    _buildBusinessNameField(),
                    const SizedBox(height: 16),
                    _buildGSTINField(),
                    const SizedBox(height: 16),
                    _buildStateCodeField(),
                    const SizedBox(height: 16),
                    _buildAddressField(),

                    const SizedBox(height: 24),

                    // Tax Configuration Section
                    _buildSectionHeader('Tax Configuration'),
                    _buildDefaultGSTRateField(),
                    const SizedBox(height: 16),
                    _buildRoundingMethodField(),

                    const SizedBox(height: 24),

                    // Tax Options Section
                    _buildSectionHeader('Tax Options'),
                    _buildTaxOptionsSection(),

                    const SizedBox(height: 32),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSaving ? null : _saveConfiguration,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.brown,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: _isSaving
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Text('Saving...'),
                                ],
                              )
                            : const Text(
                                'Save Configuration',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.brown,
        ),
      ),
    );
  }

  Widget _buildBusinessNameField() {
    return TextFormField(
      controller: _businessNameController,
      decoration: const InputDecoration(
        labelText: 'Business Name *',
        hintText: 'Enter your business name',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.business),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Business name is required';
        }
        return null;
      },
    );
  }

  Widget _buildGSTINField() {
    return TextFormField(
      controller: _gstinController,
      decoration: const InputDecoration(
        labelText: 'GSTIN (Optional)',
        hintText: 'Enter 15-digit GSTIN',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.receipt_long),
      ),
      inputFormatters: [
        LengthLimitingTextInputFormatter(15),
        FilteringTextInputFormatter.allow(RegExp(r'[0-9A-Z]')),
      ],
      validator: (value) {
        if (value != null && value.isNotEmpty && value.length != 15) {
          return 'GSTIN must be 15 characters';
        }
        return null;
      },
    );
  }

  Widget _buildStateCodeField() {
    return TextFormField(
      controller: _stateCodeController,
      decoration: const InputDecoration(
        labelText: 'State Code *',
        hintText: 'Enter 2-digit state code',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_on),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        LengthLimitingTextInputFormatter(2),
        FilteringTextInputFormatter.digitsOnly,
      ],
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'State code is required';
        }
        if (value.length != 2) {
          return 'State code must be 2 digits';
        }
        return null;
      },
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      decoration: const InputDecoration(
        labelText: 'Business Address (Optional)',
        hintText: 'Enter your business address',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.home),
      ),
      maxLines: 3,
    );
  }

  Widget _buildDefaultGSTRateField() {
    return TextFormField(
      controller: _defaultGSTRateController,
      decoration: const InputDecoration(
        labelText: 'Default GST Rate (%) *',
        hintText: 'Enter default GST rate',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.percent),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Default GST rate is required';
        }
        final rate = double.tryParse(value);
        if (rate == null || rate < 0 || rate > 100) {
          return 'Enter a valid rate between 0 and 100';
        }
        return null;
      },
    );
  }

  Widget _buildRoundingMethodField() {
    return DropdownButtonFormField<String>(
      value: _roundingMethod,
      decoration: const InputDecoration(
        labelText: 'Rounding Method',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.calculate),
      ),
      items: const [
        DropdownMenuItem(value: 'ROUND_OFF', child: Text('Round Off')),
        DropdownMenuItem(value: 'ROUND_UP', child: Text('Round Up')),
        DropdownMenuItem(value: 'ROUND_DOWN', child: Text('Round Down')),
        DropdownMenuItem(value: 'NO_ROUNDING', child: Text('No Rounding')),
      ],
      onChanged: (value) {
        setState(() {
          _roundingMethod = value!;
        });
      },
    );
  }

  Widget _buildTaxOptionsSection() {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Enable IGST'),
          subtitle: const Text('For inter-state transactions'),
          value: _enableIGST,
          onChanged: (value) {
            setState(() {
              _enableIGST = value;
            });
          },
        ),
        SwitchListTile(
          title: const Text('Enable Cess'),
          subtitle: const Text('Additional tax on specific goods'),
          value: _enableCess,
          onChanged: (value) {
            setState(() {
              _enableCess = value;
            });
          },
        ),
        SwitchListTile(
          title: const Text('Enable Reverse Charge'),
          subtitle: const Text('For specific business scenarios'),
          value: _enableReverseCharge,
          onChanged: (value) {
            setState(() {
              _enableReverseCharge = value;
            });
          },
        ),
        SwitchListTile(
          title: const Text('Tax Inclusive Pricing'),
          subtitle: const Text('Prices include tax'),
          value: _taxInclusivePricing,
          onChanged: (value) {
            setState(() {
              _taxInclusivePricing = value;
            });
          },
        ),
      ],
    );
  }
}
