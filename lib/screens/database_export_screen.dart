import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../database/database_export_utility.dart';
import '../utils/schema_share_utility.dart';

class DatabaseExportScreen extends StatefulWidget {
  const DatabaseExportScreen({super.key});

  @override
  State<DatabaseExportScreen> createState() => _DatabaseExportScreenState();
}

class _DatabaseExportScreenState extends State<DatabaseExportScreen> {
  bool _isExporting = false;
  List<FileSystemEntity> _exportedFiles = [];
  final TextEditingController _fileNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadExportedFiles();
  }

  @override
  void dispose() {
    _fileNameController.dispose();
    super.dispose();
  }

  Future<void> _loadExportedFiles() async {
    try {
      final files = await DatabaseExportUtility.getExportedFiles();
      setState(() {
        _exportedFiles = files;
      });
    } catch (e) {
      _showErrorSnackBar('Error loading exported files: $e');
    }
  }

  Future<void> _exportSchema({bool includeData = false}) async {
    setState(() {
      _isExporting = true;
    });

    try {
      final customFileName = _fileNameController.text.trim().isEmpty ? null : _fileNameController.text.trim();

      final filePath = await DatabaseExportUtility.exportSchemaWithDate(
        includeData: includeData,
        customFileName: customFileName,
      );

      _showSuccessSnackBar('Schema exported successfully!\nFile: ${filePath.split('/').last}');

      _fileNameController.clear();
      await _loadExportedFiles();
    } catch (e) {
      _showErrorSnackBar('Export failed: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _deleteFile(String filePath) async {
    final confirmed = await _showDeleteConfirmation();
    if (!confirmed) return;

    try {
      final success = await DatabaseExportUtility.deleteExportedFile(filePath);
      if (success) {
        _showSuccessSnackBar('File deleted successfully');
        await _loadExportedFiles();
      } else {
        _showErrorSnackBar('Failed to delete file');
      }
    } catch (e) {
      _showErrorSnackBar('Error deleting file: $e');
    }
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Export File'),
            content: const Text('Are you sure you want to delete this export file?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(date);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Export'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Export Options Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Export Database Schema',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Custom filename input
                    TextField(
                      controller: _fileNameController,
                      decoration: const InputDecoration(
                        labelText: 'Custom filename (optional)',
                        hintText: 'e.g., my_backup.sql',
                        border: OutlineInputBorder(),
                        suffixText: '.sql',
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Export buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isExporting ? null : () => _exportSchema(includeData: false),
                            icon: const Icon(Icons.schema),
                            label: const Text('Schema Only'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isExporting ? null : () => _exportSchema(includeData: true),
                            icon: const Icon(Icons.storage),
                            label: const Text('Schema + Data'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Share button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            _isExporting ? null : () => SchemaShareUtility.showShareOptionsDialog(context),
                        icon: const Icon(Icons.share),
                        label: const Text('Share Schema'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),

                    if (_isExporting) ...[
                      const SizedBox(height: 16),
                      const Row(
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Exporting database...'),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Exported Files List
            const Text(
              'Exported Files',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            Expanded(
              child: _exportedFiles.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.folder_open,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No exported files found',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _exportedFiles.length,
                      itemBuilder: (context, index) {
                        final file = _exportedFiles[index];
                        final stat = file.statSync();
                        final fileName = file.path.split('/').last;

                        return Card(
                          child: ListTile(
                            leading: const Icon(
                              Icons.insert_drive_file,
                              color: Colors.blue,
                            ),
                            title: Text(
                              fileName,
                              style: const TextStyle(fontWeight: FontWeight.w500),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Size: ${_formatFileSize(stat.size)}'),
                                Text('Modified: ${_formatDate(stat.modified)}'),
                              ],
                            ),
                            trailing: PopupMenuButton<String>(
                              onSelected: (value) async {
                                switch (value) {
                                  case 'share':
                                    try {
                                      await SchemaShareUtility.shareExistingFile(filePath: file.path);
                                    } catch (e) {
                                      _showErrorSnackBar('Failed to share file: $e');
                                    }
                                    break;
                                  case 'copy_path':
                                    Clipboard.setData(ClipboardData(text: file.path));
                                    _showSuccessSnackBar('File path copied to clipboard');
                                    break;
                                  case 'delete':
                                    _deleteFile(file.path);
                                    break;
                                }
                              },
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'share',
                                  child: Row(
                                    children: [
                                      Icon(Icons.share, color: Colors.blue),
                                      SizedBox(width: 8),
                                      Text('Share'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'copy_path',
                                  child: Row(
                                    children: [
                                      Icon(Icons.copy),
                                      SizedBox(width: 8),
                                      Text('Copy Path'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text('Delete', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadExportedFiles,
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
