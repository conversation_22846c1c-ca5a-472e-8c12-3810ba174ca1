import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

void showSuccessAlert({required String message, VoidCallback? onClose}) {
  Timer? timer;

  Get.dialog(
    AlertDialog(
      title: const Text("Success", style: <PERSON><PERSON><PERSON>le(color: Colors.green)),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () {
            timer?.cancel(); // Cancel auto-close if user manually closes
            Get.back();
          },
          child: const Text("Close"),
        ),
      ],
    ),
    barrierDismissible: false, // Prevent dismissing by tapping outside
  ).then((_) {
    timer?.cancel(); // Ensure timer is canceled if dialog is closed manually
    if (onClose != null) {
      onClose(); // Execute callback after dialog is closed
    }
  });

  // Auto-close after 5 seconds
  timer = Timer(const Duration(seconds: 5), () {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }).obs.value;
}
