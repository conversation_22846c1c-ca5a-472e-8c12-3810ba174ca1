import 'package:flutter/material.dart';

class DrawerExpandableItem extends StatefulWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final List<Widget>? subMenus;

  const DrawerExpandableItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.subMenus,
  });

  @override
  State<DrawerExpandableItem> createState() => _DrawerExpandableItemState();
}

class _DrawerExpandableItemState extends State<DrawerExpandableItem> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final isExpandable = widget.subMenus != null && widget.subMenus!.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          leading: Icon(widget.icon),
          title: Text(widget.title),
          trailing: isExpandable ? Icon(_isExpanded ? Icons.expand_less : Icons.expand_more) : null,
          onTap: () {
            if (isExpandable) {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            } else {
              if (Scaffold.of(context).isDrawerOpen) {
                Navigator.pop(context);
              }
              widget.onTap();
            }
          },
        ),
        if (isExpandable && _isExpanded)
          Padding(
            padding: const EdgeInsets.only(left: 30.0),
            child: Column(children: widget.subMenus!),
          ),
      ],
    );
  }
}
