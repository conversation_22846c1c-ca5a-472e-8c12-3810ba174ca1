import 'package:flutter/material.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

class CommonDropdown extends StatefulWidget {
  final List<DropdownItem> items;
  final DropdownItem? selectedItem;
  final ValueChanged<DropdownItem?> onChanged;
  final bool isSearchable;
  final bool isFullScreenSearch;
  final String? hitLabel;
  final bool enableMargine;

  const CommonDropdown(
      {super.key,
      required this.items,
      required this.selectedItem,
      required this.onChanged,
      this.isSearchable = false,
      this.enableMargine = true,
      this.isFullScreenSearch = false,
      this.hitLabel});

  @override
  State<CommonDropdown> createState() => _CommonDropdownState();
}

class _CommonDropdownState extends State<CommonDropdown> {
  final GlobalKey _dropdownButtonKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  late RenderBox _buttonRenderBox;
  late Offset _buttonPosition;

  @override
  Widget build(BuildContext context) {
    if (widget.isFullScreenSearch) {
      return FullScreenSearchDropdown(
        items: widget.items,
        selectedItem: widget.selectedItem,
        onChanged: widget.onChanged,
      );
    } else if (widget.isSearchable) {
      return SearchableDropdown(
        items: widget.items,
        selectedItem: widget.selectedItem,
        onChanged: widget.onChanged,
      );
    } else {
      return Container(
        margin: widget.enableMargine ? const EdgeInsets.symmetric(vertical: 8) : null,
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey.shade400),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: DropdownButtonHideUnderline(
          child: GestureDetector(
            key: _dropdownButtonKey,
            onTap: _toggleDropdown,
            child: DropdownButton<DropdownItem>(
              value: widget.selectedItem,
              isExpanded: true,
              hint: Text(
                widget.hitLabel ?? "Select an option",
                style: const TextStyle(color: Colors.black),
              ),
              items: widget.items
                  .map((item) => DropdownMenuItem<DropdownItem>(
                        value: item,
                        child: Text(item.label),
                      ))
                  .toList(),
              onChanged: widget.onChanged,
            ),
          ),
        ),
      );
    }
  }

  void _toggleDropdown() {
    _buttonRenderBox = _dropdownButtonKey.currentContext!.findRenderObject() as RenderBox;
    _buttonPosition = _buttonRenderBox.localToGlobal(Offset.zero);

    // Get available space in the screen
    final screenHeight = MediaQuery.of(context).size.height;
    const dropdownHeight = 200.0; // Set this to the height of your dropdown
    double yOffset = _buttonPosition.dy + _buttonRenderBox.size.height + dropdownHeight <= screenHeight
        ? _buttonPosition.dy + _buttonRenderBox.size.height
        : _buttonPosition.dy - dropdownHeight;

    _overlayEntry = _createOverlayEntry(yOffset);
    Overlay.of(context).insert(_overlayEntry!);
  }

  OverlayEntry _createOverlayEntry(double yOffset) {
    return OverlayEntry(
      builder: (context) {
        return Positioned(
          top: yOffset,
          left: _buttonPosition.dx,
          width: _buttonRenderBox.size.width,
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: widget.items.map((item) {
                  return GestureDetector(
                    onTap: () {
                      widget.onChanged(item);
                      _overlayEntry?.remove();
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      child: Text(item.label),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }
}

class SearchableDropdown extends StatelessWidget {
  final List<DropdownItem> items;
  final DropdownItem? selectedItem;
  final ValueChanged<DropdownItem?> onChanged;

  const SearchableDropdown({
    super.key,
    required this.items,
    required this.selectedItem,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton2<DropdownItem>(
      value: selectedItem,
      isExpanded: true,
      hint: const Text("Select an option"),
      items: items.map((DropdownItem item) {
        return DropdownMenuItem<DropdownItem>(
          value: item,
          child: Text(item.label),
        );
      }).toList(),
      onChanged: onChanged,
      dropdownStyleData: DropdownStyleData(
        maxHeight: 300,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
      ),
      buttonStyleData: ButtonStyleData(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey),
        ),
      ),
      menuItemStyleData: const MenuItemStyleData(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      dropdownSearchData: DropdownSearchData<DropdownItem>(
        searchController: TextEditingController(),
        searchInnerWidget: Padding(
          padding: const EdgeInsets.all(8.0),
          child: TextField(
            decoration: InputDecoration(
              hintText: "Search...",
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),
        searchMatchFn: (item, searchValue) {
          return item.value!.label.toLowerCase().contains(searchValue.toLowerCase());
        },
      ),
    );
  }
}

class FullScreenSearchDropdown extends StatefulWidget {
  final List<DropdownItem> items;
  final DropdownItem? selectedItem;
  final ValueChanged<DropdownItem?> onChanged;

  const FullScreenSearchDropdown({
    super.key,
    required this.items,
    required this.selectedItem,
    required this.onChanged,
  });

  @override
  State<FullScreenSearchDropdown> createState() => _FullScreenSearchDropdownState();
}

class _FullScreenSearchDropdownState extends State<FullScreenSearchDropdown> {
  void _openSearchModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              TextField(
                decoration: InputDecoration(
                  hintText: "Search...",
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
                onChanged: (value) {
                  setState(() {});
                },
              ),
              Expanded(
                child: ListView(
                  children: widget.items.map((DropdownItem item) {
                    return ListTile(
                      title: Text(item.label),
                      onTap: () {
                        widget.onChanged(item);
                        Navigator.pop(context);
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _openSearchModal(context),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: "Select an option",
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(widget.selectedItem?.label ?? "Tap to select"),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ),
    );
  }
}

class DropdownItem {
  final String id;
  final String label;

  DropdownItem({required this.id, required this.label});

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is DropdownItem && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => label;
}
