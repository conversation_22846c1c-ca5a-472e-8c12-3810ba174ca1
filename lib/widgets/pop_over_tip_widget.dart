import 'package:flutter/material.dart';
import 'package:popover/popover.dart';

Widget unitInfoPopover(BuildContext context) {
  return IconButton(
    icon: const Icon(Icons.info_outline, color: Colors.grey),
    onPressed: () {
      showPopover(
        context: context,
        bodyBuilder: (context) => Container(
          width: 250, // You can adjust this width as needed
          padding: const EdgeInsets.all(12),
          child: const Text(
            '''
📏 Measurement Units:

✅ Grams (g) → Small solids (sugar, coffee)
✅ Milliliters (ml) → Small liquids (milk, oil)
✅ Ounce (oz) → Solids/liquids (1oz ≈ 28g)
✅ Liters (L) → Large liquids (water)
✅ Piece (pcs) → Countable items
✅ Pack → Grouped items

Cheatsheet:
- 1 L = 1000 ml
- 1 kg = 1000 g
- 1 oz ≈ 28.35 g
''',
            style: TextStyle(fontSize: 12),
          ),
        ),
        direction: PopoverDirection.bottom, // Ensures the popover opens below
        width: 280, // You can tweak the width to fit better
        height: 270, // This ensures it's not too tall
        arrowHeight: 10,
        arrowWidth: 20,
        backgroundColor: Colors.white,
        barrierColor: Colors.transparent,
        transitionDuration: const Duration(milliseconds: 200),
      );
    },
  );
}
