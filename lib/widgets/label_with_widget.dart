import 'package:flutter/material.dart';

class LabeledFormField extends StatelessWidget {
  final String labelText;
  final Widget field;

  const LabeledFormField(this.labelText, this.field, {super.key});

  @override
  Widget build(BuildContext context) {
    final bool isRequired = labelText.trim().endsWith('*');
    final String label =
        isRequired ? labelText.trim().substring(0, labelText.trim().length - 1).trim() : labelText;

    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              style: DefaultTextStyle.of(context).style.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
              children: [
                TextSpan(text: label),
                if (isRequired)
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(color: Colors.red),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 2),
          field,
        ],
      ),
    );
  }
}
