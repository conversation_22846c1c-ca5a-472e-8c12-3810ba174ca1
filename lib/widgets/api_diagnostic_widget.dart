import 'package:flutter/material.dart';
import '../utils/api_health_checker.dart';
import '../core/network/api_endpoints.dart';

/// Widget to diagnose API issues and display server status
class ApiDiagnosticWidget extends StatefulWidget {
  @override
  _ApiDiagnosticWidgetState createState() => _ApiDiagnosticWidgetState();
}

class _ApiDiagnosticWidgetState extends State<ApiDiagnosticWidget> {
  bool _isRunning = false;
  ApiDiagnostics? _diagnostics;
  String _status = 'Ready to run diagnostics';
  
  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _status = 'Running diagnostics...';
      _diagnostics = null;
    });
    
    try {
      final diagnostics = await ApiHealthChecker.runDiagnostics();
      ApiHealthChecker.printDiagnosticReport(diagnostics);
      
      setState(() {
        _diagnostics = diagnostics;
        _status = 'Diagnostics completed';
      });
    } catch (e) {
      setState(() {
        _status = 'Diagnostics failed: $e';
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.network_check, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'API Diagnostics',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            // Server Info
            _buildInfoRow('Server URL', ApiEndpoints.baseUrl),
            SizedBox(height: 8),
            _buildInfoRow('Status', _status),
            SizedBox(height: 16),
            
            // Run Diagnostics Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isRunning ? null : _runDiagnostics,
                child: _isRunning
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Running...'),
                        ],
                      )
                    : Text('🔍 Run API Diagnostics'),
              ),
            ),
            
            // Results
            if (_diagnostics != null) ...[
              SizedBox(height: 16),
              Divider(),
              SizedBox(height: 16),
              _buildDiagnosticResults(),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(fontFamily: 'monospace'),
          ),
        ),
      ],
    );
  }
  
  Widget _buildDiagnosticResults() {
    if (_diagnostics == null) return SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Diagnostic Results',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 12),
        
        // Server Health
        _buildResultCard(
          'Server Health',
          _diagnostics!.serverHealth.isReachable,
          _diagnostics!.serverHealth.message,
          details: [
            'Status Code: ${_diagnostics!.serverHealth.statusCode ?? 'N/A'}',
            if (_diagnostics!.serverHealth.responseTime != null)
              'Response Time: ${_diagnostics!.serverHealth.responseTime}ms',
            if (_diagnostics!.serverHealth.error != null)
              'Error: ${_diagnostics!.serverHealth.error}',
          ],
        ),
        
        SizedBox(height: 8),
        
        // Network Connectivity
        _buildResultCard(
          'Network Connectivity',
          _diagnostics!.networkConnectivity,
          _diagnostics!.networkConnectivity ? 'Internet connection is working' : 'No internet connection',
        ),
        
        SizedBox(height: 8),
        
        // Endpoint Tests
        Text(
          'Endpoint Tests',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        SizedBox(height: 4),
        
        ..._diagnostics!.endpointTests.map((test) => Padding(
          padding: EdgeInsets.only(bottom: 4),
          child: _buildResultCard(
            '${test.method} ${test.endpoint}',
            test.success,
            'Status: ${test.statusCode ?? 'N/A'}',
            details: [
              if (test.responseTime != null) 'Response Time: ${test.responseTime}ms',
              if (test.error != null) 'Error: ${test.error}',
            ],
          ),
        )),
      ],
    );
  }
  
  Widget _buildResultCard(String title, bool success, String message, {List<String>? details}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: success ? Colors.green.shade50 : Colors.red.shade50,
        border: Border.all(
          color: success ? Colors.green.shade200 : Colors.red.shade200,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                success ? Icons.check_circle : Icons.error,
                color: success ? Colors.green : Colors.red,
                size: 16,
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: success ? Colors.green.shade800 : Colors.red.shade800,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4),
          Text(
            message,
            style: TextStyle(
              fontSize: 12,
              color: success ? Colors.green.shade700 : Colors.red.shade700,
            ),
          ),
          if (details != null && details.isNotEmpty) ...[
            SizedBox(height: 4),
            ...details.map((detail) => Text(
              detail,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
                fontFamily: 'monospace',
              ),
            )),
          ],
        ],
      ),
    );
  }
}

/// Simple floating action button for quick diagnostics
class QuickDiagnosticButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => Dialog(
            child: Container(
              constraints: BoxConstraints(maxWidth: 500, maxHeight: 600),
              child: ApiDiagnosticWidget(),
            ),
          ),
        );
      },
      child: Icon(Icons.network_check),
      tooltip: 'Run API Diagnostics',
    );
  }
}
