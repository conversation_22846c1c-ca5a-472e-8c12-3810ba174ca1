import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../core/constants/app_colors.dart';
import '../core/styles.dart';

class InputFormField extends StatefulWidget {
  final TextEditingController controller;
  final String? label;
  final String? hint;
  final String? Function(String?)? validator;
  final TextInputType inputType;
  final bool isPassword;
  final IconData? icon;
  final Function(String)? onChanged;
  final TextInputAction inputAction;
  final FocusNode? focusNode;
  final VoidCallback? onEditingComplete;
  final Function(String)? onFieldSubmitted;
  final int? maxLength;
  final bool autoFocus;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final bool readOnly;
  final bool enableSuggestions;
  final List<TextInputFormatter>? inputFormatters;
  final TextAlign? textAlign;
  final InputDecoration? decoration;
  final bool unfocusOnOutsideTap;

  const InputFormField(
      {super.key,
      required this.controller,
      this.label,
      this.hint,
      this.validator,
      this.inputType = TextInputType.text,
      this.isPassword = false,
      this.icon,
      this.onChanged,
      this.inputAction = TextInputAction.done,
      this.focusNode,
      this.onEditingComplete,
      this.onFieldSubmitted,
      this.suffixIcon,
      this.maxLength,
      this.autoFocus = false,
      this.readOnly = false,
      this.inputFormatters,
      this.decoration,
      this.textAlign,
      this.enableSuggestions = true,
      this.prefixIcon,
      this.unfocusOnOutsideTap = true});

  @override
  State<InputFormField> createState() => _InputFormFieldState();
}

class _InputFormFieldState extends State<InputFormField> {
  String? _errorText;
  late FocusNode _focusNode;
  late bool _obscureText;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _obscureText = widget.isPassword;

    _focusNode.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _toggleVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 2.0, bottom: 2.0),
      child: SizedBox(
        height: _errorText != null ? 60 : 45,
        child: TextFormField(
          controller: widget.controller,
          enableSuggestions: widget.enableSuggestions,
          keyboardType: widget.inputType,
          obscureText: _obscureText,
          autofocus: widget.autoFocus,
          readOnly: widget.readOnly,
          focusNode: _focusNode,
          textInputAction: widget.inputAction,
          inputFormatters: widget.inputFormatters,
          textAlign: widget.textAlign ?? TextAlign.start,
          maxLength: widget.maxLength,
          onChanged: widget.onChanged,
          onTapOutside: (event) {
            if (widget.unfocusOnOutsideTap && _focusNode.hasFocus) {
              _focusNode.unfocus();
            }
          },
          onEditingComplete: widget.onEditingComplete,
          onFieldSubmitted: widget.onFieldSubmitted,
          validator: (value) {
            final error = widget.validator?.call(value);
            setState(() {
              _errorText = error;
            });
            return error;
          },
          style: black16w500,
          decoration: widget.decoration ??
              InputDecoration(
                fillColor: AppColors.surface,
                focusColor: AppColors.surface,
                filled: true,
                prefix: widget.prefixIcon,
                contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                labelText: widget.label,
                hintText: widget.hint,
                labelStyle: labelStyle,
                hintStyle: hitStyle,
                prefixIcon: widget.icon != null ? Icon(widget.icon) : null,
                suffixIcon: widget.isPassword
                    ? IconButton(
                        icon: Icon(
                          _obscureText ? Icons.visibility_off : Icons.visibility,
                          color: Colors.grey,
                        ),
                        onPressed: _toggleVisibility,
                      )
                    : widget.suffixIcon,
                counterText:
                    widget.maxLength != null ? '${widget.controller.text.length}/${widget.maxLength}' : null,
                border: OutlineInputBorder(
                  borderSide: const BorderSide(color: Colors.grey, width: 1.0),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: Colors.red, width: 1.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: Colors.grey, width: 1.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: AppColors.primary, width: 1.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: Colors.redAccent, width: 1.0),
                ),
                errorStyle: const TextStyle(color: Colors.red),
              ),
        ),
      ),
    );
  }
}
