import 'package:coffee_cofe/core/routes/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sqlite_viewer2/sqlite_viewer.dart';

import '../core/constants/app_colors.dart';
import '../export_screen.dart';
import '../features/discount/discount_settings.dart';
import '../screens/database_export_screen.dart';
import '../screens/gst_configuration_screen.dart';
import '../screens/enhanced_sales_report_screen.dart';
import 'menu_holder_widget.dart';

class CommonDrawer extends StatefulWidget {
  const CommonDrawer({super.key});

  @override
  State<CommonDrawer> createState() => _CommonDrawerState();
}

class _CommonDrawerState extends State<CommonDrawer> {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const SizedBox(
            height: 100, // You can adjust this height as needed
            child: DrawerHeader(
              decoration: BoxDecoration(color: AppColors.primary),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Welcome!',
                  style: TextStyle(color: Colors.white, fontSize: 20),
                ),
              ),
            ),
          ),
          DrawerExpandableItem(
            icon: Icons.currency_rupee_outlined,
            title: 'Billing',
            onTap: () => Get.toNamed(AppRoutes.billingScreen),
          ),
          DrawerExpandableItem(
            icon: Icons.production_quantity_limits_sharp,
            title: 'Products',
            onTap: () => Get.toNamed(AppRoutes.productsScreen),
          ),
          DrawerExpandableItem(
            icon: Icons.document_scanner_outlined,
            title: 'Report',
            onTap: () {},
            subMenus: [
              DrawerExpandableItem(
                icon: Icons.analytics_outlined,
                title: 'Sales Report',
                onTap: () => Get.toNamed(AppRoutes.salesReportScreen),
              ),
              DrawerExpandableItem(
                icon: Icons.production_quantity_limits_outlined,
                title: 'Product Report',
                onTap: () => Get.toNamed(AppRoutes.productReportScreen),
              ),
            ],
          ),
          DrawerExpandableItem(
            icon: Icons.inventory_2_outlined,
            title: 'Inventory',
            onTap: () => Get.toNamed(AppRoutes.inventoryScreen),
          ),
          DrawerExpandableItem(
            icon: Icons.document_scanner_outlined,
            title: 'Discount ',
            onTap: () {},
            subMenus: [
              DrawerExpandableItem(
                  icon: Icons.analytics_outlined,
                  title: 'Discount Settings',
                  onTap: () => {
                        Navigator.push(
                                context, MaterialPageRoute(builder: (context) => const DiscountSettings()))
                            .then((value) {})
                      }),
              DrawerExpandableItem(
                icon: Icons.production_quantity_limits_outlined,
                title: 'Product Report',
                onTap: () => Get.toNamed(AppRoutes.productReportScreen),
              ),
            ],
          ),
          DrawerExpandableItem(
            icon: Icons.verified_user,
            title: 'Profile',
            onTap: () => Get.toNamed(AppRoutes.profileScreen),
          ),
          DrawerExpandableItem(
            icon: Icons.group_outlined,
            title: 'Staffs & Attendance',
            onTap: () => Get.toNamed(AppRoutes.attendanceScreen),
            subMenus: [
              DrawerExpandableItem(
                icon: Icons.badge_outlined,
                title: 'Staffs',
                onTap: () => Get.toNamed(AppRoutes.employeeListScreen),
              ),
              DrawerExpandableItem(
                icon: Icons.add_location_alt_outlined,
                title: 'Attendance',
                onTap: () => Get.toNamed(AppRoutes.attendanceScreen),
              ),
            ],
          ),
          DrawerExpandableItem(
            icon: Icons.receipt_long,
            title: 'GST & Tax',
            onTap: () {},
            subMenus: [
              DrawerExpandableItem(
                icon: Icons.settings,
                title: 'GST Configuration',
                onTap: () => Get.to(() => const GSTConfigurationScreen()),
              ),
              DrawerExpandableItem(
                icon: Icons.analytics,
                title: 'Enhanced Reports',
                onTap: () => Get.to(() => const EnhancedSalesReportScreen()),
              ),
            ],
          ),
          DrawerExpandableItem(
            icon: Icons.storage_outlined,
            title: 'Database',
            onTap: () {},
            subMenus: [
              DrawerExpandableItem(
                icon: Icons.list_alt,
                title: 'Database List',
                onTap: () => Get.to(() => const DatabaseList()),
              ),
              DrawerExpandableItem(
                icon: Icons.file_copy_outlined,
                title: 'Export DB File',
                onTap: () => Get.to(() => const ExportDbScreen()),
              ),
              DrawerExpandableItem(
                icon: Icons.schema_outlined,
                title: 'Export Schema',
                onTap: () => Get.to(() => const DatabaseExportScreen()),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
