import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_export_utility.dart';
import '../screens/database_export_screen.dart';
import '../utils/schema_share_utility.dart';

/// Quick access widget for database export functionality
/// Can be embedded in settings screens or dashboard
class DatabaseExportQuickWidget extends StatefulWidget {
  final bool showTitle;
  final bool compact;

  const DatabaseExportQuickWidget({
    super.key,
    this.showTitle = true,
    this.compact = false,
  });

  @override
  State<DatabaseExportQuickWidget> createState() => _DatabaseExportQuickWidgetState();
}

class _DatabaseExportQuickWidgetState extends State<DatabaseExportQuickWidget> {
  bool _isExporting = false;
  int _exportCount = 0;
  String? _lastExportDate;

  @override
  void initState() {
    super.initState();
    _loadExportInfo();
  }

  Future<void> _loadExportInfo() async {
    try {
      final files = await DatabaseExportUtility.getExportedFiles();
      setState(() {
        _exportCount = files.length;
        if (files.isNotEmpty) {
          final lastFile = files.first;
          final stat = lastFile.statSync();
          _lastExportDate = DateFormat('MMM dd, yyyy').format(stat.modified);
        }
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _quickExport({bool includeData = false}) async {
    setState(() {
      _isExporting = true;
    });

    try {
      await DatabaseExportUtility.exportSchemaWithDate(includeData: includeData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                includeData ? 'Database exported with data successfully!' : 'Schema exported successfully!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        await _loadExportInfo();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  void _openFullExportScreen() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const DatabaseExportScreen(),
          ),
        )
        .then((_) => _loadExportInfo());
  }

  @override
  Widget build(BuildContext context) {
    if (widget.compact) {
      return _buildCompactWidget();
    }

    return _buildFullWidget();
  }

  Widget _buildCompactWidget() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            const Icon(Icons.backup, color: Colors.blue),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Database Export',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    _exportCount > 0 ? '$_exportCount exports • Last: $_lastExportDate' : 'No exports yet',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
            if (_isExporting)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'schema':
                      _quickExport(includeData: false);
                      break;
                    case 'full':
                      _quickExport(includeData: true);
                      break;
                    case 'share':
                      SchemaShareUtility.showShareOptionsDialog(context);
                      break;
                    case 'manage':
                      _openFullExportScreen();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'schema',
                    child: Row(
                      children: [
                        Icon(Icons.schema, size: 18),
                        SizedBox(width: 8),
                        Text('Export Schema'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'full',
                    child: Row(
                      children: [
                        Icon(Icons.storage, size: 18),
                        SizedBox(width: 8),
                        Text('Export with Data'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share, size: 18, color: Colors.orange),
                        SizedBox(width: 8),
                        Text('Share Schema'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'manage',
                    child: Row(
                      children: [
                        Icon(Icons.settings, size: 18),
                        SizedBox(width: 8),
                        Text('Manage Exports'),
                      ],
                    ),
                  ),
                ],
                child: const Icon(Icons.more_vert),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullWidget() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showTitle) ...[
              const Row(
                children: [
                  Icon(Icons.backup, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'Database Export',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],

            // Export info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Total Exports: $_exportCount',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        if (_lastExportDate != null)
                          Text(
                            'Last Export: $_lastExportDate',
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: _openFullExportScreen,
                    child: const Text('Manage'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Quick export buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isExporting ? null : () => _quickExport(includeData: false),
                    icon: const Icon(Icons.schema, size: 18),
                    label: const Text('Schema'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isExporting ? null : () => _quickExport(includeData: true),
                    icon: const Icon(Icons.storage, size: 18),
                    label: const Text('Full'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Share button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isExporting ? null : () => SchemaShareUtility.showShareOptionsDialog(context),
                icon: const Icon(Icons.share, size: 18),
                label: const Text('Share Schema'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                  side: const BorderSide(color: Colors.orange),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),

            if (_isExporting) ...[
              const SizedBox(height: 8),
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Exporting...',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
