import 'package:coffee_cofe/core/styles.dart';
import 'package:flutter/material.dart';

import '../core/constants/app_colors.dart';

class DateTimePicker extends StatelessWidget {
  final String? label;
  final DateTime? selectedDate;
  final DateTime? minDate;
  final DateTime? maxDate;
  final bool showDate;
  final bool showTime;
  final ValueChanged<DateTime> onChanged;

  const DateTimePicker({
    super.key,
    this.label,
    this.selectedDate,
    this.minDate,
    this.maxDate,
    this.showDate = true,
    this.showTime = false,
    required this.onChanged,
  });

  Future<void> _pickDateTime(BuildContext context) async {
    DateTime? date = selectedDate ?? DateTime.now();

    if (showDate) {
      final pickedDate = await showDatePicker(
        context: context,
        initialDate: date,
        firstDate: minDate ?? DateTime(2000),
        lastDate: maxDate ?? DateTime(2100),
      );

      if (pickedDate == null) return;

      date = DateTime(
        pickedDate.year,
        pickedDate.month,
        pickedDate.day,
        date.hour,
        date.minute,
      );
    }

    if (showTime) {
      final pickedTime = await showTimePicker(
        // ignore: use_build_context_synchronously
        context: context,
        initialTime: TimeOfDay.fromDateTime(date),
      );

      if (pickedTime == null) return;

      date = DateTime(
        date.year,
        date.month,
        date.day,
        pickedTime.hour,
        pickedTime.minute,
      );
    }

    onChanged(date);
  }

  String _formatDateTime(DateTime date) {
    final time = TimeOfDay.fromDateTime(date);
    final timeString =
        "${time.hourOfPeriod.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} ${time.period == DayPeriod.am ? 'AM' : 'PM'}";

    final dateString = "${date.day.toString().padLeft(2, '0')}/"
        "${date.month.toString().padLeft(2, '0')}/"
        "${date.year}";

    if (showDate && showTime) {
      return "$dateString $timeString";
    } else if (showDate) {
      return dateString;
    } else {
      return timeString;
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayText = selectedDate != null
        ? _formatDateTime(selectedDate!)
        : "Select ${showDate && showTime ? 'date & time' : showDate ? 'date' : 'time'}";

    return GestureDetector(
      onTap: () => _pickDateTime(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.textTertiary,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              displayText,
              style: black16w500,
            ),
            Icon(
              showTime && !showDate ? Icons.access_time : Icons.calendar_today,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }
}
