// widgets/employee_card.dart
import 'dart:io';

import 'package:flutter/material.dart';

import '../models/add_employee_details_model.dart';

class EmployeeCard extends StatelessWidget {
  final Employee employee;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const EmployeeCard({
    super.key,
    required this.employee,
    required this.onTap,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Image
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: employee.profileImagePath != null
                        ? FileImage(File(employee.profileImagePath!))
                        : null,
                    child: employee.profileImagePath == null ? const Icon(Icons.person, size: 30) : null,
                  ),
                  const SizedBox(width: 16),
                  // Employee Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${employee.firstName} ${employee.lastName}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(employee.role),
                        const SizedBox(height: 4),
                        Text(employee.email),
                        const SizedBox(height: 4),
                        Text(employee.phoneNumber),
                      ],
                    ),
                  ),
                  // Delete Button
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: onDelete,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('ID: ${employee.id}'),
            ],
          ),
        ),
      ),
    );
  }
}
