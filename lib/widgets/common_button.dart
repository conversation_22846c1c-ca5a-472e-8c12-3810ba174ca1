import 'package:flutter/material.dart';
import 'package:coffee_cofe/core/styles.dart';

class CommonButton extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final bool isLoading;
  final IconData? icon;
  final double borderRadius;
  final Color backgroundColor;
  final double? width;
  final TextStyle? textStyle;

  final bool isOutlined; // ✨ New
  final Color? borderColor; // ✨ New

  const CommonButton({
    super.key,
    required this.title,
    required this.onTap,
    this.isLoading = false,
    this.icon,
    this.borderRadius = 8.0,
    this.backgroundColor = Colors.orange,
    this.width,
    this.textStyle,
    this.isOutlined = false, // ✨ Default false
    this.borderColor, // ✨ Optional
  });

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = textStyle?.color ?? (isOutlined ? backgroundColor : Colors.white);

    return SizedBox(
      height: 45,
      width: width ?? MediaQuery.of(context).size.width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: isOutlined ? BorderSide(color: borderColor ?? backgroundColor, width: 1.5) : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          child: isLoading
              ? SizedBox(
                  key: const ValueKey('loading'),
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: effectiveTextColor,
                  ),
                )
              : Row(
                  key: const ValueKey('content'),
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (icon != null)
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Icon(icon, color: effectiveTextColor),
                      ),
                    Text(
                      title,
                      style: textStyle ?? white16w500.copyWith(color: effectiveTextColor),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
