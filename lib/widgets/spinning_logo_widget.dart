import 'package:flutter/material.dart';

import '../core/constants/images.dart'; 

 

class Animated<PERSON><PERSON><PERSON>ogo extends StatefulWidget {
  const AnimatedBrandLogo({super.key});

  @override
  State<AnimatedBrandLogo> createState() => _SlideFadeLogoState();
}

class _SlideFadeLogoState extends State<AnimatedBrandLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..repeat(reverse: true); // Ping-pong animation

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-0.3, 0), // Start a bit left
      end: const Offset(0.0, 0),    // End at normal position
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.4,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Image.asset(
          brandLogo,
          width: 40,
          height: 40,
        ),
      ),
    );
  }
}
