import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SimplifiedDiscountWidget extends StatefulWidget {
  final Function(double percentage) onManualDiscountApplied;
  final Function() onDiscountRemoved;
  final double currentDiscountPercentage;
  final bool hasDiscount;

  const SimplifiedDiscountWidget({
    super.key,
    required this.onManualDiscountApplied,
    required this.onDiscountRemoved,
    this.currentDiscountPercentage = 0.0,
    this.hasDiscount = false,
  });

  @override
  State<SimplifiedDiscountWidget> createState() => _SimplifiedDiscountWidgetState();
}

class _SimplifiedDiscountWidgetState extends State<SimplifiedDiscountWidget> {
  final TextEditingController _percentageController = TextEditingController();
  bool _isApplying = false;

  @override
  void initState() {
    super.initState();
    if (widget.currentDiscountPercentage > 0) {
      _percentageController.text = widget.currentDiscountPercentage.toString();
    }
  }

  @override
  void dispose() {
    _percentageController.dispose();
    super.dispose();
  }

  void _applyDiscount() async {
    if (_isApplying) return;

    setState(() {
      _isApplying = true;
    });

    try {
      // Apply manual percentage discount
      final percentage = double.tryParse(_percentageController.text.trim()) ?? 0.0;
      if (percentage > 0 && percentage <= 100) {
        widget.onManualDiscountApplied(percentage);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${percentage.toStringAsFixed(1)}% discount applied'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please enter a valid percentage (1-100)'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      setState(() {
        _isApplying = false;
      });
    }
  }

  void _removeDiscount() {
    _percentageController.clear();
    widget.onDiscountRemoved();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Discount removed'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with remove button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Discount',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (widget.hasDiscount)
                IconButton(
                  onPressed: _removeDiscount,
                  icon: const Icon(Icons.close, color: Colors.red),
                  tooltip: 'Remove discount',
                ),
            ],
          ),

          const SizedBox(height: 8),

          // Manual percentage input
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _percentageController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                  ],
                  decoration: InputDecoration(
                    labelText: 'Discount (%)',
                    hintText: '10',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) {
                    // Validate percentage range
                    final percentage = double.tryParse(value) ?? 0.0;
                    if (percentage > 100) {
                      _percentageController.text = '100';
                      _percentageController.selection = TextSelection.fromPosition(
                        TextPosition(offset: _percentageController.text.length),
                      );
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Container(
                height: 56,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    colors: [Colors.blue, Colors.blueAccent],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: ElevatedButton(
                  onPressed: _isApplying ? null : _applyDiscount,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                  ),
                  child: _isApplying
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Apply',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ],
          ),

          // Current discount display
          if (widget.hasDiscount) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green[50]!, Colors.green[100]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[300]!, width: 1.5),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green[600],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.discount,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Discount Applied',
                          style: TextStyle(
                            color: Colors.green[800],
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          '${widget.currentDiscountPercentage.toStringAsFixed(1)}% off',
                          style: TextStyle(
                            color: Colors.green[700],
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
