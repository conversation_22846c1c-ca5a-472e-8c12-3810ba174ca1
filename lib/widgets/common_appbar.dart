import 'dart:io';

import 'package:coffee_cofe/core/routes/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../core/constants/app_colors.dart';
import '../core/styles.dart';
import '../features/profile/profile_settings_controller.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Color? appBarColor;
  final List<Widget>? actions;
  final bool isEnableBrandLogo;
  const CommonAppBar({
    super.key,
    required this.title,
    this.actions,
    this.appBarColor,
    this.isEnableBrandLogo = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.isRegistered<ProfileController>()
        ? Get.find<ProfileController>()
        : Get.put<ProfileController>(ProfileController());

    return AppBar(
      backgroundColor: appBarColor,
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.menu, color: Colors.white),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
        ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: black18w700.copyWith(
              color: appBarColor != AppColors.secondary ? AppColors.surface : AppColors.textPrimary,
            ),
          ),
        ],
      ),
      actions: [
        ...?actions,
        Padding(
          padding: const EdgeInsets.only(right: 12.0),
          child: GestureDetector(
              onTap: () {
                if (Get.currentRoute == AppRoutes.profileScreen) {
                  Get.back();
                } else {
                  Get.toNamed(AppRoutes.profileScreen);
                }
              },
              child: Obx(
                () => CircleAvatar(
                  backgroundColor: AppColors.appBarBackground,
                  radius: 18,
                  backgroundImage: controller.profilePath.value.startsWith('assets/')
                      ? AssetImage(controller.profilePath.value) as ImageProvider
                      : FileImage(File(controller.profilePath.value)),
                ),
              )),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
