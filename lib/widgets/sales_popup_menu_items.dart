import 'package:flutter/material.dart';

class PopupMenuExample extends StatelessWidget {
  final Function() onEdit;
  final Function() onDelete;

  const PopupMenuExample({super.key, required this.onEdit, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<int>(
      icon: const Icon(Icons.more_vert), // Three dots menu
      onSelected: (value) {
        if (value == 1) {
          onEdit(); // Handle Edit Action
        } else if (value == 2) {
          onDelete(); // Handle Delete Action
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 1,
          child: Row(
            children: [
              Icon(Icons.edit, color: Colors.blue),
              SizedBox(width: 10),
              Text("Edit"),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 2,
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 10),
              Text("Delete"),
            ],
          ),
        ),
      ],
    );
  }
}
