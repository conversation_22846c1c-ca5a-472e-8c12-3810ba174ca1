import 'package:flutter/material.dart';
import 'package:pie_menu/pie_menu.dart';

class PieMenuIcon extends StatelessWidget {
  final void Function(String action) onSelected;

  const PieMenuIcon(this.onSelected, {super.key});

  @override
  Widget build(BuildContext context) {
    final pieController = PieMenuController();

    return PieMenu(
      controller: pieController,
      actions: [
        PieAction(
          tooltip: const Text('Like'),
          child: const Icon(Icons.favorite),
          onSelect: () => onSelected('like'),
        ),
        PieAction(
          tooltip: const Text('Comment'),
          child: const Icon(Icons.comment),
          onSelect: () => onSelected('comment'),
        ),
        PieAction(
          tooltip: const Text('Bookmark'),
          child: const Icon(Icons.bookmark),
          onSelect: () => onSelected('bookmark'),
        ),
        PieAction(
          tooltip: const Text('Share'),
          child: const Icon(Icons.share),
          onSelect: () => onSelected('share'),
        ),
      ],
      child: IconButton(
        icon: const Icon(Icons.more_vert),
        onPressed: () => pieController.openMenu(menuAlignment: Alignment.centerLeft),
      ),
    );
  }
}
