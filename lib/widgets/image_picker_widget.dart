// widgets/image_picker.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerWidget extends StatefulWidget {
  final Function(File?) onImageSelected;
  final String? initialImagePath;

  const ImagePickerWidget({
    super.key,
    required this.onImageSelected,
    this.initialImagePath,
  });

  @override
  State<ImagePickerWidget> createState() => _ImagePickerWidgetState();
}

class _ImagePickerWidgetState extends State<ImagePickerWidget> {
  File? _pickedImage;

  @override
  void initState() {
    super.initState();
    if (widget.initialImagePath != null) {
      _pickedImage = File(widget.initialImagePath!);
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _pickedImage = File(pickedFile.path);
      });
      widget.onImageSelected(_pickedImage);
    } else {
      widget.onImageSelected(null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: _pickImage,
          child: CircleAvatar(
            radius: 50,
            backgroundImage: _pickedImage != null
                ? FileImage(_pickedImage!)
                : widget.initialImagePath != null
                    ? FileImage(File(widget.initialImagePath!))
                    : null,
            child: _pickedImage == null && widget.initialImagePath == null
                ? const Icon(Icons.person, size: 50)
                : null,
          ),
        ),
        TextButton(
          onPressed: _pickImage,
          child: const Text('Add Profile Picture'),
        ),
      ],
    );
  }
}
