import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Dynamic tax rate selector widget for professional GST management
/// Allows staff to change tax rates for specific sales
class DynamicTaxSelector extends StatefulWidget {
  final double currentTaxRate;
  final String productName;
  final double productAmount;
  final Function(double newTaxRate, String reason) onTaxRateChanged;
  final bool isEnabled;

  const DynamicTaxSelector({
    super.key,
    required this.currentTaxRate,
    required this.productName,
    required this.productAmount,
    required this.onTaxRateChanged,
    this.isEnabled = true,
  });

  @override
  State<DynamicTaxSelector> createState() => _DynamicTaxSelectorState();
}

class _DynamicTaxSelectorState extends State<DynamicTaxSelector> {
  late double _selectedTaxRate;
  final _customRateController = TextEditingController();
  final _reasonController = TextEditingController();
  String _selectedReason = 'Custom rate for this sale';

  // Predefined tax rates for quick selection
  final List<Map<String, dynamic>> _predefinedRates = [
    {'rate': 0.0, 'label': 'GST Free (0%)', 'description': 'No GST applicable'},
    {'rate': 5.0, 'label': 'GST 5%', 'description': 'Essential goods/services'},
    {'rate': 12.0, 'label': 'GST 12%', 'description': 'Standard goods'},
    {'rate': 18.0, 'label': 'GST 18%', 'description': 'Standard services'},
    {'rate': 28.0, 'label': 'GST 28%', 'description': 'Luxury items'},
  ];

  final List<String> _predefinedReasons = [
    'Custom rate for this sale',
    'Special customer discount',
    'Promotional pricing',
    'Bulk order adjustment',
    'Manager override',
    'System correction',
    'Other (specify below)',
  ];

  @override
  void initState() {
    super.initState();
    _selectedTaxRate = widget.currentTaxRate;
    _customRateController.text = widget.currentTaxRate.toString();
  }

  @override
  void dispose() {
    _customRateController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  void _showTaxRateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Change Tax Rate - ${widget.productName}'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current product info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Product: ${widget.productName}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text('Amount: ₹${widget.productAmount.toStringAsFixed(2)}'),
                      Text('Current Tax: ${widget.currentTaxRate}%'),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Predefined rates
                const Text(
                  'Quick Select:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                ..._predefinedRates.map((rateInfo) => RadioListTile<double>(
                      title: Text(rateInfo['label']),
                      subtitle: Text(rateInfo['description']),
                      value: rateInfo['rate'],
                      groupValue: _selectedTaxRate,
                      onChanged: (value) {
                        setState(() {
                          _selectedTaxRate = value!;
                          _customRateController.text = value.toString();
                        });
                      },
                    )),

                const SizedBox(height: 16),

                // Custom rate input
                const Text(
                  'Custom Rate:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                TextFormField(
                  controller: _customRateController,
                  decoration: const InputDecoration(
                    labelText: 'Tax Rate (%)',
                    border: OutlineInputBorder(),
                    suffixText: '%',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  onChanged: (value) {
                    final rate = double.tryParse(value);
                    if (rate != null && rate >= 0 && rate <= 100) {
                      setState(() {
                        _selectedTaxRate = rate;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),

                // Reason selection
                const Text(
                  'Reason for Change:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                DropdownButtonFormField<String>(
                  value: _selectedReason,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: _predefinedReasons
                      .map(
                        (reason) => DropdownMenuItem(
                          value: reason,
                          child: Text(reason),
                        ),
                      )
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedReason = value!;
                    });
                  },
                ),

                if (_selectedReason == 'Other (specify below)') ...[
                  const SizedBox(height: 12),
                  TextFormField(
                    controller: _reasonController,
                    decoration: const InputDecoration(
                      labelText: 'Specify reason',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                ],

                const SizedBox(height: 16),

                // Tax calculation preview
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Tax Calculation Preview:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text('Base Amount: ₹${widget.productAmount.toStringAsFixed(2)}'),
                      Text('Tax Rate: ${_selectedTaxRate.toStringAsFixed(2)}%'),
                      Text(
                          'Tax Amount: ₹${(widget.productAmount * _selectedTaxRate / 100).toStringAsFixed(2)}'),
                      Text(
                        'Total: ₹${(widget.productAmount + (widget.productAmount * _selectedTaxRate / 100)).toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _applyTaxRateChange,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.brown,
              foregroundColor: Colors.white,
            ),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _applyTaxRateChange() {
    final rate = double.tryParse(_customRateController.text);
    if (rate == null || rate < 0 || rate > 100) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid tax rate between 0 and 100'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    String finalReason = _selectedReason;
    if (_selectedReason == 'Other (specify below)') {
      if (_reasonController.text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please specify the reason for tax rate change'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
      finalReason = _reasonController.text.trim();
    }

    widget.onTaxRateChanged(rate, finalReason);
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Tax rate updated to ${rate.toStringAsFixed(2)}%'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool hasCustomRate = widget.currentTaxRate != 18.0; // Assuming 18% is default

    return InkWell(
      onTap: widget.isEnabled ? _showTaxRateDialog : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: hasCustomRate ? Colors.orange.shade100 : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: hasCustomRate ? Colors.orange : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              hasCustomRate ? Icons.edit : Icons.percent,
              size: 16,
              color: hasCustomRate ? Colors.orange.shade700 : Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.currentTaxRate.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: hasCustomRate ? FontWeight.bold : FontWeight.normal,
                color: hasCustomRate ? Colors.orange.shade700 : Colors.grey.shade700,
              ),
            ),
            if (widget.isEnabled) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.arrow_drop_down,
                size: 16,
                color: Colors.grey.shade600,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
