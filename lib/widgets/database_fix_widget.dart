import 'package:flutter/material.dart';
import 'dart:developer';
import '../database/immediate_fix.dart';

/// Widget that automatically fixes database issues when the app starts
class DatabaseFixWidget extends StatefulWidget {
  final Widget child;

  const DatabaseFixWidget({super.key, required this.child});

  @override
  State<DatabaseFixWidget> createState() => _DatabaseFixWidgetState();
}

class _DatabaseFixWidgetState extends State<DatabaseFixWidget> {
  bool _isFixing = false;
  bool _fixCompleted = false;
  String _status = 'Checking database...';

  @override
  void initState() {
    super.initState();
    _checkAndFixDatabase();
  }

  Future<void> _checkAndFixDatabase() async {
    try {
      setState(() {
        _isFixing = true;
        _status = 'Checking database schema...';
      });

      // Check if fix is needed
      final needsFix = await ImmediateDatabaseFix.isFixNeeded();

      if (needsFix) {
        setState(() {
          _status = 'Fixing database schema...';
        });

        // Run the fix
        await ImmediateDatabaseFix.fixNow();

        setState(() {
          _status = 'Database fixed successfully!';
        });

        // Wait a moment to show success message
        await Future.delayed(Duration(seconds: 1));
      } else {
        setState(() {
          _status = 'Database is already up to date';
        });

        // Wait a moment
        await Future.delayed(Duration(milliseconds: 500));
      }

      setState(() {
        _fixCompleted = true;
        _isFixing = false;
      });
    } catch (e) {
      log('Database fix error: $e');
      setState(() {
        _status = 'Database fix failed: $e';
        _fixCompleted = true;
        _isFixing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_fixCompleted) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                _status,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              if (_isFixing) ...[
                const SizedBox(height: 8),
                const Text(
                  'Please wait...',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}

/// Simple button widget to manually trigger database fix
class DatabaseFixButton extends StatefulWidget {
  const DatabaseFixButton({super.key});

  @override
  State<DatabaseFixButton> createState() => _DatabaseFixButtonState();
}

class _DatabaseFixButtonState extends State<DatabaseFixButton> {
  bool _isFixing = false;
  String _result = '';

  Future<void> _runFix() async {
    setState(() {
      _isFixing = true;
      _result = '';
    });

    try {
      await ImmediateDatabaseFix.fixNow();
      setState(() {
        _result = '✅ Database fixed successfully!';
      });
    } catch (e) {
      setState(() {
        _result = '❌ Fix failed: $e';
      });
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton(
          onPressed: _isFixing ? null : _runFix,
          child: _isFixing
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    const SizedBox(width: 8),
                    const Text('Fixing...'),
                  ],
                )
              : const Text('🔧 Fix Database'),
        ),
        if (_result.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            _result,
            style: TextStyle(
              color: _result.startsWith('✅') ? Colors.green : Colors.red,
            ),
          ),
        ],
      ],
    );
  }
}
