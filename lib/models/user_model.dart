class LoginResponse {
  final int? status;
  final String? message;
  final User? response;

  LoginResponse({
    this.status,
    this.message,
    this.response,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      status: json['status'],
      message: json['message'],
      response: json['response'] != null ? User.fromJson(json['response']) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'response': response?.toJson(),
      };
}

class User {
  final int? id;
  final String? userName;
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? mobileNo;
  final String? emailAddress;
  final String? token;
  final String? workspaceId;
  final String? workspaceName;
  final String? role;

  User({
    this.id,
    this.userName,
    this.userId,
    this.firstName,
    this.lastName,
    this.mobileNo,
    this.emailAddress,
    this.token,
    this.workspaceId,
    this.workspaceName,
    this.role,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      userName: json['user_name'],
      userId: json['user_id'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      mobileNo: json['mobile_no'],
      emailAddress: json['email_address'],
      token: json['token'],
      workspaceId: json['workspace_id'] ?? json['workspaceId'],
      workspaceName: json['workspace_name'] ?? json['workspaceName'],
      role: json['role'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_name': userName,
        'user_id': userId,
        'first_name': firstName,
        'last_name': lastName,
        'mobile_no': mobileNo,
        'email_address': emailAddress,
        'token': token,
        'workspace_id': workspaceId,
        'workspace_name': workspaceName,
        'role': role,
      };
}

class Workspace {
  String? userRole;
  String? workspaceId;
  String? workspaceKey;
  String? workspaceValue;
  String? workspaceName;
  String? workspaceUserName;
  String? workspaceUserId;
  String? createdDate;
  String? status;

  Workspace(
      {this.userRole,
      this.workspaceId,
      this.workspaceName,
      this.createdDate,
      this.status,
      this.workspaceKey,
      this.workspaceValue,
      this.workspaceUserName,
      this.workspaceUserId});

  Workspace.fromJson(Map<String, dynamic> json) {
    userRole = json['user_role'];
    workspaceId = json['workspace_id'];
    workspaceName = json['workspace_name'];
    createdDate = json['created_date'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_role'] = userRole;
    data['workspace_id'] = workspaceId;
    data['workspace_name'] = workspaceName;
    data['created_date'] = createdDate;
    data['status'] = status;
    return data;
  }
}
