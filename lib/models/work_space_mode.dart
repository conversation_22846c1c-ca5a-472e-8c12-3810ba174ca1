class WorkspaceListResModel {
  int? status;
  String? message;
  List<WorkSpace>? result;

  WorkspaceListResModel({
    this.status,
    this.message,
    this.result,
  });

  factory WorkspaceListResModel.fromJson(Map<String, dynamic> json) => WorkspaceListResModel(
        status: json['status'] ?? 0,
        message: json['message'] ?? '',
        result: (json['result'] as List<dynamic>?)?.map((x) => WorkSpace.fromJson(x)).toList() ?? [],
      );

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'result': result?.map((x) => x.toJson()).toList(),
      };
}

class WorkSpace {
  int? id;
  String? workspaceId;
  String? workspaceName;
  int? userId;
  String? userName;
  bool? isActive;
  String? expiryDate;
  int? roleId;
  int? status;

  WorkSpace({
    this.id,
    this.workspaceId,
    this.workspaceName,
    this.userId,
    this.userName,
    this.isActive,
    this.expiryDate,
    this.roleId,
    this.status,
  });

  factory WorkSpace.fromJson(Map<String, dynamic> json) => WorkSpace(
        id: json['id'] ?? 0,
        workspaceId: json['workspaceID'] ?? '',
        workspaceName: json['workspaceName'] ?? '',
        userId: json['userID'] ?? 0,
        userName: json['userName'] ?? '',
        isActive: json['isActive'] ?? false,
        expiryDate: json['expiryDate'] ?? '',
        roleId: json['roleID'] ?? 0,
        status: json['status'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'workspaceID': workspaceId,
        'workspaceName': workspaceName,
        'userID': userId,
        'userName': userName,
        'isActive': isActive,
        'expiryDate': expiryDate,
        'roleID': roleId,
        'status': status,
      };
}
