class RegisterUserModel {
  String? userName;
  String? email;
  String? password;
  int? phoneNumber;
  String? role;

  RegisterUserModel({this.userName, this.email, this.password, this.phoneNumber, this.role});

  RegisterUserModel.fromJson(Map<String, dynamic> json) {
    userName = json['userName'];
    email = json['email'];
    password = json['password'];
    phoneNumber = json['phoneNumber'];
    role = json['role'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userName'] = userName;
    data['email'] = email;
    data['password'] = password;
    data['phoneNumber'] = phoneNumber;
    data['role'] = role;
    return data;
  }
}
