// models/employee.dart
class Employee {
  String id;
  String? profileImagePath;
  String firstName;
  String lastName;
  String email;
  String phoneNumber;
  String role;
  String dateOfBirth;
  String address;

  Employee({
    required this.id,
    this.profileImagePath,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phoneNumber,
    required this.role,
    required this.dateOfBirth,
    required this.address,
  });

  static String generateId() {
    final now = DateTime.now();
    return 'A${now.microsecondsSinceEpoch.toString().substring(0, 6)}';
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'profileImagePath': profileImagePath,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'role': role,
      'dateOfBirth': dateOfBirth,
      'address': address,
    };
  }

  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'],
      profileImagePath: map['profileImagePath'],
      firstName: map['firstName'],
      lastName: map['lastName'],
      email: map['email'],
      phoneNumber: map['phoneNumber'],
      role: map['role'],
      dateOfBirth: map['dateOfBirth'],
      address: map['address'],
    );
  }
}
