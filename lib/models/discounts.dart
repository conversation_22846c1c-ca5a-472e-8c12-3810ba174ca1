class Discounts {
  String? discountID;
  String? discountName;
  String? discountType;
  String? discountValue;
  String? discountFormula;
  String? minLimit;
  String? maxLimit;
  String? maxDiscount;
  String? fromDate;
  String? toDate;
  String? isActive;
  String? workspaceId;
  String? createdDate;
  String? updatedDate;
  String? syncStatus;

  // Additional properties used in the UI
  String? mode;
  String? discountOn;
  String? discount;
  String? minDiscount;
  int? rowStatus;
  int? discountSync;
  String? type;
  String? categoryID;
  String? formula;
  String? couponName;
  int? discountActive;
  String? workspaceID;
  String? createdBy;

  Discounts({
    this.discountID,
    this.discountName,
    this.discountType,
    this.discountValue,
    this.discountFormula,
    this.minLimit,
    this.maxLimit,
    this.maxDiscount,
    this.fromDate,
    this.toDate,
    this.isActive,
    this.workspaceId,
    this.createdDate,
    this.updatedDate,
    this.syncStatus,
    this.mode,
    this.discountOn,
    this.discount,
    this.minDiscount,
    this.rowStatus,
    this.discountSync,
    this.type,
    this.categoryID,
    this.formula,
    this.couponName,
    this.discountActive,
    this.workspaceID,
    this.createdBy,
  });

  factory Discounts.fromMap(Map<String, dynamic> map) {
    return Discounts(
      discountID: map['discountID']?.toString(),
      discountName: map['discountName']?.toString(),
      discountType: map['discountType']?.toString(),
      discountValue: map['discountValue']?.toString(),
      discountFormula: map['discountFormula']?.toString(),
      minLimit: map['minLimit']?.toString(),
      maxLimit: map['maxLimit']?.toString(),
      maxDiscount: map['maxDiscount']?.toString(),
      fromDate: map['fromDate']?.toString(),
      toDate: map['toDate']?.toString(),
      isActive: map['isActive']?.toString(),
      workspaceId: map['workspaceId']?.toString(),
      createdDate: map['createdDate']?.toString(),
      updatedDate: map['updatedDate']?.toString(),
      syncStatus: map['syncStatus']?.toString(),
      mode: map['mode']?.toString(),
      discountOn: map['discountOn']?.toString(),
      discount: map['discount']?.toString(),
      minDiscount: map['minDiscount']?.toString(),
      rowStatus: map['rowStatus'] as int?,
      discountSync: map['discountSync'] as int?,
      type: map['type']?.toString(),
      categoryID: map['categoryID']?.toString(),
      formula: map['formula']?.toString(),
      couponName: map['couponName']?.toString(),
      discountActive: map['discountActive'] as int?,
      workspaceID: map['workspaceID']?.toString(),
      createdBy: map['createdBy']?.toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'discountID': discountID,
      'discountName': discountName,
      'discountType': discountType,
      'discountValue': discountValue,
      'discountFormula': discountFormula,
      'minLimit': minLimit,
      'maxLimit': maxLimit,
      'maxDiscount': maxDiscount,
      'fromDate': fromDate,
      'toDate': toDate,
      'isActive': isActive,
      'workspaceId': workspaceId,
      'createdDate': createdDate,
      'updatedDate': updatedDate,
      'syncStatus': syncStatus,
    };
  }

  @override
  String toString() {
    return 'Discounts{discountID: $discountID, discountName: $discountName, discountType: $discountType}';
  }

  /// Validate discount conditions
  bool isValidForAmount(double amount) {
    final minAmount = double.tryParse(minDiscount ?? '0') ?? 0;
    return amount >= minAmount;
  }

  /// Check if discount is currently active
  bool get isCurrentlyActive {
    if (discountActive != 1) return false;

    final now = DateTime.now();

    if (fromDate != null) {
      final from = DateTime.tryParse(fromDate!);
      if (from != null && now.isBefore(from)) return false;
    }

    if (toDate != null) {
      final to = DateTime.tryParse(toDate!);
      if (to != null && now.isAfter(to)) return false;
    }

    return true;
  }

  /// Calculate discount amount
  double calculateDiscountAmount(double baseAmount) {
    final discountVal = double.tryParse(discount ?? '0') ?? 0;
    double calculatedDiscount = 0;

    if (discountOn == 'P') {
      // Percentage discount
      calculatedDiscount = (baseAmount * discountVal) / 100;
    } else {
      // Fixed amount discount
      calculatedDiscount = discountVal;
    }

    // Apply maximum discount limit
    if (maxDiscount != null && maxDiscount!.isNotEmpty) {
      final maxAmount = double.tryParse(maxDiscount!) ?? double.infinity;
      calculatedDiscount = calculatedDiscount > maxAmount ? maxAmount : calculatedDiscount;
    }

    // Ensure discount doesn't exceed the base amount
    return calculatedDiscount > baseAmount ? baseAmount : calculatedDiscount;
  }

  /// Increment usage count (for coupon tracking)
  void incrementUsage({String? userId}) {
    // This would typically update usage tracking in the database
    // For now, we'll just mark it as used in the sync status
    syncStatus = '1'; // Mark as needs sync
  }
}
