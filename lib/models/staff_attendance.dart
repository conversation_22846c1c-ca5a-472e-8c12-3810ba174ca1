import 'package:flutter/material.dart';

class AttendanceEmployee {
  final String? employeeId;
  final String? employeeName;
  int punchStatus; // 0 = not punched, 1 = in, 2 = out
  String? punchInfo;
  TimeOfDay? punchInTime;
  TimeOfDay? punchOutTime;
  String? profileImageUrl;
  String? role;

  AttendanceEmployee({
    this.employeeId,
    this.employeeName,
    required this.punchStatus,
    this.punchInfo,
    this.punchInTime,
    this.punchOutTime,
    this.profileImageUrl,
    this.role,
  });
}
