import 'dart:developer';
import 'package:sqflite/sqflite.dart';

/// Schema fix utility to ensure all required columns exist
class SchemaFix {
  /// Fix discount table schema to ensure all required columns exist
  static Future<void> fixDiscountTableSchema(Database db) async {
    try {
      log('Checking and fixing discount table schema...');

      // Get current table info
      final tableInfo = await db.rawQuery("PRAGMA table_info(Discount)");
      final existingColumns = tableInfo.map((row) => row['name'] as String).toSet();

      log('Existing discount table columns: $existingColumns');

      // Define required columns with their SQL definitions
      final requiredColumns = {
        'discountID': 'TEXT PRIMARY KEY',
        'discountName': 'TEXT', // Missing column that's causing the error
        'discountCouponName': 'TEXT',
        'couponCode': 'TEXT', // Alternative column name
        'couponName': 'TEXT', // Alternative column name
        'discountType': 'TEXT',
        'discountValue': 'TEXT',
        'discountFormula': 'TEXT',
        'discountMode': 'TEXT',
        'discountOn': 'TEXT',
        'discount': 'TEXT',
        'minLimit': 'TEXT',
        'maxLimit': 'TEXT',
        'minDiscount': 'TEXT',
        'maxDiscount': 'TEXT',
        'fromDate': 'TEXT',
        'toDate': 'TEXT',
        'isActive': 'TEXT',
        'formula': 'TEXT',
        'discountActive': 'INTEGER DEFAULT 1',
        'discountSync': 'INTEGER DEFAULT 0',
        'discountPriority': 'INTEGER DEFAULT 0',
        'workspaceId': 'TEXT',
        'workspaceID': 'TEXT',
        'categoryID': 'TEXT',
        'createdBy': 'TEXT',
        'createdDate': 'TEXT',
        'updatedDate': 'TEXT',
        'syncStatus': 'TEXT',
        'rowStatus': 'INTEGER DEFAULT 0',
        'type': 'TEXT', // Alias for discountType
        'mode': 'TEXT', // Alias for discountMode
      };

      // Add missing columns with better error handling
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDef = entry.value;

        if (!existingColumns.contains(columnName)) {
          try {
            await db.execute('ALTER TABLE Discount ADD COLUMN $columnName $columnDef');
            log('Added missing column: $columnName');
          } catch (e) {
            final errorMsg = e.toString().toLowerCase();
            if (errorMsg.contains('duplicate column name')) {
              log('ℹ️ Column $columnName already exists, skipping');
            } else {
              log('Failed to add column $columnName: $e');
            }
          }
        } else {
          log('ℹ️ Column $columnName already exists');
        }
      }

      // Create indexes for better performance
      await _createDiscountIndexes(db);

      log('Discount table schema fix completed');
    } catch (e) {
      log('Error fixing discount table schema: $e');
    }
  }

  /// Create indexes for discount table
  static Future<void> _createDiscountIndexes(Database db) async {
    final indexes = [
      'CREATE INDEX IF NOT EXISTS idx_discount_active ON Discount(discountActive)',
      'CREATE INDEX IF NOT EXISTS idx_discount_coupon ON Discount(discountCouponName)',
      'CREATE INDEX IF NOT EXISTS idx_discount_workspace ON Discount(workspaceID)',
      'CREATE INDEX IF NOT EXISTS idx_discount_type ON Discount(discountType)',
      'CREATE INDEX IF NOT EXISTS idx_discount_dates ON Discount(fromDate, toDate)',
    ];

    for (final indexSql in indexes) {
      try {
        await db.execute(indexSql);
      } catch (e) {
        log('Failed to create index: $e');
      }
    }
  }

  /// Fix product table schema for GST columns
  static Future<void> fixProductTableSchema(Database db) async {
    try {
      log('Checking and fixing product table schema...');

      // Get current table info
      final tableInfo = await db.rawQuery("PRAGMA table_info(Products)");
      final existingColumns = tableInfo.map((row) => row['name'] as String).toSet();

      log('Existing product table columns: $existingColumns');

      // Define required GST columns
      final requiredColumns = {
        'cgst': 'TEXT DEFAULT "0"',
        'sgst': 'TEXT DEFAULT "0"',
        'igst': 'TEXT DEFAULT "0"',
        'gstRate': 'TEXT DEFAULT "0"',
        'taxCategory': 'TEXT DEFAULT "standard"',
        'hsnCode': 'TEXT',
      };

      // Add missing columns
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDef = entry.value;

        if (!existingColumns.contains(columnName)) {
          try {
            await db.execute('ALTER TABLE Products ADD COLUMN $columnName $columnDef');
            log('Added missing product column: $columnName');
          } catch (e) {
            log('Failed to add product column $columnName: $e');
          }
        }
      }

      log('Product table schema fix completed');
    } catch (e) {
      log('Error fixing product table schema: $e');
    }
  }

  /// Fix sales transaction table schema
  static Future<void> fixSalesTransactionSchema(Database db) async {
    try {
      log('Checking and fixing sales transaction table schema...');

      // Get current table info
      final tableInfo = await db.rawQuery("PRAGMA table_info(SalesTransaction)");
      final existingColumns = tableInfo.map((row) => row['name'] as String).toSet();

      log('Existing sales transaction table columns: $existingColumns');

      // Define required columns
      final requiredColumns = {
        'productName': 'TEXT',
        'cgst': 'REAL DEFAULT 0',
        'sgst': 'REAL DEFAULT 0',
        'igst': 'REAL DEFAULT 0',
        'gstRate': 'REAL DEFAULT 0',
        'gstAmount': 'REAL DEFAULT 0',
        'discountAmount': 'REAL DEFAULT 0',
        'discountType': 'TEXT',
        'appliedDiscountId': 'TEXT',
      };

      // Add missing columns
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDef = entry.value;

        if (!existingColumns.contains(columnName)) {
          try {
            await db.execute('ALTER TABLE SalesTransaction ADD COLUMN $columnName $columnDef');
            log('Added missing sales transaction column: $columnName');
          } catch (e) {
            log('Failed to add sales transaction column $columnName: $e');
          }
        }
      }

      log('Sales transaction table schema fix completed');
    } catch (e) {
      log('Error fixing sales transaction table schema: $e');
    }
  }

  /// Fix Sales table schema - add missing columns
  static Future<void> fixSalesTableSchema(Database db) async {
    try {
      log('🔧 Fixing Sales table schema...');

      // Get existing columns
      final tableInfo = await db.rawQuery('PRAGMA table_info(Sales)');
      final existingColumns = tableInfo.map((row) => row['name'] as String).toSet();

      log('Existing Sales table columns: $existingColumns');

      // Define required columns for Sales table
      final requiredColumns = {
        'subtotalAmount': 'REAL DEFAULT NULL',
        'discountAmount': 'REAL DEFAULT NULL',
        'parcelAmount': 'REAL DEFAULT NULL',
        'cgstAmount': 'REAL DEFAULT NULL',
        'sgstAmount': 'REAL DEFAULT NULL',
        'igstAmount': 'REAL DEFAULT NULL',
        'cessAmount': 'REAL DEFAULT NULL',
        'totalTaxAmount': 'REAL DEFAULT NULL',
        'isReverseCharge': 'INTEGER DEFAULT 0',
        'stateCode': 'TEXT DEFAULT NULL',
        'taxConfiguration': 'TEXT DEFAULT NULL',
      };

      // Add missing columns
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDefinition = entry.value;

        if (!existingColumns.contains(columnName)) {
          try {
            await db.execute('ALTER TABLE Sales ADD COLUMN $columnName $columnDefinition');
            log('✅ Added missing Sales column: $columnName');
          } catch (e) {
            final errorMsg = e.toString().toLowerCase();
            if (errorMsg.contains('duplicate column name')) {
              log('ℹ️ Sales column $columnName already exists, skipping');
            } else {
              log('⚠️ Could not add Sales column $columnName: $e');
            }
          }
        } else {
          log('ℹ️ Sales column $columnName already exists');
        }
      }

      log('✅ Sales table schema fix completed');
    } catch (e) {
      log('❌ Error fixing Sales table schema: $e');
    }
  }

  /// Fix SalesTransaction table schema - add missing columns
  static Future<void> fixSalesTransactionTableSchema(Database db) async {
    try {
      log('🔧 Fixing SalesTransaction table schema...');

      // Get existing columns
      final tableInfo = await db.rawQuery('PRAGMA table_info(salesTransaction)');
      final existingColumns = tableInfo.map((row) => row['name'] as String).toSet();

      log('Existing SalesTransaction table columns: $existingColumns');

      // Define required columns for SalesTransaction table
      final requiredColumns = {
        'baseAmount': 'REAL DEFAULT NULL',
        'hsnCode': 'TEXT DEFAULT NULL',
        'gstRate': 'REAL DEFAULT NULL',
        'totalTaxAmount': 'REAL DEFAULT NULL',
        'customTaxApplied': 'INTEGER DEFAULT 0',
        'cess': 'REAL DEFAULT NULL',
        'igst': 'REAL DEFAULT NULL',
      };

      // Add missing columns
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDefinition = entry.value;

        if (!existingColumns.contains(columnName)) {
          try {
            await db.execute('ALTER TABLE salesTransaction ADD COLUMN $columnName $columnDefinition');
            log('✅ Added missing SalesTransaction column: $columnName');
          } catch (e) {
            final errorMsg = e.toString().toLowerCase();
            if (errorMsg.contains('duplicate column name')) {
              log('ℹ️ SalesTransaction column $columnName already exists, skipping');
            } else {
              log('⚠️ Could not add SalesTransaction column $columnName: $e');
            }
          }
        } else {
          log('ℹ️ SalesTransaction column $columnName already exists');
        }
      }

      log('✅ SalesTransaction table schema fix completed');
    } catch (e) {
      log('❌ Error fixing SalesTransaction table schema: $e');
    }
  }

  /// Safe index creation - only creates indexes if columns exist
  static Future<void> createSafeIndexes(Database db) async {
    try {
      log('🔧 Creating safe indexes...');

      // Check if columns exist before creating indexes
      final salesColumns = await getTableColumns(db, 'Sales');
      final salesTransactionColumns = await getTableColumns(db, 'salesTransaction');

      // Only create indexes for existing columns
      final indexesToCreate = <String>[];

      if (salesColumns.containsKey('discountAmount')) {
        indexesToCreate.add('CREATE INDEX IF NOT EXISTS idx_sales_discount ON Sales(discountAmount)');
      }

      if (salesColumns.containsKey('stateCode')) {
        indexesToCreate.add('CREATE INDEX IF NOT EXISTS idx_sales_state_code ON Sales(stateCode)');
      }

      if (salesTransactionColumns.containsKey('hsnCode')) {
        indexesToCreate.add('CREATE INDEX IF NOT EXISTS idx_sales_transaction_hsn ON salesTransaction(hsnCode)');
      }

      if (salesTransactionColumns.containsKey('gstRate')) {
        indexesToCreate.add('CREATE INDEX IF NOT EXISTS idx_sales_transaction_gst_rate ON salesTransaction(gstRate)');
      }

      // Create the indexes
      for (final indexSql in indexesToCreate) {
        try {
          await db.execute(indexSql);
          log('✅ Created index successfully');
        } catch (e) {
          log('⚠️ Failed to create index: $e');
        }
      }

      log('✅ Safe index creation completed');
    } catch (e) {
      log('❌ Error creating safe indexes: $e');
    }
  }

  /// Run all schema fixes
  static Future<void> runAllSchemaFixes(Database db) async {
    log('Running all schema fixes...');

    await fixDiscountTableSchema(db);
    await fixSalesTableSchema(db);
    await fixProductTableSchema(db);
    await fixSalesTransactionSchema(db);
    await fixSalesTransactionTableSchema(db);
    await createSafeIndexes(db);

    log('All schema fixes completed');
  }

  /// Verify table exists and create if missing
  static Future<void> ensureTableExists(Database db, String tableName, String createSql) async {
    try {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
      );

      if (result.isEmpty) {
        log('Table $tableName does not exist, creating...');
        await db.execute(createSql);
        log('Created table: $tableName');
      } else {
        log('Table $tableName exists');
      }
    } catch (e) {
      log('Error ensuring table $tableName exists: $e');
    }
  }

  /// Get table column information for debugging
  static Future<Map<String, String>> getTableColumns(Database db, String tableName) async {
    try {
      final tableInfo = await db.rawQuery("PRAGMA table_info($tableName)");
      final columns = <String, String>{};

      for (final row in tableInfo) {
        final name = row['name'] as String;
        final type = row['type'] as String;
        columns[name] = type;
      }

      return columns;
    } catch (e) {
      log('Error getting table columns for $tableName: $e');
      return {};
    }
  }

  /// Debug: Print all table schemas
  static Future<void> debugPrintAllSchemas(Database db) async {
    final tables = ['Discount', 'Products', 'SalesTransaction', 'Sales'];

    for (final table in tables) {
      log('=== $table Table Schema ===');
      final columns = await getTableColumns(db, table);
      for (final entry in columns.entries) {
        log('  ${entry.key}: ${entry.value}');
      }
      log('');
    }
  }
}
