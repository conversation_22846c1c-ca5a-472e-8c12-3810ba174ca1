import '../table_columns.dart';

// Migration script to add new discount columns without breaking existing structure
const String addDiscountEnhancedColumns = '''
  ALTER TABLE $discountTable ADD COLUMN $discountType TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountCouponCode TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountMaxUsageCount INTEGER DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountCurrentUsageCount INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountMinOrderAmount REAL DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountMaxDiscountAmount REAL DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountIsStackable INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountApplicableProducts TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountApplicableCategories TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountCustomerSegment TEXT DEFAULT 'all';
  ALTER TABLE $discountTable ADD COLUMN $discountPriority INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountDescription TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountTermsAndConditions TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountLastUsedDate TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountLastUsedBy TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountIsAutoApply INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountTriggerConditions TEXT DEFAULT NULL;
''';

// Create indexes for better performance
const String createDiscountIndexes = '''
  CREATE INDEX IF NOT EXISTS idx_discount_type ON $discountTable($discountType);
  CREATE INDEX IF NOT EXISTS idx_discount_coupon_code ON $discountTable($discountCouponCode);
  CREATE INDEX IF NOT EXISTS idx_discount_active ON $discountTable($discountActive);
  CREATE INDEX IF NOT EXISTS idx_discount_workspace ON $discountTable($workSpaceId);
  CREATE INDEX IF NOT EXISTS idx_discount_sync ON $discountTable($discountSync);
  CREATE INDEX IF NOT EXISTS idx_discount_priority ON $discountTable($discountPriority);
''';

// Create coupon usage tracking table
const String createCouponUsageTable = '''
  CREATE TABLE IF NOT EXISTS CouponUsage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    discountID TEXT NOT NULL,
    couponCode TEXT NOT NULL,
    usedBy TEXT NOT NULL,
    usedDate TEXT NOT NULL,
    salesId TEXT DEFAULT NULL,
    orderAmount REAL DEFAULT NULL,
    discountAmount REAL DEFAULT NULL,
    workSpaceId TEXT NOT NULL,
    FOREIGN KEY (discountID) REFERENCES $discountTable($discountID)
  );
''';

const String createCouponUsageIndexes = '''
  CREATE INDEX IF NOT EXISTS idx_coupon_usage_discount ON CouponUsage(discountID);
  CREATE INDEX IF NOT EXISTS idx_coupon_usage_code ON CouponUsage(couponCode);
  CREATE INDEX IF NOT EXISTS idx_coupon_usage_user ON CouponUsage(usedBy);
  CREATE INDEX IF NOT EXISTS idx_coupon_usage_date ON CouponUsage(usedDate);
''';

// Create discount application log table for audit trail
const String createDiscountLogTable = '''
  CREATE TABLE IF NOT EXISTS DiscountLog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    discountID TEXT NOT NULL,
    salesId TEXT NOT NULL,
    productId TEXT DEFAULT NULL,
    appliedBy TEXT NOT NULL,
    appliedDate TEXT NOT NULL,
    originalAmount REAL NOT NULL,
    discountAmount REAL NOT NULL,
    finalAmount REAL NOT NULL,
    discountType TEXT NOT NULL,
    workSpaceId TEXT NOT NULL,
    syncStatus INTEGER DEFAULT 0,
    FOREIGN KEY (discountID) REFERENCES $discountTable($discountID)
  );
''';

const String createDiscountLogIndexes = '''
  CREATE INDEX IF NOT EXISTS idx_discount_log_discount ON DiscountLog(discountID);
  CREATE INDEX IF NOT EXISTS idx_discount_log_sales ON DiscountLog(salesId);
  CREATE INDEX IF NOT EXISTS idx_discount_log_date ON DiscountLog(appliedDate);
  CREATE INDEX IF NOT EXISTS idx_discount_log_sync ON DiscountLog(syncStatus);
''';

// Function to run all migration scripts
List<String> getMigrationV2Scripts() {
  return [
    addDiscountEnhancedColumns,
    createDiscountIndexes,
    createCouponUsageTable,
    createCouponUsageIndexes,
    createDiscountLogTable,
    createDiscountLogIndexes,
  ];
}
