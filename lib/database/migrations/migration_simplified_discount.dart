import '../table_columns.dart';

/// Simplified discount migration that removes complex features
/// while keeping essential analytics tables

// Remove complex discount columns that are not needed for simplified functionality
const String removeComplexDiscountColumns = '''
  -- Note: SQLite doesn't support DROP COLUMN directly
  -- These columns will remain but won't be used in the simplified implementation:
  -- discountType, couponCode, maxUsageCount, currentUsageCount, minOrderAmount,
  -- maxDiscountAmount, isStackable, applicableProducts, applicableCategories,
  -- customerSegment, priority, description, termsAndConditions, lastUsedDate,
  -- lastUsedBy, isAutoApply, triggerConditions
  
  -- For now, we'll just ensure the basic discount table structure is correct
  -- and keep the analytics tables (CouponUsage and DiscountLog) for reporting
''';

// Ensure basic discount table has required columns for simplified functionality
const String ensureBasicDiscountColumns = '''
  -- Ensure basic discount table has the minimum required columns
  -- These are already in the base table, so this is just a verification
  SELECT name FROM sqlite_master WHERE type='table' AND name='$discountTable';
''';

// Keep CouponUsage table for analytics (already created in migration_v2)
const String verifyCouponUsageTable = '''
  -- Verify CouponUsage table exists for analytics
  SELECT name FROM sqlite_master WHERE type='table' AND name='CouponUsage';
''';

// Keep DiscountLog table for audit trail (already created in migration_v2)
const String verifyDiscountLogTable = '''
  -- Verify DiscountLog table exists for audit trail
  SELECT name FROM sqlite_master WHERE type='table' AND name='DiscountLog';
''';

// Create a view for simplified discount analytics
const String createSimplifiedDiscountAnalyticsView = '''
  CREATE VIEW IF NOT EXISTS SimplifiedDiscountAnalytics AS
  SELECT 
    dl.discountType,
    COUNT(*) as usage_count,
    SUM(dl.discountAmount) as total_discount_amount,
    AVG(dl.discountAmount) as avg_discount_amount,
    DATE(dl.appliedDate) as date_applied,
    dl.workSpaceId
  FROM DiscountLog dl
  GROUP BY dl.discountType, DATE(dl.appliedDate), dl.workSpaceId
  ORDER BY dl.appliedDate DESC;
''';

// Create a view for coupon analytics
const String createCouponAnalyticsView = '''
  CREATE VIEW IF NOT EXISTS CouponAnalytics AS
  SELECT 
    cu.couponCode,
    COUNT(*) as usage_count,
    SUM(cu.discountAmount) as total_discount_amount,
    AVG(cu.discountAmount) as avg_discount_amount,
    AVG(cu.orderAmount) as avg_order_amount,
    DATE(cu.usedDate) as date_used,
    cu.workSpaceId
  FROM CouponUsage cu
  GROUP BY cu.couponCode, DATE(cu.usedDate), cu.workSpaceId
  ORDER BY cu.usedDate DESC;
''';

// Function to run simplified discount migration
List<String> getSimplifiedDiscountMigrationScripts() {
  return [
    ensureBasicDiscountColumns,
    verifyCouponUsageTable,
    verifyDiscountLogTable,
    createSimplifiedDiscountAnalyticsView,
    createCouponAnalyticsView,
  ];
}

/// Documentation for simplified discount system
/// 
/// This migration simplifies the discount system by:
/// 1. Keeping only essential discount functionality
/// 2. Maintaining analytics tables for reporting
/// 3. Creating simplified views for common analytics queries
/// 4. Removing complex features like:
///    - Auto-apply discounts
///    - Complex coupon management
///    - Product-specific discounts
///    - Customer segmentation
///    - Discount stacking rules
/// 
/// The simplified system supports:
/// - Manual percentage discounts
/// - Basic coupon codes with predefined discounts
/// - Usage tracking for analytics
/// - Audit trail for compliance
