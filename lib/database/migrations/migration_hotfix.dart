import '../table_columns.dart';

/// Hotfix migration to add missing columns that are causing runtime errors
/// This migration adds missing columns to existing tables without breaking the app

const String addMissingSalesColumns = '''
  ALTER TABLE $salesTable ADD COLUMN subtotalAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN cgstAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN sgstAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN igstAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN cessAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN totalTaxAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN isReverseCharge INTEGER DEFAULT 0;
  ALTER TABLE $salesTable ADD COLUMN stateCode TEXT DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN taxConfiguration TEXT DEFAULT NULL;
''';

const String addMissingDiscountColumns = '''
  ALTER TABLE $discountTable ADD COLUMN $discountPriority INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountType TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountCouponCode TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountMaxUsageCount INTEGER DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountCurrentUsageCount INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountMinOrderAmount REAL DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountMaxDiscountAmount REAL DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountIsStackable INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountApplicableProducts TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountApplicableCategories TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountCustomerSegment TEXT DEFAULT 'all';
  ALTER TABLE $discountTable ADD COLUMN $discountDescription TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountTermsAndConditions TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountLastUsedDate TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountLastUsedBy TEXT DEFAULT NULL;
  ALTER TABLE $discountTable ADD COLUMN $discountIsAutoApply INTEGER DEFAULT 0;
  ALTER TABLE $discountTable ADD COLUMN $discountTriggerConditions TEXT DEFAULT NULL;
''';

const String addMissingSalesTransactionColumns = '''
  ALTER TABLE $salesTransactionTable ADD COLUMN baseAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN igst REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN cess REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN gstRate REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN cessRate REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN hsnCode TEXT DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN taxableAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN totalTaxAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN customTaxApplied INTEGER DEFAULT 0;
''';

/// Function to get all hotfix migration scripts
List<String> getHotfixMigrationScripts() {
  return [
    addMissingSalesColumns,
    addMissingDiscountColumns,
    addMissingSalesTransactionColumns,
  ];
}

/// Safe column addition - checks if column exists before adding
String createSafeColumnAddition(String tableName, String columnName, String columnDefinition) {
  return '''
    DO \$\$ 
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pragma_table_info('$tableName') WHERE name = '$columnName') THEN
            ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition;
        END IF;
    END \$\$;
  ''';
}

/// Get safe migration scripts that check for column existence
List<String> getSafeMigrationScripts() {
  return [
    // Sales table columns
    "ALTER TABLE $salesTable ADD COLUMN subtotalAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTable ADD COLUMN cgstAmount REAL DEFAULT NULL", 
    "ALTER TABLE $salesTable ADD COLUMN sgstAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTable ADD COLUMN igstAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTable ADD COLUMN cessAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTable ADD COLUMN totalTaxAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTable ADD COLUMN isReverseCharge INTEGER DEFAULT 0",
    "ALTER TABLE $salesTable ADD COLUMN stateCode TEXT DEFAULT NULL",
    "ALTER TABLE $salesTable ADD COLUMN taxConfiguration TEXT DEFAULT NULL",
    
    // Discount table columns
    "ALTER TABLE $discountTable ADD COLUMN $discountPriority INTEGER DEFAULT 0",
    "ALTER TABLE $discountTable ADD COLUMN $discountType TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountCouponCode TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountMaxUsageCount INTEGER DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountCurrentUsageCount INTEGER DEFAULT 0",
    "ALTER TABLE $discountTable ADD COLUMN $discountMinOrderAmount REAL DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountMaxDiscountAmount REAL DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountIsStackable INTEGER DEFAULT 0",
    "ALTER TABLE $discountTable ADD COLUMN $discountApplicableProducts TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountApplicableCategories TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountCustomerSegment TEXT DEFAULT 'all'",
    "ALTER TABLE $discountTable ADD COLUMN $discountDescription TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountTermsAndConditions TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountLastUsedDate TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountLastUsedBy TEXT DEFAULT NULL",
    "ALTER TABLE $discountTable ADD COLUMN $discountIsAutoApply INTEGER DEFAULT 0",
    "ALTER TABLE $discountTable ADD COLUMN $discountTriggerConditions TEXT DEFAULT NULL",
    
    // Sales transaction table columns
    "ALTER TABLE $salesTransactionTable ADD COLUMN baseAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN igst REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN cess REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN gstRate REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN cessRate REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN hsnCode TEXT DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN taxableAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN totalTaxAmount REAL DEFAULT NULL",
    "ALTER TABLE $salesTransactionTable ADD COLUMN customTaxApplied INTEGER DEFAULT 0",
  ];
}
