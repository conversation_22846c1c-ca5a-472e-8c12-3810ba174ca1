import '../table_columns.dart';

/// Migration V3: GST Enhancement - Add comprehensive tax tracking columns
/// This migration adds professional-level GST and tax management fields

// Add comprehensive tax columns to Sales table
const String addSalesTaxColumns = '''
  ALTER TABLE $salesTable ADD COLUMN subtotalAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN cgstAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN sgstAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN igstAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN cessAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN totalTaxAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN isReverseCharge INTEGER DEFAULT 0;
  ALTER TABLE $salesTable ADD COLUMN stateCode TEXT DEFAULT NULL;
  ALTER TABLE $salesTable ADD COLUMN taxConfiguration TEXT DEFAULT NULL;
''';

// Add comprehensive tax columns to SalesTransaction table
const String addSalesTransactionTaxColumns = '''
  ALTER TABLE $salesTransactionTable ADD COLUMN baseAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN igst REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN cess REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN gstRate REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN cessRate REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN hsnCode TEXT DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN taxableAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN totalTaxAmount REAL DEFAULT NULL;
  ALTER TABLE $salesTransactionTable ADD COLUMN customTaxApplied INTEGER DEFAULT 0;
''';

// Create GST Configuration table for professional tax management
const String createGSTConfigurationTable = '''
  CREATE TABLE IF NOT EXISTS GSTConfiguration (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workSpaceId TEXT NOT NULL,
    businessName TEXT NOT NULL,
    businessGSTIN TEXT DEFAULT NULL,
    businessStateCode TEXT NOT NULL,
    businessAddress TEXT DEFAULT NULL,
    defaultGSTRate REAL DEFAULT 18.0,
    enableIGST INTEGER DEFAULT 1,
    enableCess INTEGER DEFAULT 0,
    enableReverseCharge INTEGER DEFAULT 0,
    taxInclusivePricing INTEGER DEFAULT 0,
    roundingMethod TEXT DEFAULT 'ROUND_OFF',
    createdDate TEXT DEFAULT CURRENT_TIMESTAMP,
    updatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    isActive INTEGER DEFAULT 1,
    syncStatus INTEGER DEFAULT 0
  );
''';

// Create Tax Rate Master table for flexible tax configuration
const String createTaxRateMasterTable = '''
  CREATE TABLE IF NOT EXISTS TaxRateMaster (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workSpaceId TEXT NOT NULL,
    taxRateId TEXT UNIQUE NOT NULL,
    taxRateName TEXT NOT NULL,
    cgstRate REAL DEFAULT 0,
    sgstRate REAL DEFAULT 0,
    igstRate REAL DEFAULT 0,
    cessRate REAL DEFAULT 0,
    hsnCodeRange TEXT DEFAULT NULL,
    applicableFrom TEXT DEFAULT NULL,
    applicableTo TEXT DEFAULT NULL,
    description TEXT DEFAULT NULL,
    createdDate TEXT DEFAULT CURRENT_TIMESTAMP,
    updatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    isActive INTEGER DEFAULT 1,
    syncStatus INTEGER DEFAULT 0
  );
''';

// Create HSN/SAC Master table for product classification
const String createHSNMasterTable = '''
  CREATE TABLE IF NOT EXISTS HSNMaster (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workSpaceId TEXT NOT NULL,
    hsnCode TEXT NOT NULL,
    sacCode TEXT DEFAULT NULL,
    description TEXT NOT NULL,
    defaultGSTRate REAL DEFAULT 0,
    defaultCessRate REAL DEFAULT 0,
    category TEXT DEFAULT NULL,
    subCategory TEXT DEFAULT NULL,
    createdDate TEXT DEFAULT CURRENT_TIMESTAMP,
    updatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    isActive INTEGER DEFAULT 1,
    syncStatus INTEGER DEFAULT 0,
    UNIQUE(workSpaceId, hsnCode)
  );
''';

// Create Tax Audit Log table for compliance and tracking
const String createTaxAuditLogTable = '''
  CREATE TABLE IF NOT EXISTS TaxAuditLog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workSpaceId TEXT NOT NULL,
    salesId TEXT NOT NULL,
    salesTransactionId TEXT DEFAULT NULL,
    productId TEXT DEFAULT NULL,
    auditType TEXT NOT NULL,
    originalTaxRate REAL DEFAULT NULL,
    appliedTaxRate REAL DEFAULT NULL,
    taxAmount REAL DEFAULT NULL,
    reason TEXT DEFAULT NULL,
    appliedBy TEXT NOT NULL,
    appliedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ipAddress TEXT DEFAULT NULL,
    deviceInfo TEXT DEFAULT NULL,
    syncStatus INTEGER DEFAULT 0
  );
''';

// Create indexes for better performance
const String createGSTIndexes = '''
  CREATE INDEX IF NOT EXISTS idx_sales_tax_config ON $salesTable(taxConfiguration);
  CREATE INDEX IF NOT EXISTS idx_sales_state_code ON $salesTable(stateCode);
  CREATE INDEX IF NOT EXISTS idx_sales_transaction_hsn ON $salesTransactionTable(hsnCode);
  CREATE INDEX IF NOT EXISTS idx_sales_transaction_gst_rate ON $salesTransactionTable(gstRate);
  CREATE INDEX IF NOT EXISTS idx_gst_config_workspace ON GSTConfiguration(workSpaceId);
  CREATE INDEX IF NOT EXISTS idx_tax_rate_workspace ON TaxRateMaster(workSpaceId);
  CREATE INDEX IF NOT EXISTS idx_hsn_workspace ON HSNMaster(workSpaceId);
  CREATE INDEX IF NOT EXISTS idx_tax_audit_sales ON TaxAuditLog(salesId);
  CREATE INDEX IF NOT EXISTS idx_tax_audit_date ON TaxAuditLog(appliedDate);
''';

// Insert default GST rates for common scenarios
const String insertDefaultTaxRates = '''
  INSERT OR IGNORE INTO TaxRateMaster (
    workSpaceId, taxRateId, taxRateName, cgstRate, sgstRate, igstRate, 
    cessRate, description, isActive
  ) VALUES 
  ('default', 'GST_0', 'GST Free', 0, 0, 0, 0, 'No GST applicable', 1),
  ('default', 'GST_5', 'GST 5%', 2.5, 2.5, 5, 0, 'Essential goods and services', 1),
  ('default', 'GST_12', 'GST 12%', 6, 6, 12, 0, 'Standard rate for many goods', 1),
  ('default', 'GST_18', 'GST 18%', 9, 9, 18, 0, 'Standard rate for services', 1),
  ('default', 'GST_28', 'GST 28%', 14, 14, 28, 0, 'Luxury goods and services', 1);
''';

// Insert common HSN codes for F&B industry
const String insertDefaultHSNCodes = '''
  INSERT OR IGNORE INTO HSNMaster (
    workSpaceId, hsnCode, description, defaultGSTRate, category, isActive
  ) VALUES 
  ('default', '2101', 'Coffee and coffee preparations', 18, 'Food & Beverages', 1),
  ('default', '2102', 'Tea preparations', 18, 'Food & Beverages', 1),
  ('default', '2105', 'Ice cream and edible ice', 18, 'Food & Beverages', 1),
  ('default', '1905', 'Bakery products', 18, 'Food & Beverages', 1),
  ('default', '2202', 'Non-alcoholic beverages', 12, 'Food & Beverages', 1),
  ('default', '9963', 'Restaurant services', 5, 'Services', 1);
''';

// Function to get all migration scripts for V3
List<String> getMigrationV3Scripts() {
  return [
    addSalesTaxColumns,
    addSalesTransactionTaxColumns,
    createGSTConfigurationTable,
    createTaxRateMasterTable,
    createHSNMasterTable,
    createTaxAuditLogTable,
    createGSTIndexes,
    insertDefaultTaxRates,
    insertDefaultHSNCodes,
  ];
}

// Rollback scripts (if needed)
const String rollbackSalesTaxColumns = '''
  ALTER TABLE $salesTable DROP COLUMN subtotalAmount;
  ALTER TABLE $salesTable DROP COLUMN cgstAmount;
  ALTER TABLE $salesTable DROP COLUMN sgstAmount;
  ALTER TABLE $salesTable DROP COLUMN igstAmount;
  ALTER TABLE $salesTable DROP COLUMN cessAmount;
  ALTER TABLE $salesTable DROP COLUMN totalTaxAmount;
  ALTER TABLE $salesTable DROP COLUMN isReverseCharge;
  ALTER TABLE $salesTable DROP COLUMN stateCode;
  ALTER TABLE $salesTable DROP COLUMN taxConfiguration;
''';

const String rollbackSalesTransactionTaxColumns = '''
  ALTER TABLE $salesTransactionTable DROP COLUMN baseAmount;
  ALTER TABLE $salesTransactionTable DROP COLUMN igst;
  ALTER TABLE $salesTransactionTable DROP COLUMN cess;
  ALTER TABLE $salesTransactionTable DROP COLUMN gstRate;
  ALTER TABLE $salesTransactionTable DROP COLUMN cessRate;
  ALTER TABLE $salesTransactionTable DROP COLUMN hsnCode;
  ALTER TABLE $salesTransactionTable DROP COLUMN taxableAmount;
  ALTER TABLE $salesTransactionTable DROP COLUMN totalTaxAmount;
  ALTER TABLE $salesTransactionTable DROP COLUMN customTaxApplied;
''';

const String rollbackGSTTables = '''
  DROP TABLE IF EXISTS GSTConfiguration;
  DROP TABLE IF EXISTS TaxRateMaster;
  DROP TABLE IF EXISTS HSNMaster;
  DROP TABLE IF EXISTS TaxAuditLog;
''';

List<String> getRollbackV3Scripts() {
  return [
    rollbackSalesTaxColumns,
    rollbackSalesTransactionTaxColumns,
    rollbackGSTTables,
  ];
}
