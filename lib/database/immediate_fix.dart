import 'dart:developer';
import 'package:sqflite/sqflite.dart';
import 'app_database.dart';

/// Immediate database fix - run this to fix column issues right now
class ImmediateDatabaseFix {
  
  /// Fix the database schema issues immediately
  static Future<void> fixNow() async {
    try {
      log('🔧 Starting immediate database fix...');
      
      final db = await AppDatabase().database;
      
      // Fix Sales table - add missing columns
      await _fixSalesTable(db);
      
      // Fix Discount table - add missing columns  
      await _fixDiscountTable(db);
      
      // Fix SalesTransaction table - add missing columns
      await _fixSalesTransactionTable(db);
      
      log('✅ Immediate database fix completed successfully!');
      
    } catch (e) {
      log('❌ Immediate database fix failed: $e');
      rethrow;
    }
  }
  
  static Future<void> _fixSalesTable(Database db) async {
    log('🔧 Fixing Sales table...');
    
    final List<String> salesColumns = [
      'ALTER TABLE Sales ADD COLUMN subtotalAmount REAL DEFAULT NULL',
      'ALTER TABLE Sales ADD COLUMN cgstAmount REAL DEFAULT NULL',
      'ALTER TABLE Sales ADD COLUMN sgstAmount REAL DEFAULT NULL', 
      'ALTER TABLE Sales ADD COLUMN igstAmount REAL DEFAULT NULL',
      'ALTER TABLE Sales ADD COLUMN cessAmount REAL DEFAULT NULL',
      'ALTER TABLE Sales ADD COLUMN totalTaxAmount REAL DEFAULT NULL',
      'ALTER TABLE Sales ADD COLUMN isReverseCharge INTEGER DEFAULT 0',
      'ALTER TABLE Sales ADD COLUMN stateCode TEXT DEFAULT NULL',
      'ALTER TABLE Sales ADD COLUMN taxConfiguration TEXT DEFAULT NULL',
    ];
    
    for (String sql in salesColumns) {
      try {
        await db.execute(sql);
        log('✅ Added column: ${sql.split(' ')[4]}');
      } catch (e) {
        if (e.toString().contains('duplicate column name')) {
          log('ℹ️ Column already exists: ${sql.split(' ')[4]}');
        } else {
          log('⚠️ Error adding column: $e');
        }
      }
    }
  }
  
  static Future<void> _fixDiscountTable(Database db) async {
    log('🔧 Fixing Discount table...');
    
    final List<String> discountColumns = [
      'ALTER TABLE Discount ADD COLUMN priority INTEGER DEFAULT 0',
      'ALTER TABLE Discount ADD COLUMN discountType TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN couponCode TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN maxUsageCount INTEGER DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN currentUsageCount INTEGER DEFAULT 0',
      'ALTER TABLE Discount ADD COLUMN minOrderAmount REAL DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN maxDiscountAmount REAL DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN isStackable INTEGER DEFAULT 0',
      'ALTER TABLE Discount ADD COLUMN applicableProducts TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN applicableCategories TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN customerSegment TEXT DEFAULT \'all\'',
      'ALTER TABLE Discount ADD COLUMN description TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN termsAndConditions TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN lastUsedDate TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN lastUsedBy TEXT DEFAULT NULL',
      'ALTER TABLE Discount ADD COLUMN isAutoApply INTEGER DEFAULT 0',
      'ALTER TABLE Discount ADD COLUMN triggerConditions TEXT DEFAULT NULL',
    ];
    
    for (String sql in discountColumns) {
      try {
        await db.execute(sql);
        log('✅ Added column: ${sql.split(' ')[4]}');
      } catch (e) {
        if (e.toString().contains('duplicate column name')) {
          log('ℹ️ Column already exists: ${sql.split(' ')[4]}');
        } else {
          log('⚠️ Error adding column: $e');
        }
      }
    }
  }
  
  static Future<void> _fixSalesTransactionTable(Database db) async {
    log('🔧 Fixing SalesTransaction table...');
    
    final List<String> salesTransactionColumns = [
      'ALTER TABLE salesTransaction ADD COLUMN baseAmount REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN igst REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN cess REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN gstRate REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN cessRate REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN hsnCode TEXT DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN taxableAmount REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN totalTaxAmount REAL DEFAULT NULL',
      'ALTER TABLE salesTransaction ADD COLUMN customTaxApplied INTEGER DEFAULT 0',
    ];
    
    for (String sql in salesTransactionColumns) {
      try {
        await db.execute(sql);
        log('✅ Added column: ${sql.split(' ')[4]}');
      } catch (e) {
        if (e.toString().contains('duplicate column name')) {
          log('ℹ️ Column already exists: ${sql.split(' ')[4]}');
        } else {
          log('⚠️ Error adding column: $e');
        }
      }
    }
  }
  
  /// Check if the fix is needed
  static Future<bool> isFixNeeded() async {
    try {
      final db = await AppDatabase().database;
      
      // Check if priority column exists in Discount table
      final discountInfo = await db.rawQuery('PRAGMA table_info(Discount)');
      bool hasPriority = discountInfo.any((col) => col['name'] == 'priority');
      
      // Check if subtotalAmount exists in Sales table
      final salesInfo = await db.rawQuery('PRAGMA table_info(Sales)');
      bool hasSubtotal = salesInfo.any((col) => col['name'] == 'subtotalAmount');
      
      return !hasPriority || !hasSubtotal;
    } catch (e) {
      log('Error checking if fix is needed: $e');
      return true; // Assume fix is needed if we can't check
    }
  }
  
  /// Print current table schemas for debugging
  static Future<void> printTableSchemas() async {
    try {
      final db = await AppDatabase().database;
      
      log('=== SALES TABLE SCHEMA ===');
      final salesInfo = await db.rawQuery('PRAGMA table_info(Sales)');
      for (var col in salesInfo) {
        log('${col['name']}: ${col['type']}');
      }
      
      log('=== DISCOUNT TABLE SCHEMA ===');
      final discountInfo = await db.rawQuery('PRAGMA table_info(Discount)');
      for (var col in discountInfo) {
        log('${col['name']}: ${col['type']}');
      }
      
      log('=== SALES TRANSACTION TABLE SCHEMA ===');
      final salesTransactionInfo = await db.rawQuery('PRAGMA table_info(salesTransaction)');
      for (var col in salesTransactionInfo) {
        log('${col['name']}: ${col['type']}');
      }
      
    } catch (e) {
      log('Error printing table schemas: $e');
    }
  }
}
