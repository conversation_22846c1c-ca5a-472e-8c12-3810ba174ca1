import 'dart:developer';
import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'database_helper.dart';
import 'database_migration_helper.dart';

/// Utility class for exporting SQLite database schema and data
class DatabaseExportUtility {
  
  /// Export database schema with date information
  static Future<String> exportSchemaWithDate({
    bool includeData = false,
    String? customFileName,
  }) async {
    try {
      log('Starting database schema export...');
      
      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;
      
      // Generate filename with date
      final now = DateTime.now();
      final dateFormatter = DateFormat('yyyy-MM-dd_HH-mm-ss');
      final dateString = dateFormatter.format(now);
      
      final fileName = customFileName ?? 'coffee_pos_schema_$dateString.sql';
      
      // Get export directory
      final exportDir = await _getExportDirectory();
      final filePath = join(exportDir.path, fileName);
      
      // Generate schema content
      final schemaContent = await _generateSchemaContent(db, includeData, dateString);
      
      // Write to file
      final file = File(filePath);
      await file.writeAsString(schemaContent);
      
      log('Schema exported successfully to: $filePath');
      return filePath;
      
    } catch (e, stack) {
      log('Error exporting schema: $e\n$stack');
      rethrow;
    }
  }
  
  /// Export only table structures (no data)
  static Future<String> exportSchemaOnly({String? customFileName}) async {
    return await exportSchemaWithDate(
      includeData: false,
      customFileName: customFileName,
    );
  }
  
  /// Export schema with all data
  static Future<String> exportSchemaWithData({String? customFileName}) async {
    return await exportSchemaWithDate(
      includeData: true,
      customFileName: customFileName,
    );
  }
  
  /// Get list of all exported schema files
  static Future<List<FileSystemEntity>> getExportedFiles() async {
    try {
      final exportDir = await _getExportDirectory();
      if (!await exportDir.exists()) {
        return [];
      }
      
      final files = exportDir.listSync()
          .where((file) => file.path.endsWith('.sql'))
          .toList();
      
      // Sort by modification date (newest first)
      files.sort((a, b) {
        final aStat = a.statSync();
        final bStat = b.statSync();
        return bStat.modified.compareTo(aStat.modified);
      });
      
      return files;
    } catch (e) {
      log('Error getting exported files: $e');
      return [];
    }
  }
  
  /// Delete an exported file
  static Future<bool> deleteExportedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        log('Deleted exported file: $filePath');
        return true;
      }
      return false;
    } catch (e) {
      log('Error deleting file: $e');
      return false;
    }
  }
  
  /// Get export directory
  static Future<Directory> _getExportDirectory() async {
    late Directory baseDir;
    
    if (Platform.isAndroid || Platform.isIOS) {
      baseDir = await getApplicationDocumentsDirectory();
    } else {
      baseDir = await getApplicationDocumentsDirectory();
    }
    
    final exportDir = Directory(join(baseDir.path, 'database_exports'));
    if (!await exportDir.exists()) {
      await exportDir.create(recursive: true);
    }
    
    return exportDir;
  }
  
  /// Generate schema content with optional data
  static Future<String> _generateSchemaContent(
    Database db, 
    bool includeData, 
    String dateString
  ) async {
    final buffer = StringBuffer();
    
    // Header with date and metadata
    buffer.writeln('-- Coffee POS Database Schema Export');
    buffer.writeln('-- Generated on: ${DateTime.now().toIso8601String()}');
    buffer.writeln('-- Export Date: $dateString');
    buffer.writeln('-- Include Data: $includeData');
    buffer.writeln('-- Database Version: ${await _getDatabaseVersion(db)}');
    buffer.writeln('-- Total Tables: ${await _getTableCount(db)}');
    buffer.writeln('');
    buffer.writeln('-- ============================================');
    buffer.writeln('-- SCHEMA EXPORT START');
    buffer.writeln('-- ============================================');
    buffer.writeln('');
    
    // Get all tables
    final tables = await DatabaseMigrationHelper.getAllTables(db);
    
    for (final tableName in tables) {
      buffer.writeln('-- ----------------------------------------');
      buffer.writeln('-- Table: $tableName');
      buffer.writeln('-- ----------------------------------------');
      
      // Get table schema
      final schema = await _getTableSchema(db, tableName);
      buffer.writeln(schema);
      buffer.writeln('');
      
      // Get table info (columns, types, etc.)
      final tableInfo = await _getTableInfo(db, tableName);
      buffer.writeln('-- Table Info:');
      for (final info in tableInfo) {
        buffer.writeln('-- Column: ${info['name']} | Type: ${info['type']} | NotNull: ${info['notnull']} | Default: ${info['dflt_value']} | PK: ${info['pk']}');
      }
      buffer.writeln('');
      
      // Include data if requested
      if (includeData) {
        final rowCount = await _getTableRowCount(db, tableName);
        buffer.writeln('-- Data for table $tableName ($rowCount rows)');
        
        if (rowCount > 0) {
          final data = await _getTableData(db, tableName);
          for (final insertStatement in data) {
            buffer.writeln(insertStatement);
          }
        } else {
          buffer.writeln('-- No data in table $tableName');
        }
        buffer.writeln('');
      }
    }
    
    // Footer
    buffer.writeln('-- ============================================');
    buffer.writeln('-- SCHEMA EXPORT END');
    buffer.writeln('-- ============================================');
    buffer.writeln('-- Export completed at: ${DateTime.now().toIso8601String()}');
    
    return buffer.toString();
  }
  
  /// Get database version
  static Future<int> _getDatabaseVersion(Database db) async {
    try {
      final result = await db.rawQuery('PRAGMA user_version');
      return result.first['user_version'] as int? ?? 0;
    } catch (e) {
      return 0;
    }
  }
  
  /// Get total table count
  static Future<int> _getTableCount(Database db) async {
    try {
      final tables = await DatabaseMigrationHelper.getAllTables(db);
      return tables.length;
    } catch (e) {
      return 0;
    }
  }
  
  /// Get table schema (CREATE TABLE statement)
  static Future<String> _getTableSchema(Database db, String tableName) async {
    try {
      final result = await db.rawQuery(
        "SELECT sql FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      );
      
      if (result.isNotEmpty) {
        return '${result.first['sql']};';
      }
      return '-- Schema not found for table: $tableName';
    } catch (e) {
      return '-- Error getting schema for table $tableName: $e';
    }
  }
  
  /// Get table column information
  static Future<List<Map<String, dynamic>>> _getTableInfo(Database db, String tableName) async {
    try {
      return await db.rawQuery('PRAGMA table_info($tableName)');
    } catch (e) {
      log('Error getting table info for $tableName: $e');
      return [];
    }
  }
  
  /// Get table row count
  static Future<int> _getTableRowCount(Database db, String tableName) async {
    try {
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
      return result.first['count'] as int? ?? 0;
    } catch (e) {
      log('Error getting row count for $tableName: $e');
      return 0;
    }
  }
  
  /// Get table data as INSERT statements
  static Future<List<String>> _getTableData(Database db, String tableName) async {
    try {
      final data = await db.query(tableName);
      final insertStatements = <String>[];
      
      for (final row in data) {
        final columns = row.keys.join(', ');
        final values = row.values.map((value) {
          if (value == null) return 'NULL';
          if (value is String) return "'${value.replaceAll("'", "''")}'";
          return value.toString();
        }).join(', ');
        
        insertStatements.add('INSERT INTO $tableName ($columns) VALUES ($values);');
      }
      
      return insertStatements;
    } catch (e) {
      log('Error getting data for $tableName: $e');
      return ['-- Error getting data for table $tableName: $e'];
    }
  }
}
