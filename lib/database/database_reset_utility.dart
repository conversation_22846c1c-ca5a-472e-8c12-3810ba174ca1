import 'dart:developer';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'database_helper.dart';
import 'database_migration_helper.dart';

/// Utility class for database reset and debugging operations
class DatabaseResetUtility {
  /// Reset the entire database (delete and recreate)
  static Future<void> resetDatabase() async {
    try {
      log('Starting database reset...');

      // Close existing database connection
      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;
      await db.close();

      // Get database path
      late String path;
      if (Platform.isAndroid || Platform.isIOS) {
        final dbPath = await getDatabasesPath();
        path = join(dbPath, 'coffee_pos.db');
      } else if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        final dir = await getApplicationDocumentsDirectory();
        path = join(dir.path, 'coffee_pos.db');
      }

      // Delete database file
      final dbFile = File(path);
      if (await dbFile.exists()) {
        await dbFile.delete();
        log('Database file deleted: $path');
      }

      // Recreate database
      // DataBaseHelper._database = null; // Reset singleton
      final newDb = await dbHelper.database;
      log('Database recreated successfully');

      // Print table list for verification
      await DatabaseMigrationHelper.printAllTables(newDb);
    } catch (e) {
      log('Error resetting database: $e');
      rethrow;
    }
  }

  /// Fix existing database issues without full reset
  static Future<void> fixDatabaseIssues() async {
    try {
      log('Starting database issue fix...');

      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;

      // Fix duplicate table issues
      await DatabaseMigrationHelper.fixDuplicateTableIssue(db);

      // Print current state
      await DatabaseMigrationHelper.printAllTables(db);

      log('Database issues fixed');
    } catch (e) {
      log('Error fixing database issues: $e');
      rethrow;
    }
  }

  /// Check database health and print diagnostics
  static Future<void> checkDatabaseHealth() async {
    try {
      log('=== Database Health Check ===');

      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;

      // Print all tables
      await DatabaseMigrationHelper.printAllTables(db);

      // Check specific problematic tables
      final workspaceExists = await DatabaseMigrationHelper.tableExists(db, 'WorkSpaceSettings');
      final deviceExists = await DatabaseMigrationHelper.tableExists(db, 'DeviceSettings');

      log('WorkSpaceSettings table exists: $workspaceExists');
      log('DeviceSettings table exists: $deviceExists');

      if (workspaceExists) {
        await DatabaseMigrationHelper.printTableSchema(db, 'WorkSpaceSettings');
      }

      if (deviceExists) {
        await DatabaseMigrationHelper.printTableSchema(db, 'DeviceSettings');
      }

      log('=== Health Check Complete ===');
    } catch (e) {
      log('Error during health check: $e');
    }
  }

  /// Clean up duplicate or problematic tables
  static Future<void> cleanupTables() async {
    try {
      log('Starting table cleanup...');

      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;

      // Get all tables
      final tables = await DatabaseMigrationHelper.getAllTables(db);
      log('Found ${tables.length} tables');

      // Look for potential duplicates or issues
      final workspaceSettingsTables = tables
          .where((t) => t.toLowerCase().contains('workspace') || t.toLowerCase().contains('settings'))
          .toList();

      log('Workspace/Settings related tables: $workspaceSettingsTables');

      // You can add specific cleanup logic here if needed

      log('Table cleanup completed');
    } catch (e) {
      log('Error during table cleanup: $e');
    }
  }

  /// Export database schema for debugging
  static Future<String> exportDatabaseSchema() async {
    try {
      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;

      final tables = await DatabaseMigrationHelper.getAllTables(db);
      final schema = StringBuffer();

      schema.writeln('=== Database Schema Export ===');
      schema.writeln('Total tables: ${tables.length}');
      schema.writeln('');

      for (String tableName in tables) {
        schema.writeln('Table: $tableName');
        final result = await db.rawQuery('PRAGMA table_info($tableName)');
        for (var column in result) {
          schema.writeln(
              '  ${column['name']}: ${column['type']} ${column['notnull'] == 1 ? 'NOT NULL' : ''} ${column['pk'] == 1 ? 'PRIMARY KEY' : ''}');
        }
        schema.writeln('');
      }

      schema.writeln('=== End Schema Export ===');

      final schemaString = schema.toString();
      log(schemaString);

      return schemaString;
    } catch (e) {
      log('Error exporting schema: $e');
      return 'Error exporting schema: $e';
    }
  }

  /// Quick fix for the WorkSpaceSettings table conflict
  static Future<void> quickFixWorkspaceSettingsConflict() async {
    try {
      log('Applying quick fix for WorkSpaceSettings conflict...');

      final dbHelper = DataBaseHelper();
      final db = await dbHelper.database;

      // Check if WorkSpaceSettings exists
      final exists = await DatabaseMigrationHelper.tableExists(db, 'WorkSpaceSettings');
      if (!exists) {
        log('WorkSpaceSettings table does not exist - no fix needed');
        return;
      }

      // Get table schema to determine what type of table it is
      final result = await db.rawQuery('PRAGMA table_info(WorkSpaceSettings)');
      final columnNames = result.map((col) => col['name'] as String).toList();

      log('WorkSpaceSettings columns: $columnNames');

      if (columnNames.contains('settingKey') && columnNames.contains('settingValue')) {
        // This is actually a device settings table
        log('WorkSpaceSettings appears to be device settings - renaming to DeviceSettings');

        // Check if DeviceSettings already exists
        final deviceExists = await DatabaseMigrationHelper.tableExists(db, 'DeviceSettings');
        if (deviceExists) {
          log('DeviceSettings already exists - dropping WorkSpaceSettings');
          await db.execute('DROP TABLE WorkSpaceSettings');
        } else {
          log('Renaming WorkSpaceSettings to DeviceSettings');
          await db.execute('ALTER TABLE WorkSpaceSettings RENAME TO DeviceSettings');
        }
      } else {
        log('WorkSpaceSettings appears to be workspace data - keeping as is');
      }

      log('Quick fix completed');
    } catch (e) {
      log('Error in quick fix: $e');
    }
  }
}
