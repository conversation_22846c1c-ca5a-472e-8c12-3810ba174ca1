import 'dart:developer';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

/// Helper class for database migrations and table management
class DatabaseMigrationHelper {
  
  /// Check if a table exists in the database
  static Future<bool> tableExists(Database db, String tableName) async {
    try {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      );
      return result.isNotEmpty;
    } catch (e) {
      log('Error checking table existence for $tableName: $e');
      return false;
    }
  }

  /// Create table if it doesn't exist
  static Future<void> createTableIfNotExists(Database db, String tableName, String createTableSQL) async {
    try {
      final exists = await tableExists(db, tableName);
      if (!exists) {
        await db.execute(createTableSQL);
        log('Created table: $tableName');
      } else {
        log('Table already exists: $tableName');
      }
    } catch (e) {
      log('Error creating table $tableName: $e');
      // Don't rethrow - let the app continue with existing table
    }
  }

  /// Drop table if it exists
  static Future<void> dropTableIfExists(Database db, String tableName) async {
    try {
      final exists = await tableExists(db, tableName);
      if (exists) {
        await db.execute('DROP TABLE IF EXISTS $tableName');
        log('Dropped table: $tableName');
      }
    } catch (e) {
      log('Error dropping table $tableName: $e');
    }
  }

  /// Get list of all tables in the database
  static Future<List<String>> getAllTables(Database db) async {
    try {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      return result.map((row) => row['name'] as String).toList();
    } catch (e) {
      log('Error getting table list: $e');
      return [];
    }
  }

  /// Print all tables in the database (for debugging)
  static Future<void> printAllTables(Database db) async {
    try {
      final tables = await getAllTables(db);
      log('=== Database Tables ===');
      for (int i = 0; i < tables.length; i++) {
        log('${i + 1}. ${tables[i]}');
      }
      log('Total tables: ${tables.length}');
      log('=====================');
    } catch (e) {
      log('Error printing tables: $e');
    }
  }

  /// Check table schema
  static Future<void> printTableSchema(Database db, String tableName) async {
    try {
      final exists = await tableExists(db, tableName);
      if (!exists) {
        log('Table $tableName does not exist');
        return;
      }

      final result = await db.rawQuery('PRAGMA table_info($tableName)');
      log('=== Schema for $tableName ===');
      for (var column in result) {
        log('${column['name']}: ${column['type']} ${column['notnull'] == 1 ? 'NOT NULL' : ''} ${column['pk'] == 1 ? 'PRIMARY KEY' : ''}');
      }
      log('============================');
    } catch (e) {
      log('Error getting schema for $tableName: $e');
    }
  }

  /// Safe migration execution with error handling
  static Future<void> executeMigrationScript(Database db, String script, String description) async {
    try {
      await db.execute(script);
      log('Migration success: $description');
    } catch (e) {
      log('Migration error ($description): $e');
      // Check if it's a "table already exists" error
      if (e.toString().contains('already exists')) {
        log('Table already exists - continuing migration');
      } else {
        // For other errors, you might want to handle differently
        log('Non-critical migration error - continuing');
      }
    }
  }

  /// Fix duplicate table names issue
  static Future<void> fixDuplicateTableIssue(Database db) async {
    try {
      log('Checking for duplicate table issues...');
      
      // Check if WorkSpaceSettings table exists
      final workspaceSettingsExists = await tableExists(db, 'WorkSpaceSettings');
      final deviceSettingsExists = await tableExists(db, 'DeviceSettings');
      
      log('WorkSpaceSettings exists: $workspaceSettingsExists');
      log('DeviceSettings exists: $deviceSettingsExists');
      
      if (workspaceSettingsExists && !deviceSettingsExists) {
        // Check the schema to determine which table it actually is
        final result = await db.rawQuery('PRAGMA table_info(WorkSpaceSettings)');
        final columnNames = result.map((col) => col['name'] as String).toList();
        
        if (columnNames.contains('workSpaceUserName') && columnNames.contains('workSpaceName')) {
          // This is actually the workspace table, not device settings
          log('WorkSpaceSettings contains workspace data - this is correct');
        } else if (columnNames.contains('settingKey') && columnNames.contains('settingValue')) {
          // This is actually device settings table with wrong name
          log('WorkSpaceSettings contains device settings data - renaming to DeviceSettings');
          await db.execute('ALTER TABLE WorkSpaceSettings RENAME TO DeviceSettings');
        }
      }
      
      log('Duplicate table issue check completed');
    } catch (e) {
      log('Error fixing duplicate table issue: $e');
    }
  }
}
