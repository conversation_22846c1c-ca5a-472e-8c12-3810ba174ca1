import 'dart:developer';

import 'package:sqflite/sqflite.dart';

import '../entities/sales_transaction_dto.dart';
import '../table_columns.dart';

class SalesTransactionDao {
  final Database _db;
  SalesTransactionDao(this._db);

  Future<int> insertTransaction(SalesTransactionDto transaction) async {
    try {
      return await _db.insert(salesTransactionTable, transaction.toMap());
    } catch (e) {
      log('Error inserting transaction: $e');
      return -1; // Return -1 to indicate failure
    }
  }

  Future<int> insertMultipleTransactions(List<SalesTransactionDto> transactions) async {
    try {
      Batch batch = _db.batch();
      for (var transaction in transactions) {
        batch.insert(salesTransactionTable, transaction.toMap());
      }
      await batch.commit(noResult: true);
      return transactions.length; // Return the count of inserted records
    } catch (e) {
      log('Error inserting multiple transactions: $e');
      return -1;
    }
  }

  Future<List<SalesTransactionDto>> getTransactionsBySalesId(String salesId) async {
    try {
      final List<Map<String, dynamic>> result = await _db.query(
        salesTransactionTable,
        where: 'salesId = ?',
        whereArgs: [salesId],
      );
      return result.map((e) => SalesTransactionDto.fromMap(e)).toList();
    } catch (e) {
      log('Error fetching transactions: $e');
      return [];
    }
  }

  Future<int> updateTransaction(SalesTransactionDto transaction) async {
    try {
      return await _db.update(
        salesTransactionTable,
        transaction.toMap(),
        where: 'id = ?',
        whereArgs: [transaction.id],
      );
    } catch (e) {
      log('Error updating transaction: $e');
      return -1;
    }
  }

  Future<int> deleteTransaction(int id) async {
    try {
      return await _db.delete(salesTransactionTable, where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      log('Error deleting transaction: $e');
      return -1;
    }
  }

  Future<List<SalesTransactionDto>> getAllTransactions() async {
    try {
      final List<Map<String, dynamic>> result = await _db.query(
        salesTransactionTable,
        where: "datetime(createdAt) >= datetime('now', '-2 days')",
      );
      return result.map((e) => SalesTransactionDto.fromMap(e)).toList();
    } catch (e) {
      log('Error fetching all transactions: $e');
      return [];
    }
  }

  Future<List<SalesTransactionDto>> getTransactionsBetweenDates(DateTime from, DateTime to) async {
  try {
    final List<Map<String, dynamic>> result = await _db.query(
      salesTransactionTable,
      where: 'createdAt BETWEEN ? AND ?',
      whereArgs: [from.toIso8601String(), to.toIso8601String()],
    );
    return result.map((e) => SalesTransactionDto.fromMap(e)).toList();
  } catch (e) {
    log('Error fetching transactions between dates: $e');
    return [];
  }
}
}
