import 'dart:developer';

import 'package:sqflite/sqflite.dart';
import '../entities/raw_metrials_dto.dart';
import '../table_columns.dart';
import '../table_columns.dart' as caldb;

class RawMaterialDao {
  final Database db;
  RawMaterialDao(this.db);

  // Insert a new raw material
  Future<int> insert(RawMaterialDto dto) async {
    return await db.insert(rawMaterialTable, dto.toMap());
  }

  // Get all raw materials
  Future<List<RawMaterialDto>> getAll() async {
    final List<Map<String, dynamic>> maps = await db.query(rawMaterialTable);
    return maps.map((map) => RawMaterialDto.fromMap(map)).toList();
  }

  // Check if RawMaterial already exists for the given productId
  Future<bool> existsWithProductId(String rid, String productId) async {
    var result = await db.query(
      rawMaterialTable,
      where: '${caldb.rawMaterialId}= ? AND ${caldb.productId} = ?',
      whereArgs: [rid, productId],
    );
    return result.isNotEmpty; // Return true if productId exists
  }

  Future<void> updateQuantity(String productId, String rawMetId, double newQuantity, String uUnit) async {
    try {
      final count = await db.update(
        rawMaterialTable,
        {'quantity': newQuantity, 'unit': uUnit},
        where: '${caldb.rawMaterialId} = ? AND ${caldb.productId} = ?',
        whereArgs: [rawMetId, productId],
      );

      if (count == 0) {
        log('⚠️ No rows updated. Check if rawMetId: $rawMetId and productId: $productId exist.');
      } else {
        log('✅ Quantity updated successfully for rawMetId: $rawMetId, productId: $productId');
      }
    } catch (e) {
      log('❌ Error updating quantity: $e');
    }
  }

  Future<List<RawMaterialDto>> getAllRawmetrialByProductIdFromDB(String productId) async {
    final List<Map<String, dynamic>> maps = await db.query(
      rawMaterialTable,
      where: '${caldb.productId} = ?',
      whereArgs: [productId],
    );

    return maps.map((map) => RawMaterialDto.fromMap(map)).toList();
  }
}
