import 'dart:developer';

import 'package:coffee_cofe/database/entities/inventory_category_dto.dart';
import 'package:coffee_cofe/database/table_columns.dart';
import 'package:coffee_cofe/database/table_columns.dart' as dbCols;
import 'package:sqflite/sqflite.dart';

import '../../features/bill/billing_screen_provider.dart';

class InventoryDao {
  final Database dbS;

  InventoryDao(this.dbS);

  Future<int> insertInventory(InventoryDto inventoryList) async {
    return await dbS.insert(inventoryTable, inventoryList.toJson());
  }

  Future<List<InventoryDto>> getAllInventoryList() async {
    final List<Map<String, dynamic>> maps = await dbS.query(inventoryTable);
    return maps.map((map) => InventoryDto.fromJson(map)).toList();
  }

  Future<int> updateInventory(InventoryDto inventory) async {
    // Ensure you're not passing null values where the database doesn't accept them
    final updateData = inventory.toJson();

    // If 'id' is auto-increment or should not be updated, you may want to remove it
    updateData.remove(id); // Remove 'id' from the update data if it's auto-increment

    // Execute the update query
    final result = await dbS.update(
      inventoryTable,
      updateData,
      where: '$inventoryId = ?', // Use 'inventoryId' to match
      whereArgs: [inventory.inventoryId],
    );

    // Return the number of affected rows (int)
    return result;
  }

  Future<int> updateInventoryStockQty(List<ProductWithQty> products, {bool allowNegative = true}) async {
    try {
      int totalUpdated = 0;

      // Iterate over each ProductWithQty in the list
      for (var product in products) {
        // Fetch the RawMaterial records that match the productId
        final rawMaterialRecords = await dbS.query(
          dbCols.rawMaterialTable, // Table name for raw materials
          where: '${dbCols.productId} = ?', // Match the productId
          whereArgs: [product.productId],
        );

        if (rawMaterialRecords.isEmpty) {
          log("No RawMaterial records found for productId: ${product.productId}");
          continue; // Skip if no matching RawMaterial record is found
        }

        // Iterate through each RawMaterial record to calculate stock reduction
        for (var rawMaterial in rawMaterialRecords) {
          String inventoryId = (rawMaterial[dbCols.inventoryId] as String?) ?? '';
          // String inventoryUnit = (rawMaterial[dbCols.rawMaterialUnit] as String?) ?? ''; // Get inventory unit
          String inventoryUnit = (rawMaterial[dbCols.rawMaterialUnit] as String?)?.toLowerCase() ?? '';

          // Fetch the current inventory record for the given inventoryId
          final currentInventory = await dbS.query(
            dbCols.inventoryTable, // Assuming the table name is 'Inventory'
            where: '${dbCols.inventoryId} = ?', // Ensure the correct column is being used
            whereArgs: [inventoryId], // Use the correct column (e.g., 'id' instead of 'inventoryId')
          );

          if (currentInventory.isEmpty) {
            log("No inventory record found for inventoryId: $inventoryId");
            continue; // Skip if no inventory found for this inventoryId
          }

          // Get the current quantity from the inventory and unit
          double currentQty =
              double.tryParse(currentInventory.first[dbCols.inventoryQunatity] as String? ?? '') ?? 0.0;

          // String currentUnit = (currentInventory.first[dbCols.inventoryUnit] as String?) ?? '';
          String currentUnit = (currentInventory.first[dbCols.inventoryUnit] as String?)?.toLowerCase() ?? '';

          // Calculate the quantity needed based on units
          double quantityNeeded = product.qty != null
              ? product.qty! *
                  (double.tryParse((rawMaterial[dbCols.recipeQuantity] as String?) ?? '0.0') ?? 0.0)
              : 0.0;
          // Check if the unit in RawMaterial matches the unit in the inventory
          if (inventoryUnit != currentUnit) {
            log("Unit mismatch: RawMaterial unit: $inventoryUnit, Inventory unit: $currentUnit");
            quantityNeeded = convertUnits(quantityNeeded, inventoryUnit, currentUnit);
          }

          // Check if the stock is sufficient to reduce
          if (!allowNegative && currentQty < quantityNeeded) {
            log("Not enough stock to reduce and negative stock is not allowed for inventoryId: $inventoryId");
            continue; // Skip if not enough stock and negative stock is not allowed
          }

          // Calculate the new quantity after reducing the stock
          double updatedQty = currentQty - quantityNeeded;

          // If negative stock is not allowed and updated quantity is below zero, set it to zero
          if (!allowNegative && updatedQty < 0) {
            updatedQty = 0;
          }

          // Prepare the update data for inventory
          final updateData = {
            dbCols.inventoryQunatity: updatedQty.toString(),
          };

          // Execute the update query
          final result = await dbS.update(
            dbCols.inventoryTable, // Assuming the table name is 'Inventory'
            updateData,
            where:
                '${dbCols.inventoryId} = ?', // Use the correct column name (e.g., 'id' instead of 'inventoryId')
            whereArgs: [inventoryId],
          );

          if (result > 0) {
            totalUpdated++;
            log("Updated inventory stock for inventoryId: $inventoryId, new quantity: $updatedQty");
          }
        }
      }

      // Return the total number of rows updated
      return totalUpdated;
    } catch (e) {
      log("Error in updating inventory stock quantity: $e");
      return 0; // Return 0 on error
    }
  }

  double convertUnits(double quantity, String fromUnit, String toUnit) {
    fromUnit = fromUnit.toLowerCase();
    toUnit = toUnit.toLowerCase();

    // Handle plural forms by removing "s"
    if (fromUnit.endsWith('s')) fromUnit = fromUnit.substring(0, fromUnit.length - 1);
    if (toUnit.endsWith('s')) toUnit = toUnit.substring(0, toUnit.length - 1);

    // Liquid conversions
    if (fromUnit == 'ml' && toUnit == 'liter') return quantity / 1000;
    if (fromUnit == 'liter' && toUnit == 'ml') return quantity * 1000;

    // Weight conversions
    if (fromUnit == 'gram' && toUnit == 'kg') return quantity / 1000;
    if (fromUnit == 'kg' && toUnit == 'gram') return quantity * 1000;

    // Ounces
    if (fromUnit == 'ounce' && toUnit == 'gram') return quantity * 28.3495;
    if (fromUnit == 'gram' && toUnit == 'ounce') return quantity / 28.3495;
    if (fromUnit == 'ounce' && toUnit == 'kg') return quantity / 35.274;
    if (fromUnit == 'kg' && toUnit == 'ounce') return quantity * 35.274;

    // Pieces to packs conversion
    if (fromUnit == 'piece' && toUnit == 'pack') return quantity / 10; // Assume 10 pieces = 1 pack
    if (fromUnit == 'pack' && toUnit == 'piece') return quantity * 10;

    // If no conversion exists, return the original quantity
    log('⚠️ No conversion found for $fromUnit to $toUnit, returning original quantity');
    return quantity;
  }
}
