import 'dart:developer';
import 'package:sqflite/sqflite.dart';
import '../../models/discounts.dart';
import '../table_columns.dart';

class EnhancedDiscountDao {
  final Database _db;

  EnhancedDiscountDao(this._db);

  /// Insert a new discount
  Future<int> insertDiscount(Discounts discount) async {
    return await _db.insert(discountTable, discount.toMap());
  }

  /// Update an existing discount
  Future<int> updateDiscount(Discounts discount) async {
    return await _db.update(
      discountTable,
      discount.toMap(),
      where: '$discountID = ?',
      whereArgs: [discount.discountID],
    );
  }

  /// Get all discounts with pagination
  Future<List<Discounts>> getDiscountsWithPagination({
    int limit = 20,
    int offset = 0,
    String? workspaceId,
    String? discountType,
    bool? isActive,
  }) async {
    String whereClause = 'WHERE 1=1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    if (discountType != null) {
      whereClause += ' AND discountType = ?';
      whereArgs.add(discountType);
    }

    if (isActive != null) {
      whereClause += ' AND $discountActive = ?';
      whereArgs.add(isActive ? 1 : 0);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM $discountTable $whereClause ORDER BY $discountPriority DESC, $discountCreatedDate DESC LIMIT ? OFFSET ?',
      [...whereArgs, limit, offset],
    );
    return result.map((e) => Discounts.fromMap(e)).toList();
  }

  /// Get total count of discounts
  Future<int> getTotalDiscountsCount({
    String? workspaceId,
    String? discountType,
    bool? isActive,
  }) async {
    String whereClause = 'WHERE 1=1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    if (discountType != null) {
      whereClause += ' AND discountType = ?';
      whereArgs.add(discountType);
    }

    if (isActive != null) {
      whereClause += ' AND $discountActive = ?';
      whereArgs.add(isActive ? 1 : 0);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT COUNT(*) as count FROM $discountTable $whereClause',
      whereArgs,
    );
    return result.first['count'] as int;
  }

  /// Get discount by ID
  Future<Discounts?> getDiscountById(String discountId) async {
    final List<Map<String, dynamic>> result = await _db.query(
      discountTable,
      where: '$discountID = ?',
      whereArgs: [discountId],
    );
    return result.isNotEmpty ? Discounts.fromMap(result.first) : null;
  }

  /// Get discount by coupon code
  Future<Discounts?> getDiscountByCouponCode(String couponCode) async {
    try {
      final List<Map<String, dynamic>> result = await _db.query(
        discountTable,
        where: '$discountCouponName = ? AND $discountActive = 1',
        whereArgs: [couponCode],
      );
      return result.isNotEmpty ? Discounts.fromMap(result.first) : null;
    } catch (e) {
      // Fallback to check if couponCode column exists
      try {
        final List<Map<String, dynamic>> result = await _db.query(
          discountTable,
          where: 'couponCode = ? AND $discountActive = 1',
          whereArgs: [couponCode],
        );
        return result.isNotEmpty ? Discounts.fromMap(result.first) : null;
      } catch (e2) {
        log('Error getting discount by coupon code: $e2');
        return null;
      }
    }
  }

  /// Get active discounts for auto-apply
  Future<List<Discounts>> getAutoApplyDiscounts({String? workspaceId}) async {
    String whereClause = 'WHERE $discountActive = 1 AND isAutoApply = 1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM $discountTable $whereClause ORDER BY $discountPriority DESC',
      whereArgs,
    );
    return result.map((e) => Discounts.fromMap(e)).toList();
  }

  /// Get applicable discounts for a product
  Future<List<Discounts>> getApplicableDiscountsForProduct({
    required String productId,
    String? categoryId,
    String? workspaceId,
  }) async {
    String whereClause = '''
      WHERE $discountActive = 1 
      AND (discountType = 'product_wise' OR discountType = 'manual')
      AND (applicableProducts IS NULL OR applicableProducts = '' OR applicableProducts LIKE '%$productId%')
    ''';
    List<dynamic> whereArgs = [];

    if (categoryId != null) {
      whereClause +=
          ' AND (applicableCategories IS NULL OR applicableCategories = \'\' OR applicableCategories LIKE ?)';
      whereArgs.add('%$categoryId%');
    }

    if (workspaceId != null) {
      whereClause += ' AND $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM $discountTable $whereClause ORDER BY $discountPriority DESC',
      whereArgs,
    );
    return result.map((e) => Discounts.fromMap(e)).toList();
  }

  /// Get sales-wise discounts
  Future<List<Discounts>> getSalesWiseDiscounts({String? workspaceId}) async {
    String whereClause = 'WHERE $discountActive = 1 AND discountType = \'sales_wise\'';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM $discountTable $whereClause ORDER BY $discountPriority DESC',
      whereArgs,
    );
    return result.map((e) => Discounts.fromMap(e)).toList();
  }

  /// Search discounts
  Future<List<Discounts>> searchDiscounts({
    required String query,
    String? workspaceId,
    int limit = 20,
    int offset = 0,
  }) async {
    String whereClause = '''
      WHERE (
        $discountCouponName LIKE ? OR 
        couponCode LIKE ? OR 
        description LIKE ?
      )
    ''';
    List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];

    if (workspaceId != null) {
      whereClause += ' AND $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM $discountTable $whereClause ORDER BY $discountPriority DESC LIMIT ? OFFSET ?',
      [...whereArgs, limit, offset],
    );
    return result.map((e) => Discounts.fromMap(e)).toList();
  }

  /// Get unsynchronized discounts
  Future<List<Discounts>> getUnsyncedDiscounts({int limit = 100}) async {
    final List<Map<String, dynamic>> result = await _db.query(
      discountTable,
      where: '$discountSync = ?',
      whereArgs: [0],
      orderBy: '$discountCreatedDate ASC',
      limit: limit,
    );
    return result.map((e) => Discounts.fromMap(e)).toList();
  }

  /// Mark discounts as synchronized
  Future<int> markAsSynced(List<String> discountIds) async {
    if (discountIds.isEmpty) return 0;

    final String placeholders = discountIds.map((_) => '?').join(',');
    return await _db.rawUpdate(
      'UPDATE $discountTable SET $discountSync = 1 WHERE $discountID IN ($placeholders)',
      discountIds,
    );
  }

  /// Increment coupon usage count
  Future<int> incrementCouponUsage(String discountId, {String? userId}) async {
    final discount = await getDiscountById(discountId);
    if (discount == null) return 0;

    discount.incrementUsage(userId: userId);
    return await updateDiscount(discount);
  }

  /// Delete discount
  Future<int> deleteDiscount(String discountId) async {
    return await _db.delete(
      discountTable,
      where: '$discountID = ?',
      whereArgs: [discountId],
    );
  }

  /// Get discount statistics
  Future<Map<String, dynamic>> getDiscountStatistics({String? workspaceId}) async {
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause = 'WHERE $workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      '''
      SELECT 
        COUNT(*) as totalDiscounts,
        COUNT(CASE WHEN $discountActive = 1 THEN 1 END) as activeDiscounts,
        COUNT(CASE WHEN discountType = 'coupon' THEN 1 END) as couponDiscounts,
        COUNT(CASE WHEN discountType = 'manual' THEN 1 END) as manualDiscounts,
        COUNT(CASE WHEN discountType = 'sales_wise' THEN 1 END) as salesWiseDiscounts,
        COUNT(CASE WHEN discountType = 'product_wise' THEN 1 END) as productWiseDiscounts,
        COUNT(CASE WHEN isAutoApply = 1 THEN 1 END) as autoApplyDiscounts
      FROM $discountTable $whereClause
      ''',
      whereArgs,
    );

    return result.first;
  }
}
