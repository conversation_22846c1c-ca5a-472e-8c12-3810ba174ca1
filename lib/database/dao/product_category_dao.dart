import 'package:sqflite/sqflite.dart';
import '../entities/product_category_dto.dart';
import '../table_columns.dart'; 

class ProductCategoryDao {
  final Database db;

  ProductCategoryDao(this.db);

  Future<int> insertProductCategory(ProductCategoryDto category) async {
    return await db.insert(productCategoryTable, category.toJson());
  }

  Future<int> updateProductCategory(ProductCategoryDto category) async {
    return await db.update(
      productCategoryTable,
      category.toJson(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteProductCategory(int id) async {
    return await db.delete(
      productCategoryTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<ProductCategoryDto>> getAllProductCategories() async {
    final List<Map<String, dynamic>> maps = await db.query(productCategoryTable);
    return maps.map((map) => ProductCategoryDto.fromJson(map)).toList();
  }

  Future<ProductCategoryDto?> getProductCategoryById(int id) async {
    final List<Map<String, dynamic>> maps = await db.query(
      productCategoryTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ProductCategoryDto.fromJson(maps.first);
    }
    return null;
  }
}
