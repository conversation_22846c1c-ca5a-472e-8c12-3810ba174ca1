import 'package:sqflite/sqflite.dart';
import '../entities/workspace_dto.dart';
import '../table_columns.dart';

class WorkspaceSettingsDao {
  final Database db;

  WorkspaceSettingsDao(this.db);

  Future<int> insert(WorkspaceSettingsDto dto) async {
    return await db.insert(workSpaceSettingsTable, dto.toMap());
  }

  Future<int> update(WorkspaceSettingsDto dto) async {
    return await db.update(
      workSpaceSettingsTable,
      dto.toMap(),
      where: '$wSSettingId = ?',
      whereArgs: [dto.id],
    );
  }

  Future<int> delete(int id) async {
    return await db.delete(
      workSpaceSettingsTable,
      where: '$wSSettingId = ?',
      whereArgs: [id],
    );
  }

  Future<List<WorkspaceSettingsDto>> getAll() async {
    final List<Map<String, dynamic>> maps = await db.query(workSpaceSettingsTable);
    return maps.map((map) => WorkspaceSettingsDto.fromMap(map)).toList();
  }

  Future<WorkspaceSettingsDto?> getById(int id) async {
    final List<Map<String, dynamic>> maps = await db.query(
      workSpaceSettingsTable,
      where: '$wSSettingId = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return WorkspaceSettingsDto.fromMap(maps.first);
    }
    return null;
  }
}
