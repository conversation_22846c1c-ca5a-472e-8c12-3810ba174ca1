import 'package:sqflite/sqflite.dart';
import '../entities/sales_dto.dart';
import '../table_columns.dart';

class SalesDao {
  final Database _db;

  SalesDao(this._db); // Dependency injection ensures better testability and flexibility

  Future<int> insertSale(SalesDto sale) async {
    return await _db.insert(salesTable, sale.toMap());
  }

  Future<List<SalesDto>> getAllSales() async {
    final List<Map<String, dynamic>> result = await _db.query(salesTable);
    return result.map((e) => SalesDto.fromMap(e)).toList();
  }

  Future<int> updateSale(SalesDto sale) async {
    return await _db.update(salesTable, sale.toMap(), where: '$salesId = ?', whereArgs: [sale.id]);
  }

  Future<int> deleteSale(int id) async {
    return await _db.delete(salesTable, where: '$salesId = ?', whereArgs: [id]);
  }

  Future<List<SalesDto>> getSalesBetweenDates(String fromDate, String toDate) async {
    final List<Map<String, dynamic>> result = await _db.query(
      salesTable,
      where: 'DATE(createdDate) BETWEEN ? AND ?',
      whereArgs: [fromDate, toDate],
    );
    return result.map((e) => SalesDto.fromMap(e)).toList();
  }
}
