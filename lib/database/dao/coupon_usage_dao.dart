import 'package:sqflite/sqflite.dart';
import '../entities/coupon_usage_dto.dart';

class CouponUsageDao {
  final Database _db;

  CouponUsageDao(this._db);

  /// Insert a new coupon usage record
  Future<int> insertCouponUsage(CouponUsageDto couponUsage) async {
    return await _db.insert('CouponUsage', couponUsage.toMap());
  }

  /// Get all coupon usage records for a specific discount
  Future<List<CouponUsageDto>> getCouponUsageByDiscountId(String discountId) async {
    final List<Map<String, dynamic>> result = await _db.query(
      'CouponUsage',
      where: 'discountID = ?',
      whereArgs: [discountId],
      orderBy: 'usedDate DESC',
    );
    return result.map((e) => CouponUsageDto.fromMap(e)).toList();
  }

  /// Get all coupon usage records for a specific coupon code
  Future<List<CouponUsageDto>> getCouponUsageByCouponCode(String couponCode) async {
    final List<Map<String, dynamic>> result = await _db.query(
      'CouponUsage',
      where: 'couponCode = ?',
      whereArgs: [couponCode],
      orderBy: 'usedDate DESC',
    );
    return result.map((e) => CouponUsageDto.fromMap(e)).toList();
  }

  /// Get coupon usage count for a specific coupon code
  Future<int> getCouponUsageCount(String couponCode) async {
    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT COUNT(*) as count FROM CouponUsage WHERE couponCode = ?',
      [couponCode],
    );
    return result.first['count'] as int;
  }

  /// Get coupon usage count for a specific user
  Future<int> getCouponUsageCountByUser(String couponCode, String userId) async {
    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT COUNT(*) as count FROM CouponUsage WHERE couponCode = ? AND usedBy = ?',
      [couponCode, userId],
    );
    return result.first['count'] as int;
  }

  /// Get all coupon usage records with pagination
  Future<List<CouponUsageDto>> getCouponUsageWithPagination({
    int limit = 20,
    int offset = 0,
    String? workspaceId,
  }) async {
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause = 'WHERE workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM CouponUsage $whereClause ORDER BY usedDate DESC LIMIT ? OFFSET ?',
      [...whereArgs, limit, offset],
    );
    return result.map((e) => CouponUsageDto.fromMap(e)).toList();
  }

  /// Get total count of coupon usage records
  Future<int> getTotalCouponUsageCount({String? workspaceId}) async {
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause = 'WHERE workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT COUNT(*) as count FROM CouponUsage $whereClause',
      whereArgs,
    );
    return result.first['count'] as int;
  }

  /// Delete coupon usage records older than specified date
  Future<int> deleteOldCouponUsage(String beforeDate) async {
    return await _db.delete(
      'CouponUsage',
      where: 'usedDate < ?',
      whereArgs: [beforeDate],
    );
  }

  /// Get coupon usage analytics
  Future<Map<String, dynamic>> getCouponUsageAnalytics({
    String? workspaceId,
    String? fromDate,
    String? toDate,
  }) async {
    String whereClause = 'WHERE 1=1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    if (fromDate != null) {
      whereClause += ' AND usedDate >= ?';
      whereArgs.add(fromDate);
    }

    if (toDate != null) {
      whereClause += ' AND usedDate <= ?';
      whereArgs.add(toDate);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      '''
      SELECT 
        COUNT(*) as totalUsage,
        COUNT(DISTINCT couponCode) as uniqueCoupons,
        COUNT(DISTINCT usedBy) as uniqueUsers,
        SUM(discountAmount) as totalDiscountAmount,
        AVG(discountAmount) as avgDiscountAmount,
        SUM(orderAmount) as totalOrderAmount
      FROM CouponUsage $whereClause
      ''',
      whereArgs,
    );

    return result.first;
  }
}
