import 'package:coffee_cofe/database/table_columns.dart' as db_cols;
import 'package:sqflite/sqflite.dart';
import '../entities/product.dart';

class ProductDAO {
  final Database _db;

  ProductDAO(this._db);

  /// Insert Product
  Future<int> insertProduct(ProductDto product) async {
    return await _db.insert(
      db_cols.productsTable,
      product.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get All Products
  Future<List<ProductDto>> getAllProducts() async {
    final List<Map<String, dynamic>> maps = await _db.query(db_cols.productsTable);
    return maps.map((e) => ProductDto.fromMap(e)).toList();
  }

  /// Get Product by ID
  Future<ProductDto?> getProductById(String productId) async {
    final List<Map<String, dynamic>> maps = await _db.query(
      db_cols.productsTable,
      where: '${db_cols.productId} = ?',
      whereArgs: [productId],
    );

    if (maps.isNotEmpty) {
      return ProductDto.fromMap(maps.first);
    }
    return null;
  }

  /// Update Product
  Future<int> updateProduct(ProductDto product) async {
    return await _db.update(
      db_cols.productsTable,
      product.toMap(),
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  /// Delete Product
  Future<int> deleteProduct(int id) async {
    return await _db.delete(
      db_cols.productsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
