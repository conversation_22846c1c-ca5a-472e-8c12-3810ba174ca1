import 'package:sqflite/sqflite.dart';
import '../entities/discount_log_dto.dart';

class DiscountLogDao {
  final Database _db;

  DiscountLogDao(this._db);

  /// Insert a new discount log record
  Future<int> insertDiscountLog(DiscountLogDto discountLog) async {
    return await _db.insert('DiscountLog', discountLog.toMap());
  }

  /// Get all discount logs for a specific sales transaction
  Future<List<DiscountLogDto>> getDiscountLogsBySalesId(String salesId) async {
    final List<Map<String, dynamic>> result = await _db.query(
      'DiscountLog',
      where: 'salesId = ?',
      whereArgs: [salesId],
      orderBy: 'appliedDate DESC',
    );
    return result.map((e) => DiscountLogDto.fromMap(e)).toList();
  }

  /// Get all discount logs for a specific discount
  Future<List<DiscountLogDto>> getDiscountLogsByDiscountId(String discountId) async {
    final List<Map<String, dynamic>> result = await _db.query(
      'DiscountLog',
      where: 'discountID = ?',
      whereArgs: [discountId],
      orderBy: 'appliedDate DESC',
    );
    return result.map((e) => DiscountLogDto.fromMap(e)).toList();
  }

  /// Get discount logs with pagination
  Future<List<DiscountLogDto>> getDiscountLogsWithPagination({
    int limit = 20,
    int offset = 0,
    String? workspaceId,
    String? discountType,
    String? fromDate,
    String? toDate,
  }) async {
    String whereClause = 'WHERE 1=1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    if (discountType != null) {
      whereClause += ' AND discountType = ?';
      whereArgs.add(discountType);
    }

    if (fromDate != null) {
      whereClause += ' AND appliedDate >= ?';
      whereArgs.add(fromDate);
    }

    if (toDate != null) {
      whereClause += ' AND appliedDate <= ?';
      whereArgs.add(toDate);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT * FROM DiscountLog $whereClause ORDER BY appliedDate DESC LIMIT ? OFFSET ?',
      [...whereArgs, limit, offset],
    );
    return result.map((e) => DiscountLogDto.fromMap(e)).toList();
  }

  /// Get total count of discount logs
  Future<int> getTotalDiscountLogsCount({
    String? workspaceId,
    String? discountType,
    String? fromDate,
    String? toDate,
  }) async {
    String whereClause = 'WHERE 1=1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    if (discountType != null) {
      whereClause += ' AND discountType = ?';
      whereArgs.add(discountType);
    }

    if (fromDate != null) {
      whereClause += ' AND appliedDate >= ?';
      whereArgs.add(fromDate);
    }

    if (toDate != null) {
      whereClause += ' AND appliedDate <= ?';
      whereArgs.add(toDate);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      'SELECT COUNT(*) as count FROM DiscountLog $whereClause',
      whereArgs,
    );
    return result.first['count'] as int;
  }

  /// Get discount analytics
  Future<Map<String, dynamic>> getDiscountAnalytics({
    String? workspaceId,
    String? fromDate,
    String? toDate,
  }) async {
    String whereClause = 'WHERE 1=1';
    List<dynamic> whereArgs = [];

    if (workspaceId != null) {
      whereClause += ' AND workSpaceId = ?';
      whereArgs.add(workspaceId);
    }

    if (fromDate != null) {
      whereClause += ' AND appliedDate >= ?';
      whereArgs.add(fromDate);
    }

    if (toDate != null) {
      whereClause += ' AND appliedDate <= ?';
      whereArgs.add(toDate);
    }

    final List<Map<String, dynamic>> result = await _db.rawQuery(
      '''
      SELECT 
        COUNT(*) as totalApplications,
        COUNT(DISTINCT discountID) as uniqueDiscounts,
        COUNT(DISTINCT salesId) as affectedSales,
        SUM(discountAmount) as totalDiscountAmount,
        AVG(discountAmount) as avgDiscountAmount,
        SUM(originalAmount) as totalOriginalAmount,
        SUM(finalAmount) as totalFinalAmount,
        discountType,
        COUNT(*) as typeCount
      FROM DiscountLog $whereClause
      GROUP BY discountType
      ''',
      whereArgs,
    );

    return {
      'summary': result.isNotEmpty ? result.first : {},
      'byType': result,
    };
  }

  /// Get unsynchronized discount logs
  Future<List<DiscountLogDto>> getUnsyncedDiscountLogs({int limit = 100}) async {
    final List<Map<String, dynamic>> result = await _db.query(
      'DiscountLog',
      where: 'syncStatus = ?',
      whereArgs: [0],
      orderBy: 'appliedDate ASC',
      limit: limit,
    );
    return result.map((e) => DiscountLogDto.fromMap(e)).toList();
  }

  /// Mark discount logs as synchronized
  Future<int> markAsSynced(List<int> ids) async {
    if (ids.isEmpty) return 0;
    
    final String placeholders = ids.map((_) => '?').join(',');
    return await _db.rawUpdate(
      'UPDATE DiscountLog SET syncStatus = 1 WHERE id IN ($placeholders)',
      ids,
    );
  }

  /// Delete old discount logs
  Future<int> deleteOldDiscountLogs(String beforeDate) async {
    return await _db.delete(
      'DiscountLog',
      where: 'appliedDate < ? AND syncStatus = 1',
      whereArgs: [beforeDate],
    );
  }

  /// Update discount log
  Future<int> updateDiscountLog(DiscountLogDto discountLog) async {
    return await _db.update(
      'DiscountLog',
      discountLog.toMap(),
      where: 'id = ?',
      whereArgs: [discountLog.id],
    );
  }

  /// Delete discount log
  Future<int> deleteDiscountLog(int id) async {
    return await _db.delete(
      'DiscountLog',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
