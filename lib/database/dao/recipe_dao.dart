import 'dart:developer';

import 'package:sqflite/sqflite.dart';
import '../entities/recipe_dto.dart';
import '../table_columns.dart';

class RecipeDao {
  final Database db;
  RecipeDao(this.db);

  // Insert a new recipe into the database
  Future<void> insert(RecipeDto dto) async {
    try {
      await db.insert(
        recipeTable,
        dto.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace, // Handle conflicts (optional)
      );
    } catch (e) {
      log('Error inserting recipe: $e');
      rethrow; // Optional: Re-throw to handle higher up the stack
    }
  }

  // Get recipes by product ID
  Future<List<RecipeDto>> getRecipeByProduct(String productId) async {
    try {
      final result = await db.query(
        recipeTable,
        where: 'product_id = ?',
        whereArgs: [productId],
      );

      return result.map((e) => RecipeDto.fromMap(e)).toList();
    } catch (e) {
      log('Error fetching recipes for product $productId: $e');
      return []; // Return an empty list in case of error
    }
  }
}
