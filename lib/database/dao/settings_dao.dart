import 'package:sqflite/sqflite.dart';
import '../entities/settings_dto.dart';
import '../table_columns.dart';

class SettingsDAO {
  final Database _db;

  SettingsDAO(this._db);

  /// Insert or Update a Setting
  Future<int> insertOrUpdateSetting(SettingsDto setting) async {
    return await _db.insert(
      settingsTable,
      setting.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get All Settings for a Specific Shop
  Future<List<SettingsDto>> getAllSettings(String shopUId) async {
    final List<Map<String, dynamic>> maps = await _db.query(
      settingsTable,
      where: '$dbShopUId = ?',
      whereArgs: [shopUId],
    );
    return maps.map((e) => SettingsDto.fromMap(e)).toList();
  }

  /// Get a Specific Setting by Key and Shop ID
  Future<SettingsDto?> getSettingByKey(String keyName, String shopUId) async {
    final List<Map<String, dynamic>> maps = await _db.query(
      settingsTable,
      where: '$dbKeyName = ? AND $dbShopUId = ?',
      whereArgs: [keyName, shopUId],
    );
    return maps.isNotEmpty ? SettingsDto.fromMap(maps.first) : null;
  }

  /// Delete a Specific Setting
  Future<int> deleteSetting(String keyName, String shopUId) async {
    return await _db.delete(
      settingsTable,
      where: '$dbKeyName = ? AND $dbShopUId = ?',
      whereArgs: [keyName, shopUId],
    );
  }

  /// Delete All Settings for a Shop
  Future<int> deleteAllSettingsForShop(String shopUId) async {
    return await _db.delete(
      settingsTable,
      where: '$dbShopUId = ?',
      whereArgs: [shopUId],
    );
  }
    Future<void> saveSetting(SettingsDto setting) async {
    
    await _db.insert(
      settingsTable,
      setting.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

}
