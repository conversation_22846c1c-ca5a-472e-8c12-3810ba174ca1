import 'package:sqflite/sqflite.dart';
import '../entities/gst_configuration_dto.dart';

class GSTConfigurationDao {
  final Database db;
  static const String tableName = 'GSTConfiguration';

  GSTConfigurationDao(this.db);

  /// Insert or update GST configuration
  Future<int> insertOrUpdate(GSTConfigurationDto config) async {
    // First, deactivate all existing configurations for this workspace
    await db.update(
      tableName,
      {'isActive': 0, 'updatedDate': DateTime.now().toIso8601String()},
      where: 'workSpaceId = ? AND isActive = 1',
      whereArgs: [config.workSpaceId],
    );

    // Insert new configuration
    final configToInsert = config.copyWith(
      createdDate: DateTime.now().toIso8601String(),
      updatedDate: DateTime.now().toIso8601String(),
      isActive: true,
    );

    return await db.insert(
      tableName,
      configToInsert.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get active GST configuration for current workspace
  Future<GSTConfigurationDto?> getActiveConfiguration([String? workSpaceId]) async {
    final workspace = workSpaceId ?? 'current_workspace';
    
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: 'workSpaceId = ? AND isActive = 1',
      whereArgs: [workspace],
      orderBy: 'createdDate DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return GSTConfigurationDto.fromMap(maps.first);
    }
    return null;
  }

  /// Get all configurations for a workspace
  Future<List<GSTConfigurationDto>> getAllConfigurations([String? workSpaceId]) async {
    final workspace = workSpaceId ?? 'current_workspace';
    
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: 'workSpaceId = ?',
      whereArgs: [workspace],
      orderBy: 'createdDate DESC',
    );

    return maps.map((map) => GSTConfigurationDto.fromMap(map)).toList();
  }

  /// Get configuration by ID
  Future<GSTConfigurationDto?> getById(int id) async {
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return GSTConfigurationDto.fromMap(maps.first);
    }
    return null;
  }

  /// Update configuration
  Future<int> update(GSTConfigurationDto config) async {
    final updatedConfig = config.copyWith(
      updatedDate: DateTime.now().toIso8601String(),
    );

    return await db.update(
      tableName,
      updatedConfig.toMap(),
      where: 'id = ?',
      whereArgs: [config.id],
    );
  }

  /// Delete configuration
  Future<int> delete(int id) async {
    return await db.delete(
      tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Soft delete (deactivate) configuration
  Future<int> deactivate(int id) async {
    return await db.update(
      tableName,
      {
        'isActive': 0,
        'updatedDate': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Check if GST is configured for workspace
  Future<bool> isGSTConfigured([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config != null;
  }

  /// Get business GSTIN
  Future<String?> getBusinessGSTIN([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.businessGSTIN;
  }

  /// Get business state code
  Future<String?> getBusinessStateCode([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.businessStateCode;
  }

  /// Get default GST rate
  Future<double> getDefaultGSTRate([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.defaultGSTRate ?? 18.0;
  }

  /// Check if IGST is enabled
  Future<bool> isIGSTEnabled([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.enableIGST ?? true;
  }

  /// Check if Cess is enabled
  Future<bool> isCessEnabled([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.enableCess ?? false;
  }

  /// Check if Reverse Charge is enabled
  Future<bool> isReverseChargeEnabled([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.enableReverseCharge ?? false;
  }

  /// Check if tax inclusive pricing is enabled
  Future<bool> isTaxInclusivePricing([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.taxInclusivePricing ?? false;
  }

  /// Get rounding method
  Future<String> getRoundingMethod([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    return config?.roundingMethod ?? 'ROUND_OFF';
  }

  /// Get configurations that need sync
  Future<List<GSTConfigurationDto>> getUnsyncedConfigurations() async {
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: 'syncStatus = 0',
      orderBy: 'createdDate DESC',
    );

    return maps.map((map) => GSTConfigurationDto.fromMap(map)).toList();
  }

  /// Mark configuration as synced
  Future<int> markAsSynced(int id) async {
    return await db.update(
      tableName,
      {
        'syncStatus': 1,
        'updatedDate': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get configuration summary for display
  Future<Map<String, dynamic>> getConfigurationSummary([String? workSpaceId]) async {
    final config = await getActiveConfiguration(workSpaceId);
    
    if (config == null) {
      return {
        'configured': false,
        'businessName': null,
        'gstin': null,
        'stateCode': null,
        'defaultRate': 18.0,
        'features': <String>[],
      };
    }

    final features = <String>[];
    if (config.enableIGST) features.add('IGST');
    if (config.enableCess) features.add('Cess');
    if (config.enableReverseCharge) features.add('Reverse Charge');
    if (config.taxInclusivePricing) features.add('Tax Inclusive');

    return {
      'configured': true,
      'businessName': config.businessName,
      'gstin': config.businessGSTIN,
      'stateCode': config.businessStateCode,
      'defaultRate': config.defaultGSTRate,
      'features': features,
      'roundingMethod': config.roundingMethod,
    };
  }
}
