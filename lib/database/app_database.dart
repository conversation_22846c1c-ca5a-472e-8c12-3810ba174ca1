import 'package:coffee_cofe/database/dao/inventory_dao.dart';
import 'package:coffee_cofe/database/dao/product_category_dao.dart';
import 'package:sqflite/sqflite.dart';
import 'dao/raw_metrials_dao.dart';
import 'dao/recipe_dao.dart';
import 'dao/sales_dao.dart';
import 'dao/sales_transation_dao.dart';
import 'dao/settings_dao.dart';
import 'dao/workspace_dao.dart';
import 'dao/enhanced_discount_dao.dart';
import 'dao/coupon_usage_dao.dart';
import 'dao/discount_log_dao.dart';
import 'dao/gst_configuration_dao.dart';
import 'database_helper.dart';
import 'dao/product_dao.dart';

class AppDatabase {
  static final AppDatabase _instance = AppDatabase._internal();
  factory AppDatabase() => _instance;
  AppDatabase._internal();

  Database? _database;

  /// Initialize Database
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await DataBaseHelper().database;
    return _database!;
  }

  /// DAO Instances
  ProductDAO? _productDao;
  SettingsDAO? _settingsDao;
  SalesDao? _salesDao;
  SalesTransactionDao? _salesTransactionDao;
  ProductCategoryDao? _productCategoryDao;
  RawMaterialDao? _rawMaterialDao;
  RecipeDao? _recipeDao;
  InventoryDao? _inventoryDao;
  WorkspaceSettingsDao? _workspaceSettingsDao;
  EnhancedDiscountDao? _enhancedDiscountDao;
  CouponUsageDao? _couponUsageDao;
  DiscountLogDao? _discountLogDao;
  Future<ProductDAO> get productDao async {
    final db = await database;
    _productDao ??= ProductDAO(db);
    return _productDao!;
  }

  Future<SettingsDAO> get settingsDao async {
    final db = await database;
    _settingsDao ??= SettingsDAO(db);
    return _settingsDao!;
  }

  Future<SalesDao> get salesDao async {
    final db = await database;
    _salesDao ??= SalesDao(db);
    return _salesDao!;
  }

  Future<SalesTransactionDao> get salesTransactionDao async {
    final db = await database;
    _salesTransactionDao ??= SalesTransactionDao(db);
    return _salesTransactionDao!;
  }

  Future<ProductCategoryDao> get productCategoryDao async {
    final db = await database;
    _productCategoryDao ??= ProductCategoryDao(db);
    return _productCategoryDao!;
  }

  Future<RawMaterialDao> get rawMaterialDao async {
    final db = await database;
    _rawMaterialDao ??= RawMaterialDao(db);
    return _rawMaterialDao!;
  }

  Future<RecipeDao> get recipeDao async {
    final db = await database;
    _recipeDao ??= RecipeDao(db);
    return _recipeDao!;
  }

  Future<InventoryDao> get inventoryDao async {
    final db = await database;
    _inventoryDao ??= InventoryDao(db);
    return _inventoryDao!;
  }

  Future<WorkspaceSettingsDao> get workspaceSettingsDao async {
    final db = await database;
    _workspaceSettingsDao ??= WorkspaceSettingsDao(db);
    return _workspaceSettingsDao!;
  }

  Future<EnhancedDiscountDao> get enhancedDiscountDao async {
    final db = await database;
    _enhancedDiscountDao ??= EnhancedDiscountDao(db);
    return _enhancedDiscountDao!;
  }

  Future<CouponUsageDao> get couponUsageDao async {
    final db = await database;
    _couponUsageDao ??= CouponUsageDao(db);
    return _couponUsageDao!;
  }

  Future<DiscountLogDao> get discountLogDao async {
    final db = await database;
    _discountLogDao ??= DiscountLogDao(db);
    return _discountLogDao!;
  }

  GSTConfigurationDao? _gstConfigurationDao;
  Future<GSTConfigurationDao> get gstConfigurationDao async {
    final db = await database;
    _gstConfigurationDao ??= GSTConfigurationDao(db);
    return _gstConfigurationDao!;
  }
}
