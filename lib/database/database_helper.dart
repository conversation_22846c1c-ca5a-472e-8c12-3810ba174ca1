// ignore_for_file: depend_on_referenced_packages

import 'dart:developer';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'migrations/migration_v1.dart';
import 'migrations/migration_v2.dart';
import 'migrations/migration_v3_gst_enhancement.dart';
import 'migrations/migration_simple_gst.dart';
import 'migrations/migration_hotfix.dart';
import 'database_migration_helper.dart';
import 'schema_fix.dart';
import 'dart:io' show Platform;

class DataBaseHelper {
  static final DataBaseHelper _instance = DataBaseHelper._internal();
  factory DataBaseHelper() => _instance;
  DataBaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await initDB();
    return _database!;
  }

  Future<Database> initDB() async {
    late String path;

    if (Platform.isAndroid || Platform.isIOS) {
      final dbPath = await getDatabasesPath(); // Android/iOS path
      path = join(dbPath, 'coffee_pos.db');
    } else if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      // FFI setup only for desktop
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;

      final dir = await getApplicationDocumentsDirectory(); // Desktop path
      path = join(dir.path, 'coffee_pos.db');
    } else {
      throw UnsupportedError('This platform is not supported');
    }

    log("Database Path: $path");

    _database = await openDatabase(
      path,
      version: 5,
      onCreate: (db, version) async {
        log('Creating database tables...');

        // Create tables safely using migration helper
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'Products', createProductTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'Settings', createSettingsTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'Sales', createSalesTable);
        await DatabaseMigrationHelper.createTableIfNotExists(
            db, 'SalesTransaction', createSalesTransactionTable);
        await DatabaseMigrationHelper.createTableIfNotExists(
            db, 'ProductCategory', createProductCategoryTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'RawMaterial', createRawMetrialTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'Inventory', createInventoryTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'Discount', createDiscountTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'WorkSpaceSettings', createworkSpaceTable);
        await DatabaseMigrationHelper.createTableIfNotExists(db, 'DeviceSettings', createDeviceSettingsTable);

        // Run migration v2 scripts for new installations
        final migrationV2Scripts = getMigrationV2Scripts();
        for (String script in migrationV2Scripts) {
          await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Migration V2 script');
        }

        // Run migration v3 scripts for GST enhancement
        final migrationV3Scripts = getMigrationV3Scripts();
        for (String script in migrationV3Scripts) {
          await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Migration V3 GST script');
        }

        // Run simple GST migration scripts
        final simpleGSTScripts = getSimpleGSTMigrationScripts();
        for (String script in simpleGSTScripts) {
          await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Simple GST script');
        }

        // Run hotfix migration scripts to ensure all columns exist
        final hotfixScripts = getSafeMigrationScripts();
        for (String script in hotfixScripts) {
          await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Hotfix creation script');
        }

        log('Database creation completed');
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        log('Upgrading database from version $oldVersion to $newVersion');

        // Fix duplicate table issue first
        await DatabaseMigrationHelper.fixDuplicateTableIssue(db);

        if (oldVersion < 2) {
          // Run migration v2 scripts for existing databases
          final migrationV2Scripts = getMigrationV2Scripts();
          for (String script in migrationV2Scripts) {
            await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Migration V2 upgrade script');
          }
        }

        if (oldVersion < 3) {
          // Run migration v3 scripts for GST enhancement
          final migrationV3Scripts = getMigrationV3Scripts();
          for (String script in migrationV3Scripts) {
            await DatabaseMigrationHelper.executeMigrationScript(
                db, script, 'Migration V3 GST upgrade script');
          }
        }

        if (oldVersion < 4) {
          // Run simple GST migration scripts
          final simpleGSTScripts = getSimpleGSTMigrationScripts();
          for (String script in simpleGSTScripts) {
            await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Simple GST upgrade script');
          }
        }

        if (oldVersion < 5) {
          // Run hotfix migration scripts to add missing columns
          final hotfixScripts = getSafeMigrationScripts();
          for (String script in hotfixScripts) {
            await DatabaseMigrationHelper.executeMigrationScript(db, script, 'Hotfix migration script');
          }
        }

        log('Database upgrade completed');

        // Run schema fixes to ensure all required columns exist
        await SchemaFix.runAllSchemaFixes(db);
      },
    );

    return _database!;
  }
}
