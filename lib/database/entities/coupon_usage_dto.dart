class CouponUsageDto {
  int? id;
  String discountID;
  String couponCode;
  String usedBy;
  String usedDate;
  String? salesId;
  double? orderAmount;
  double? discountAmount;
  String workSpaceId;

  CouponUsageDto({
    this.id,
    required this.discountID,
    required this.couponCode,
    required this.usedBy,
    required this.usedDate,
    this.salesId,
    this.orderAmount,
    this.discountAmount,
    required this.workSpaceId,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'discountID': discountID,
      'couponCode': couponCode,
      'usedBy': usedBy,
      'usedDate': usedDate,
      'salesId': salesId,
      'orderAmount': orderAmount,
      'discountAmount': discountAmount,
      'workSpaceId': workSpaceId,
    };
  }

  factory CouponUsageDto.fromMap(Map<String, dynamic> map) {
    return CouponUsageDto(
      id: map['id'],
      discountID: map['discountID'],
      couponCode: map['couponCode'],
      usedBy: map['usedBy'],
      usedDate: map['usedDate'],
      salesId: map['salesId'],
      orderAmount: map['orderAmount']?.toDouble(),
      discountAmount: map['discountAmount']?.toDouble(),
      workSpaceId: map['workSpaceId'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'discountID': discountID,
        'couponCode': couponCode,
        'usedBy': usedBy,
        'usedDate': usedDate,
        'salesId': salesId,
        'orderAmount': orderAmount,
        'discountAmount': discountAmount,
        'workSpaceId': workSpaceId,
      };

  factory CouponUsageDto.fromJson(Map<String, dynamic> json) {
    return CouponUsageDto(
      id: json['id'],
      discountID: json['discountID'],
      couponCode: json['couponCode'],
      usedBy: json['usedBy'],
      usedDate: json['usedDate'],
      salesId: json['salesId'],
      orderAmount: json['orderAmount']?.toDouble(),
      discountAmount: json['discountAmount']?.toDouble(),
      workSpaceId: json['workSpaceId'],
    );
  }
}
