class GSTConfigurationDto {
  int? id;
  String workSpaceId;
  String businessName;
  String? businessGSTIN;
  String businessStateCode;
  String? businessAddress;
  double defaultGSTRate;
  bool enableIGST;
  bool enableCess;
  bool enableReverseCharge;
  bool taxInclusivePricing;
  String roundingMethod;
  String? createdDate;
  String? updatedDate;
  bool isActive;
  int syncStatus;

  GSTConfigurationDto({
    this.id,
    required this.workSpaceId,
    required this.businessName,
    this.businessGSTIN,
    required this.businessStateCode,
    this.businessAddress,
    required this.defaultGSTRate,
    required this.enableIGST,
    required this.enableCess,
    required this.enableReverseCharge,
    required this.taxInclusivePricing,
    required this.roundingMethod,
    this.createdDate,
    this.updatedDate,
    required this.isActive,
    required this.syncStatus,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workSpaceId': workSpaceId,
      'businessName': businessName,
      'businessGSTIN': businessGSTIN,
      'businessStateCode': businessStateCode,
      'businessAddress': businessAddress,
      'defaultGSTRate': defaultGSTRate,
      'enableIGST': enableIGST ? 1 : 0,
      'enableCess': enableCess ? 1 : 0,
      'enableReverseCharge': enableReverseCharge ? 1 : 0,
      'taxInclusivePricing': taxInclusivePricing ? 1 : 0,
      'roundingMethod': roundingMethod,
      'createdDate': createdDate,
      'updatedDate': updatedDate,
      'isActive': isActive ? 1 : 0,
      'syncStatus': syncStatus,
    };
  }

  factory GSTConfigurationDto.fromMap(Map<String, dynamic> map) {
    return GSTConfigurationDto(
      id: map['id'],
      workSpaceId: map['workSpaceId'],
      businessName: map['businessName'],
      businessGSTIN: map['businessGSTIN'],
      businessStateCode: map['businessStateCode'],
      businessAddress: map['businessAddress'],
      defaultGSTRate: map['defaultGSTRate']?.toDouble() ?? 0.0,
      enableIGST: map['enableIGST'] == 1,
      enableCess: map['enableCess'] == 1,
      enableReverseCharge: map['enableReverseCharge'] == 1,
      taxInclusivePricing: map['taxInclusivePricing'] == 1,
      roundingMethod: map['roundingMethod'] ?? 'ROUND_OFF',
      createdDate: map['createdDate'],
      updatedDate: map['updatedDate'],
      isActive: map['isActive'] == 1,
      syncStatus: map['syncStatus'] ?? 0,
    );
  }

  GSTConfigurationDto copyWith({
    int? id,
    String? workSpaceId,
    String? businessName,
    String? businessGSTIN,
    String? businessStateCode,
    String? businessAddress,
    double? defaultGSTRate,
    bool? enableIGST,
    bool? enableCess,
    bool? enableReverseCharge,
    bool? taxInclusivePricing,
    String? roundingMethod,
    String? createdDate,
    String? updatedDate,
    bool? isActive,
    int? syncStatus,
  }) {
    return GSTConfigurationDto(
      id: id ?? this.id,
      workSpaceId: workSpaceId ?? this.workSpaceId,
      businessName: businessName ?? this.businessName,
      businessGSTIN: businessGSTIN ?? this.businessGSTIN,
      businessStateCode: businessStateCode ?? this.businessStateCode,
      businessAddress: businessAddress ?? this.businessAddress,
      defaultGSTRate: defaultGSTRate ?? this.defaultGSTRate,
      enableIGST: enableIGST ?? this.enableIGST,
      enableCess: enableCess ?? this.enableCess,
      enableReverseCharge: enableReverseCharge ?? this.enableReverseCharge,
      taxInclusivePricing: taxInclusivePricing ?? this.taxInclusivePricing,
      roundingMethod: roundingMethod ?? this.roundingMethod,
      createdDate: createdDate ?? this.createdDate,
      updatedDate: updatedDate ?? this.updatedDate,
      isActive: isActive ?? this.isActive,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  String toString() {
    return 'GSTConfigurationDto(id: $id, workSpaceId: $workSpaceId, '
        'businessName: $businessName, businessGSTIN: $businessGSTIN, '
        'businessStateCode: $businessStateCode, defaultGSTRate: $defaultGSTRate, '
        'enableIGST: $enableIGST, enableCess: $enableCess, '
        'enableReverseCharge: $enableReverseCharge, '
        'taxInclusivePricing: $taxInclusivePricing, '
        'roundingMethod: $roundingMethod, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GSTConfigurationDto &&
        other.id == id &&
        other.workSpaceId == workSpaceId &&
        other.businessName == businessName &&
        other.businessGSTIN == businessGSTIN &&
        other.businessStateCode == businessStateCode &&
        other.businessAddress == businessAddress &&
        other.defaultGSTRate == defaultGSTRate &&
        other.enableIGST == enableIGST &&
        other.enableCess == enableCess &&
        other.enableReverseCharge == enableReverseCharge &&
        other.taxInclusivePricing == taxInclusivePricing &&
        other.roundingMethod == roundingMethod &&
        other.isActive == isActive &&
        other.syncStatus == syncStatus;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      workSpaceId,
      businessName,
      businessGSTIN,
      businessStateCode,
      businessAddress,
      defaultGSTRate,
      enableIGST,
      enableCess,
      enableReverseCharge,
      taxInclusivePricing,
      roundingMethod,
      isActive,
      syncStatus,
    );
  }
}
