import '../table_columns.dart' as dbFile;

class SalesDto {
  int? id;
  String salesId;
  String? salesTransactionId;
  String createdDate;
  String? updatedDate;
  double totalAmount;
  double? subtotalAmount;
  double? discountAmount;
  double? parcelAmount;
  double? cgstAmount;
  double? sgstAmount;
  double? igstAmount;
  double? cessAmount;
  double? totalTaxAmount;
  bool? isReverseCharge;
  String? stateCode;
  String? taxConfiguration;
  int status;
  int rowStatus;
  String? customerId;

  SalesDto({
    this.id,
    required this.salesId,
    this.salesTransactionId,
    required this.createdDate,
    this.updatedDate,
    required this.totalAmount,
    this.subtotalAmount,
    this.discountAmount,
    this.parcelAmount,
    this.cgstAmount,
    this.sgstAmount,
    this.igstAmount,
    this.cessAmount,
    this.totalTaxAmount,
    this.isReverseCharge,
    this.stateCode,
    this.taxConfiguration,
    required this.status,
    required this.rowStatus,
    this.customerId,
  });

  Map<String, dynamic> toMap() {
    return {
      dbFile.id: id,
      dbFile.salesId: salesId,
      dbFile.salesTransactionId: salesTransactionId,
      dbFile.createdDate: createdDate,
      dbFile.updatedDate: updatedDate,
      dbFile.totalAmount: totalAmount,
      'subtotalAmount': subtotalAmount,
      dbFile.discountAmount: discountAmount,
      dbFile.parcelAmount: parcelAmount,
      'cgstAmount': cgstAmount,
      'sgstAmount': sgstAmount,
      'igstAmount': igstAmount,
      'cessAmount': cessAmount,
      'totalTaxAmount': totalTaxAmount,
      'isReverseCharge': isReverseCharge == true ? 1 : 0,
      'stateCode': stateCode,
      'taxConfiguration': taxConfiguration,
      dbFile.status: status,
      dbFile.rowStatus: rowStatus,
      dbFile.customerId: customerId,
    };
  }

  factory SalesDto.fromMap(Map<String, dynamic> map) {
    return SalesDto(
      id: map[dbFile.id],
      salesId: map[dbFile.salesId],
      salesTransactionId: map[dbFile.salesTransactionId],
      createdDate: map[dbFile.createdDate],
      updatedDate: map[dbFile.updatedDate],
      totalAmount: map[dbFile.totalAmount]?.toDouble() ?? 0.0,
      subtotalAmount: map['subtotalAmount']?.toDouble(),
      discountAmount: map[dbFile.discountAmount]?.toDouble(),
      parcelAmount: map[dbFile.parcelAmount]?.toDouble(),
      cgstAmount: map['cgstAmount']?.toDouble(),
      sgstAmount: map['sgstAmount']?.toDouble(),
      igstAmount: map['igstAmount']?.toDouble(),
      cessAmount: map['cessAmount']?.toDouble(),
      totalTaxAmount: map['totalTaxAmount']?.toDouble(),
      isReverseCharge: map['isReverseCharge'] == 1,
      stateCode: map['stateCode'],
      taxConfiguration: map['taxConfiguration'],
      status: map[dbFile.status],
      rowStatus: map[dbFile.rowStatus],
      customerId: map[dbFile.customerId],
    );
  }
}
