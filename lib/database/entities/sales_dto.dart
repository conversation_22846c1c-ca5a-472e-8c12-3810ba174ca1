import '../table_columns.dart' as db_file;

class SalesDto {
  int? id;
  String salesId;
  String? salesTransactionId;
  String createdDate;
  String? updatedDate;
  double totalAmount;
  double? subtotalAmount;
  double? discountAmount;
  double? parcelAmount;
  double? cgstAmount;
  double? sgstAmount;
  double? igstAmount;
  double? cessAmount;
  double? totalTaxAmount;
  bool? isReverseCharge;
  String? stateCode;
  String? taxConfiguration;
  int status;
  int rowStatus;
  String? customerId;

  SalesDto({
    this.id,
    required this.salesId,
    this.salesTransactionId,
    required this.createdDate,
    this.updatedDate,
    required this.totalAmount,
    this.subtotalAmount,
    this.discountAmount,
    this.parcelAmount,
    this.cgstAmount,
    this.sgstAmount,
    this.igstAmount,
    this.cessAmount,
    this.totalTaxAmount,
    this.isReverseCharge,
    this.stateCode,
    this.taxConfiguration,
    required this.status,
    required this.rowStatus,
    this.customerId,
  });

  Map<String, dynamic> toMap() {
    return {
      db_file.id: id,
      db_file.salesId: salesId,
      db_file.salesTransactionId: salesTransactionId,
      db_file.createdDate: createdDate,
      db_file.updatedDate: updatedDate,
      db_file.totalAmount: totalAmount,
      'subtotalAmount': subtotalAmount,
      db_file.discountAmount: discountAmount,
      db_file.parcelAmount: parcelAmount,
      'cgstAmount': cgstAmount,
      'sgstAmount': sgstAmount,
      'igstAmount': igstAmount,
      'cessAmount': cessAmount,
      'totalTaxAmount': totalTaxAmount,
      'isReverseCharge': isReverseCharge == true ? 1 : 0,
      'stateCode': stateCode,
      'taxConfiguration': taxConfiguration,
      db_file.status: status,
      db_file.rowStatus: rowStatus,
      db_file.customerId: customerId,
    };
  }

  factory SalesDto.fromMap(Map<String, dynamic> map) {
    return SalesDto(
      id: map[db_file.id],
      salesId: map[db_file.salesId],
      salesTransactionId: map[db_file.salesTransactionId],
      createdDate: map[db_file.createdDate],
      updatedDate: map[db_file.updatedDate],
      totalAmount: map[db_file.totalAmount]?.toDouble() ?? 0.0,
      subtotalAmount: map['subtotalAmount']?.toDouble(),
      discountAmount: map[db_file.discountAmount]?.toDouble(),
      parcelAmount: map[db_file.parcelAmount]?.toDouble(),
      cgstAmount: map['cgstAmount']?.toDouble(),
      sgstAmount: map['sgstAmount']?.toDouble(),
      igstAmount: map['igstAmount']?.toDouble(),
      cessAmount: map['cessAmount']?.toDouble(),
      totalTaxAmount: map['totalTaxAmount']?.toDouble(),
      isReverseCharge: map['isReverseCharge'] == 1,
      stateCode: map['stateCode'],
      taxConfiguration: map['taxConfiguration'],
      status: map[db_file.status],
      rowStatus: map[db_file.rowStatus],
      customerId: map[db_file.customerId],
    );
  }
}
