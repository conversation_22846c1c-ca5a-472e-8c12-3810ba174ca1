import '.././table_columns.dart' as db;

class RecipeDto {
  final String productId;
  final String rawMaterialId;
  final double quantity;

  RecipeDto({
    required this.productId,
    required this.rawMaterialId,
    required this.quantity,
  });

  Map<String, dynamic> toMap() {
    return {
      db.productId: productId,
      db.recipeRawMaterialId: rawMaterialId,
      db.rawMaterialQuantity: quantity,
    };
  }

  factory RecipeDto.fromMap(Map<String, dynamic> map) {
    return RecipeDto(
      productId: map[db.productId],
      rawMaterialId: map[db.recipeRawMaterialId],
      quantity: map[db.rawMaterialQuantity],
    );
  }
}
