class DiscountLogDto {
  int? id;
  String discountID;
  String salesId;
  String? productId;
  String appliedBy;
  String appliedDate;
  double originalAmount;
  double discountAmount;
  double finalAmount;
  String discountType;
  String workSpaceId;
  int syncStatus;

  DiscountLogDto({
    this.id,
    required this.discountID,
    required this.salesId,
    this.productId,
    required this.appliedBy,
    required this.appliedDate,
    required this.originalAmount,
    required this.discountAmount,
    required this.finalAmount,
    required this.discountType,
    required this.workSpaceId,
    this.syncStatus = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'discountID': discountID,
      'salesId': salesId,
      'productId': productId,
      'appliedBy': appliedBy,
      'appliedDate': appliedDate,
      'originalAmount': originalAmount,
      'discountAmount': discountAmount,
      'finalAmount': finalAmount,
      'discountType': discountType,
      'workSpaceId': workSpaceId,
      'syncStatus': syncStatus,
    };
  }

  factory DiscountLogDto.fromMap(Map<String, dynamic> map) {
    return DiscountLogDto(
      id: map['id'],
      discountID: map['discountID'],
      salesId: map['salesId'],
      productId: map['productId'],
      appliedBy: map['appliedBy'],
      appliedDate: map['appliedDate'],
      originalAmount: map['originalAmount']?.toDouble() ?? 0.0,
      discountAmount: map['discountAmount']?.toDouble() ?? 0.0,
      finalAmount: map['finalAmount']?.toDouble() ?? 0.0,
      discountType: map['discountType'],
      workSpaceId: map['workSpaceId'],
      syncStatus: map['syncStatus'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'discountID': discountID,
        'salesId': salesId,
        'productId': productId,
        'appliedBy': appliedBy,
        'appliedDate': appliedDate,
        'originalAmount': originalAmount,
        'discountAmount': discountAmount,
        'finalAmount': finalAmount,
        'discountType': discountType,
        'workSpaceId': workSpaceId,
        'syncStatus': syncStatus,
      };

  factory DiscountLogDto.fromJson(Map<String, dynamic> json) {
    return DiscountLogDto(
      id: json['id'],
      discountID: json['discountID'],
      salesId: json['salesId'],
      productId: json['productId'],
      appliedBy: json['appliedBy'],
      appliedDate: json['appliedDate'],
      originalAmount: json['originalAmount']?.toDouble() ?? 0.0,
      discountAmount: json['discountAmount']?.toDouble() ?? 0.0,
      finalAmount: json['finalAmount']?.toDouble() ?? 0.0,
      discountType: json['discountType'],
      workSpaceId: json['workSpaceId'],
      syncStatus: json['syncStatus'] ?? 0,
    );
  }
}
