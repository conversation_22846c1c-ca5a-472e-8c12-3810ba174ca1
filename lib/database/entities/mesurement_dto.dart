class MeasurementModelDto {
  final int? id;
  final String measurementId;
  final String? productId;
  final String measurementName;
  final String? measurementPrice;
  final String? createdDate;
  final String? updatedDate;

  MeasurementModelDto({
    this.id,
    required this.measurementId,
    this.productId,
    required this.measurementName,
    this.measurementPrice,
    this.createdDate,
    this.updatedDate,
  });

  // Convert a MeasurementModel object into a Map (for SQLite)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'measurementId': measurementId,
      'productId': productId,
      'measurementName': measurementName,
      'measurementPrice': measurementPrice,
      'createdDate': createdDate,
      'updatedDate': updatedDate,
    };
  }

  // Convert a Map into a MeasurementModel object (from SQLite)
  factory MeasurementModelDto.fromMap(Map<String, dynamic> map) {
    return MeasurementModelDto(
      id: map['id'],
      measurementId: map['measurementId'],
      productId: map['productId'],
      measurementName: map['measurementName'],
      measurementPrice: map['measurementPrice'],
      createdDate: map['createdDate'],
      updatedDate: map['updatedDate'],
    );
  }
}
