import '../table_columns.dart';

class SettingsDto {
  final int id;
  final String shopUId;
  final String keyName;
  final String value;

  SettingsDto({
    required this.id,
    required this.shopUId,
    required this.keyName,
    required this.value,
  });

  factory SettingsDto.fromMap(Map<String, dynamic> map) {
    return SettingsDto(
      id: map['id'],
      shopUId: map[dbShopUId],
      keyName: map['keyName'],
      value: map['value'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'keyName': keyName,
      dbShopUId: shopUId,
      'value': value,
    };
  }
}
