// ignore: library_prefixes
import '../table_columns.dart' as dbCols;

class WorkspaceSettingsDto {
  int? id;
  String? workSpaceId;
  String? wSName;
  String? wSUserName;
  String? wSUserId;
  String? wSSettingKey;
  String? wSSettingValue;
  int? workSsettingSync;

  WorkspaceSettingsDto({
    this.id,
    this.workSpaceId,
    this.wSName,
    this.wSUserName,
    this.wSUserId,
    this.wSSettingKey,
    this.wSSettingValue,
    this.workSsettingSync,
  });

  Map<String, dynamic> toMap() {
    return {
      dbCols.wSSettingId: id,
      dbCols.workSpaceId: workSpaceId,
      dbCols.wSName: wSName,
      dbCols.wSUserName: wSUserName,
      dbCols.wSUserId: wSUserId,
      dbCols.wSSettingKey: wSSettingKey,
      dbCols.wSSettingValue: wSSettingValue,
      dbCols.workSsettingSync: workSsettingSync,
    };
  }

  factory WorkspaceSettingsDto.fromMap(Map<String, dynamic> map) {
    return WorkspaceSettingsDto(
      id: map[dbCols.wSSettingId],
      workSpaceId: map[dbCols.workSpaceId],
      wSName: map[dbCols.wSName],
      wSUserName: map[dbCols.wSUserName],
      wSUserId: map[dbCols.wSUserId],
      wSSettingKey: map[dbCols.wSSettingKey],
      wSSettingValue: map[dbCols.wSSettingValue],
      workSsettingSync: map[dbCols.workSsettingSync],
    );
  }

  ///
  WorkspaceSettingsDto copyWith({
    int? id,
    String? workSpaceId,
    String? wSName,
    String? wSUserName,
    String? wSUserId,
    String? wSSettingKey,
    String? wSSettingValue,
    int? workSsettingSync,
  }) {
    return WorkspaceSettingsDto(
      id: id ?? this.id,
      workSpaceId: workSpaceId ?? this.workSpaceId,
      wSName: wSName ?? this.wSName,
      wSUserName: wSUserName ?? this.wSUserName,
      wSUserId: wSUserId ?? this.wSUserId,
      wSSettingKey: wSSettingKey ?? this.wSSettingKey,
      wSSettingValue: wSSettingValue ?? this.wSSettingValue,
      workSsettingSync: workSsettingSync ?? this.workSsettingSync,
    );
  }
}
