import '../table_columns.dart';
import '../table_columns.dart' as db;

class ProductDto {
  int? id;
  String? shopId;
  String? productId;
  String? productName;
  String? productUniqId;
  String? createdDate;
  String? updatedDate;
  int? status;
  int? rowStatus;
  String? price;
  String? categoryId;
  String? toppingsId;
  String? mrp;
  String? mesurementId;
  String? productImagePath;
  String? parcelAmount;
  String? costOfProduction;
  String? cgst;
  String? sgst;
  String? productDiscountAmount;
  String? recipeRawMaterialId;
  ProductDto(
      {this.id,
      this.cgst,
      this.sgst,
      this.shopId,
      this.productUniqId,
      this.productId,
      this.productName,
      this.createdDate,
      this.updatedDate,
      this.status,
      this.rowStatus,
      this.price,
      this.categoryId,
      this.toppingsId,
      this.productImagePath,
      this.mesurementId,
      this.parcelAmount,
      this.mrp,
      this.costOfProduction,
      this.recipeRawMaterialId,
      this.productDiscountAmount});

  factory ProductDto.fromMap(Map<String, dynamic> map) {
    return ProductDto(
        id: map['id'],
        productId: map['productId'],
        productUniqId: map[db.productUniqId],
        cgst: map[db.cgst],
        sgst: map[db.sgst],
        costOfProduction: map[db.productionCost],
        productDiscountAmount: map[db.discountAmount],
        productName: map['productName'],
        createdDate: map['createdDate'],
        updatedDate: map['updatedDate'],
        status: map['status'],
        rowStatus: map['rowStatus'],
        price: map['price'],
        categoryId: map['categoryId'],
        toppingsId: map['toppingsId'],
        recipeRawMaterialId: map[db.recipeRawMaterialId],
        productImagePath: map[productImage]);
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      db.productUniqId: productUniqId,
      dbShopUId: shopId,
      db.cgst: cgst,
      db.sgst: sgst,
      db.discountAmount: productDiscountAmount,
      'productName': productName,
      'createdDate': createdDate,
      'updatedDate': updatedDate,
      'status': status,
      'rowStatus': rowStatus,
      'price': price,
      'categoryId': categoryId,
      'toppingsId': toppingsId,
      productImage: productImagePath,
      db.productionCost: costOfProduction,
      db.recipeRawMaterialId: recipeRawMaterialId
    };
  }
}
