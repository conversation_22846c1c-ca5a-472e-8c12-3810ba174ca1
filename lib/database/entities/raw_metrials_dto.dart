import '../table_columns.dart' as db;

class RawMaterialDto {
  String? inventoryId;
  String? rawMaterialId;
  String? name;
  String? unit;
  double? unitPrice;
  int? status;
  int? rowStates;
  double? quantity;
  String? productId;

  RawMaterialDto(
      {this.inventoryId,
      this.rawMaterialId,
      this.name,
      this.unit,
      this.unitPrice,
      this.status,
      this.rowStates,
      this.quantity,
      this.productId});

  Map<String, dynamic> toMap() {
    return {
      db.inventoryId: inventoryId,
      db.rawMaterialId: rawMaterialId,
      'name': name,
      'unit': unit,
      db.rawMaterialUnitPrice: unitPrice,
      'status': status,
      db.rowStatus: rowStates,
      'quantity': quantity,
      db.productId: productId
    };
  }

  factory RawMaterialDto.fromMap(Map<String, dynamic> map) {
    return RawMaterialDto(
      inventoryId: map[db.inventoryId],
      rawMaterialId: map[db.rawMaterialId],
      name: map['name'],
      unit: map['unit'],
      unitPrice: map[db.rawMaterialUnitPrice],
      status: map['status'],
      rowStates: map[db.rowStatus],
      quantity: map['quantity'] != null ? double.tryParse(map['quantity'].toString()) : null,
      productId: map[db.productId],
    );
  }
}
