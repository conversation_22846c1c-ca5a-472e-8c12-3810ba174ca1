import '.././table_columns.dart' as db;

class ProductCategoryDto {
  final int? id;
  final String shopId;
  final String? categoryId;
  final String? productCategoryName;
  final String? createdDate;
  final String? updatedDate;
  final int? status;
  final int? rowStatus;

  ProductCategoryDto({
    this.id,
    required this.shopId,
    this.categoryId,
    this.productCategoryName,
    this.createdDate,
    this.updatedDate,
    this.status,
    this.rowStatus,
  });

  factory ProductCategoryDto.fromJson(Map<String, dynamic> json) {
    return ProductCategoryDto(
      id: json[db.id],
      shopId: json[db.dbShopUId],
      categoryId: json[db.categoryId],
      productCategoryName: json[db.productCategoryName],
      createdDate: json[db.createdDate],
      updatedDate: json[db.updatedDate],
      status: json[db.status],
      rowStatus: json[db.rowStatus],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      db.id: id,
      db.dbShopUId: shopId,
      db.categoryId: categoryId,
      db.productCategoryName: productCategoryName,
      db.createdDate: createdDate,
      db.updatedDate: updatedDate,
      db.status: status,
      db.rowStatus: rowStatus,
    };
  }
}
