import '.././table_columns.dart' as db;

class InventoryDto {
  final int? id;
  String shopId;
  String? rawMId;
  String? inventoryId;
  String? inventoyProductName;
  String? inventoryQunatity;
  String? inventoryUnit;
  String? createdDate;
  String? updatedDate;
  int? status;
  int? rowStatus;

  InventoryDto({
    this.id,
    required this.shopId,
    this.inventoryId,
    this.inventoyProductName,
    this.inventoryQunatity,
    this.rawMId,
    this.inventoryUnit,
    this.createdDate,
    this.updatedDate,
    this.status,
    this.rowStatus,
  });

  factory InventoryDto.fromJson(Map<String, dynamic> json) {
    return InventoryDto(
      id: json[db.id],
      shopId: json[db.dbShopUId],
      inventoryId: json[db.inventoryId],
      inventoyProductName: json[db.inventoyProductName],
      inventoryQunatity: json[db.inventoryQunatity],
      inventoryUnit: json[db.inventoryUnit],
      createdDate: json[db.createdDate],
      updatedDate: json[db.updatedDate],
      status: json[db.status],
      rowStatus: json[db.rowStatus],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      db.id: id,
      db.dbShopUId: shopId,
      db.inventoryId: inventoryId,
      db.inventoyProductName: inventoyProductName,
      db.inventoryQunatity: inventoryQunatity,
      db.inventoryUnit: inventoryUnit,
      db.createdDate: createdDate,
      db.updatedDate: updatedDate,
      db.status: status,
      db.rowStatus: rowStatus,
    };
  }
}
