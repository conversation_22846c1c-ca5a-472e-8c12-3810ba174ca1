import '../table_columns.dart' as db_file;

class SalesTransactionDto {
  int? id;
  String? salesId;
  String? productName;
  String? salesTransactionId;
  String? productId;
  double? productAmount;
  double? baseAmount; // Amount before tax
  double? cgst;
  double? sgst;
  double? igst;
  double? cess;
  double? gstRate; // Applied GST rate
  double? cessRate; // Applied Cess rate
  String? hsnCode; // HSN/SAC code
  double? parcelAmount;
  double? discountAmount;
  double? taxableAmount; // Amount after discount, before tax
  double? totalTaxAmount; // Total tax for this item
  bool? customTaxApplied; // Whether custom tax rate was used
  int? productQty;
  int? status;
  int? rowStatus;
  String? createdAt;

  SalesTransactionDto({
    this.id,
    this.salesId,
    this.salesTransactionId,
    this.productId,
    this.productName,
    this.productAmount,
    this.baseAmount,
    this.cgst,
    this.sgst,
    this.igst,
    this.cess,
    this.gstRate,
    this.cessRate,
    this.hsnCode,
    this.parcelAmount,
    this.discountAmount,
    this.taxableAmount,
    this.totalTaxAmount,
    this.customTaxApplied,
    this.productQty,
    this.status,
    this.rowStatus,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      db_file.id: id,
      db_file.salesId: salesId,
      db_file.salesTransactionId: salesTransactionId,
      db_file.productId: productId,
      'productName': productName,
      db_file.productAmount: productAmount,
      'baseAmount': baseAmount,
      db_file.cgst: cgst,
      db_file.sgst: sgst,
      'igst': igst,
      'cess': cess,
      'gstRate': gstRate,
      'cessRate': cessRate,
      'hsnCode': hsnCode,
      db_file.parcelAmount: parcelAmount,
      db_file.discountAmount: discountAmount,
      'taxableAmount': taxableAmount,
      'totalTaxAmount': totalTaxAmount,
      'customTaxApplied': customTaxApplied == true ? 1 : 0,
      db_file.productQty: productQty,
      db_file.status: status,
      db_file.rowStatus: rowStatus,
      db_file.createdAt: createdAt,
    };
  }

  factory SalesTransactionDto.fromMap(Map<String, dynamic> map) {
    return SalesTransactionDto(
      id: map[db_file.id],
      salesId: map[db_file.salesId],
      salesTransactionId: map[db_file.salesTransactionId],
      productId: map[db_file.productId],
      productName: map['productName'],
      productAmount: _parseDouble(map[db_file.productAmount]),
      baseAmount: _parseDouble(map['baseAmount']),
      cgst: _parseDouble(map[db_file.cgst]),
      sgst: _parseDouble(map[db_file.sgst]),
      igst: _parseDouble(map['igst']),
      cess: _parseDouble(map['cess']),
      gstRate: _parseDouble(map['gstRate']),
      cessRate: _parseDouble(map['cessRate']),
      hsnCode: map['hsnCode'],
      parcelAmount: _parseDouble(map[db_file.parcelAmount]),
      discountAmount: _parseDouble(map[db_file.discountAmount]),
      taxableAmount: _parseDouble(map['taxableAmount']),
      totalTaxAmount: _parseDouble(map['totalTaxAmount']),
      customTaxApplied: map['customTaxApplied'] == 1,
      productQty: map[db_file.productQty],
      status: map[db_file.status],
      rowStatus: map[db_file.rowStatus],
      createdAt: map[db_file.createdAt],
    );
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }
}
