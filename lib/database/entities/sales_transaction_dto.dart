import '../table_columns.dart' as dbFile;

class SalesTransactionDto {
  int? id;
  String? salesId;
  String? productName;
  String? salesTransactionId;
  String? productId;
  double? productAmount;
  double? baseAmount; // Amount before tax
  double? cgst;
  double? sgst;
  double? igst;
  double? cess;
  double? gstRate; // Applied GST rate
  double? cessRate; // Applied Cess rate
  String? hsnCode; // HSN/SAC code
  double? parcelAmount;
  double? discountAmount;
  double? taxableAmount; // Amount after discount, before tax
  double? totalTaxAmount; // Total tax for this item
  bool? customTaxApplied; // Whether custom tax rate was used
  int? productQty;
  int? status;
  int? rowStatus;
  String? createdAt;

  SalesTransactionDto({
    this.id,
    this.salesId,
    this.salesTransactionId,
    this.productId,
    this.productName,
    this.productAmount,
    this.baseAmount,
    this.cgst,
    this.sgst,
    this.igst,
    this.cess,
    this.gstRate,
    this.cessRate,
    this.hsnCode,
    this.parcelAmount,
    this.discountAmount,
    this.taxableAmount,
    this.totalTaxAmount,
    this.customTaxApplied,
    this.productQty,
    this.status,
    this.rowStatus,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      dbFile.id: id,
      dbFile.salesId: salesId,
      dbFile.salesTransactionId: salesTransactionId,
      dbFile.productId: productId,
      'productName': productName,
      dbFile.productAmount: productAmount,
      'baseAmount': baseAmount,
      dbFile.cgst: cgst,
      dbFile.sgst: sgst,
      'igst': igst,
      'cess': cess,
      'gstRate': gstRate,
      'cessRate': cessRate,
      'hsnCode': hsnCode,
      dbFile.parcelAmount: parcelAmount,
      dbFile.discountAmount: discountAmount,
      'taxableAmount': taxableAmount,
      'totalTaxAmount': totalTaxAmount,
      'customTaxApplied': customTaxApplied == true ? 1 : 0,
      dbFile.productQty: productQty,
      dbFile.status: status,
      dbFile.rowStatus: rowStatus,
      dbFile.createdAt: createdAt,
    };
  }

  factory SalesTransactionDto.fromMap(Map<String, dynamic> map) {
    return SalesTransactionDto(
      id: map[dbFile.id],
      salesId: map[dbFile.salesId],
      salesTransactionId: map[dbFile.salesTransactionId],
      productId: map[dbFile.productId],
      productName: map['productName'],
      productAmount: _parseDouble(map[dbFile.productAmount]),
      baseAmount: _parseDouble(map['baseAmount']),
      cgst: _parseDouble(map[dbFile.cgst]),
      sgst: _parseDouble(map[dbFile.sgst]),
      igst: _parseDouble(map['igst']),
      cess: _parseDouble(map['cess']),
      gstRate: _parseDouble(map['gstRate']),
      cessRate: _parseDouble(map['cessRate']),
      hsnCode: map['hsnCode'],
      parcelAmount: _parseDouble(map[dbFile.parcelAmount]),
      discountAmount: _parseDouble(map[dbFile.discountAmount]),
      taxableAmount: _parseDouble(map['taxableAmount']),
      totalTaxAmount: _parseDouble(map['totalTaxAmount']),
      customTaxApplied: map['customTaxApplied'] == 1,
      productQty: map[dbFile.productQty],
      status: map[dbFile.status],
      rowStatus: map[dbFile.rowStatus],
      createdAt: map[dbFile.createdAt],
    );
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }
}
