import 'dart:developer';
import '../database/entities/sales_transaction_dto.dart';
import '../database/entities/product.dart';
import '../database/app_database.dart';
import '../features/settings/settings_provider.dart';

class GSTService {
  static final GSTService _instance = GSTService._internal();
  factory GSTService() => _instance;
  GSTService._internal();

  /// Calculate GST breakdown for cart items
  Future<GSTCalculation> calculateGST({
    required List<SalesTransactionDto> cartItems,
    required double discountAmount,
    required SettingsProvider settingsProvider,
  }) async {
    try {
      if (!settingsProvider.isTaxEnabled) {
        return GSTCalculation(
          subtotal: _calculateSubtotal(cartItems),
          discountAmount: discountAmount,
          cgst: 0,
          sgst: 0,
          igst: 0,
          totalGST: 0,
          grandTotal: _calculateSubtotal(cartItems) - discountAmount,
          taxableAmount: 0,
          gstBreakdown: [],
        );
      }

      final subtotal = _calculateSubtotal(cartItems);
      final taxableAmount = subtotal - discountAmount;

      List<GSTBreakdownItem> gstBreakdown = [];
      double totalCGST = 0;
      double totalSGST = 0;
      double totalIGST = 0;

      // Group items by GST rate
      Map<double, List<SalesTransactionDto>> itemsByGSTRate = {};

      for (final item in cartItems) {
        final product = await _getProductDetails(item.productId ?? '');
        final gstRate = _getGSTRate(product, settingsProvider);

        if (!itemsByGSTRate.containsKey(gstRate)) {
          itemsByGSTRate[gstRate] = [];
        }
        itemsByGSTRate[gstRate]!.add(item);
      }

      // Calculate GST for each rate group
      for (final entry in itemsByGSTRate.entries) {
        final gstRate = entry.key;
        final items = entry.value;

        final groupSubtotal = items.fold<double>(0, (sum, item) => sum + (item.productAmount ?? 0));
        final groupDiscountRatio = discountAmount > 0 ? groupSubtotal / subtotal : 0;
        final groupDiscount = discountAmount * groupDiscountRatio;
        final groupTaxableAmount = groupSubtotal - groupDiscount;

        if (gstRate > 0 && groupTaxableAmount > 0) {
          final cgst = (groupTaxableAmount * gstRate) / 200; // Divide by 200 for CGST (half of GST)
          final sgst = (groupTaxableAmount * gstRate) / 200; // Divide by 200 for SGST (half of GST)
          const igst = 0.0; // For now, assuming intra-state transactions

          totalCGST += cgst;
          totalSGST += sgst;
          totalIGST += igst;

          gstBreakdown.add(GSTBreakdownItem(
            gstRate: gstRate,
            taxableAmount: groupTaxableAmount,
            cgst: cgst,
            sgst: sgst,
            igst: igst,
            totalGST: cgst + sgst + igst,
            items: items.map((item) => item.productName ?? '').toList(),
          ));
        }
      }

      final totalGST = totalCGST + totalSGST + totalIGST;
      final grandTotal = taxableAmount + totalGST;

      return GSTCalculation(
        subtotal: subtotal,
        discountAmount: discountAmount,
        cgst: totalCGST,
        sgst: totalSGST,
        igst: totalIGST,
        totalGST: totalGST,
        grandTotal: grandTotal,
        taxableAmount: taxableAmount,
        gstBreakdown: gstBreakdown,
      );
    } catch (e) {
      log('Error calculating GST: $e');
      return GSTCalculation(
        subtotal: _calculateSubtotal(cartItems),
        discountAmount: discountAmount,
        cgst: 0,
        sgst: 0,
        igst: 0,
        totalGST: 0,
        grandTotal: _calculateSubtotal(cartItems) - discountAmount,
        taxableAmount: 0,
        gstBreakdown: [],
      );
    }
  }

  /// Get product details from database
  Future<ProductDto?> _getProductDetails(String productId) async {
    try {
      final productDao = await AppDatabase().productDao;
      final products = await productDao.getAllProducts();
      return products.firstWhere((p) => p.productId == productId, orElse: () => ProductDto());
    } catch (e) {
      log('Error getting product details: $e');
      return null;
    }
  }

  /// Get GST rate for a product
  double _getGSTRate(ProductDto? product, SettingsProvider settingsProvider) {
    if (product == null) return 0;

    // Check settings for tax application
    if (settingsProvider.applyTaxAllProducts) {
      // Use CGST + SGST as total GST rate
      final cgst = double.tryParse(product.cgst ?? '0') ?? 0;
      final sgst = double.tryParse(product.sgst ?? '0') ?? 0;
      return cgst + sgst;
    } else if (settingsProvider.applyTaxSpecificProduct) {
      // Check if this specific product has GST
      final cgst = double.tryParse(product.cgst ?? '0') ?? 0;
      final sgst = double.tryParse(product.sgst ?? '0') ?? 0;
      return cgst + sgst;
    }

    return 0;
  }

  /// Calculate subtotal without tax
  double _calculateSubtotal(List<SalesTransactionDto> cartItems) {
    return cartItems.fold<double>(0, (sum, item) => sum + (item.productAmount ?? 0));
  }

  /// Calculate tax-inclusive price for a product
  double calculateTaxInclusivePrice({
    required double basePrice,
    required double gstRate,
    required bool includeTax,
  }) {
    if (!includeTax || gstRate == 0) {
      return basePrice;
    }

    // If tax is included in the price, extract the base price
    return basePrice / (1 + (gstRate / 100));
  }

  /// Calculate tax-exclusive price for a product
  double calculateTaxExclusivePrice({
    required double basePrice,
    required double gstRate,
    required bool excludeTax,
  }) {
    if (!excludeTax || gstRate == 0) {
      return basePrice;
    }

    // Add tax to the base price
    return basePrice * (1 + (gstRate / 100));
  }

  /// Format GST amount for display
  String formatGSTAmount(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Get GST summary text
  String getGSTSummary(GSTCalculation calculation) {
    if (calculation.totalGST == 0) {
      return 'No GST applicable';
    }

    final breakdown = calculation.gstBreakdown
        .map((item) => '${item.gstRate}%: ${formatGSTAmount(item.totalGST)}')
        .join(', ');

    return 'GST: $breakdown (Total: ${formatGSTAmount(calculation.totalGST)})';
  }
}

/// GST calculation result
class GSTCalculation {
  final double subtotal;
  final double discountAmount;
  final double cgst;
  final double sgst;
  final double igst;
  final double totalGST;
  final double grandTotal;
  final double taxableAmount;
  final List<GSTBreakdownItem> gstBreakdown;

  GSTCalculation({
    required this.subtotal,
    required this.discountAmount,
    required this.cgst,
    required this.sgst,
    required this.igst,
    required this.totalGST,
    required this.grandTotal,
    required this.taxableAmount,
    required this.gstBreakdown,
  });

  @override
  String toString() {
    return 'GSTCalculation(subtotal: $subtotal, discount: $discountAmount, '
        'CGST: $cgst, SGST: $sgst, IGST: $igst, total: $grandTotal)';
  }
}

/// GST breakdown by rate
class GSTBreakdownItem {
  final double gstRate;
  final double taxableAmount;
  final double cgst;
  final double sgst;
  final double igst;
  final double totalGST;
  final List<String> items;

  GSTBreakdownItem({
    required this.gstRate,
    required this.taxableAmount,
    required this.cgst,
    required this.sgst,
    required this.igst,
    required this.totalGST,
    required this.items,
  });

  @override
  String toString() {
    return 'GSTBreakdownItem(rate: $gstRate%, taxable: $taxableAmount, '
        'CGST: $cgst, SGST: $sgst, total: $totalGST)';
  }
}
