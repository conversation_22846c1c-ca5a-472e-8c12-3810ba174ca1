import 'dart:developer';
import '../models/discounts.dart';
import '../database/app_database.dart';
import '../database/dao/enhanced_discount_dao.dart';
import '../database/entities/sales_transaction_dto.dart';
import '../database/entities/product.dart';

class DiscountService {
  static final DiscountService _instance = DiscountService._internal();
  factory DiscountService() => _instance;
  DiscountService._internal();

  EnhancedDiscountDao? _discountDao;

  Future<void> initialize() async {
    _discountDao = await AppDatabase().enhancedDiscountDao;
  }

  /// Calculate applicable discounts for the current cart
  Future<List<DiscountResult>> calculateApplicableDiscounts({
    required List<SalesTransactionDto> cartItems,
    required double totalAmount,
    String? couponCode,
    String? workspaceId,
  }) async {
    try {
      await initialize();
      List<DiscountResult> applicableDiscounts = [];

      // Get all active discounts
      final activeDiscounts = await _discountDao?.getDiscountsWithPagination(
            isActive: true,
            workspaceId: workspaceId,
            limit: 100,
          ) ??
          [];

      for (final discount in activeDiscounts) {
        final result = await _evaluateDiscount(
          discount: discount,
          cartItems: cartItems,
          totalAmount: totalAmount,
          couponCode: couponCode,
        );

        if (result != null) {
          applicableDiscounts.add(result);
        }
      }

      // Sort by discount amount (highest first)
      applicableDiscounts.sort((a, b) => b.discountAmount.compareTo(a.discountAmount));

      return applicableDiscounts;
    } catch (e) {
      log('Error calculating applicable discounts: $e');
      return [];
    }
  }

  /// Evaluate a single discount against the cart
  Future<DiscountResult?> _evaluateDiscount({
    required Discounts discount,
    required List<SalesTransactionDto> cartItems,
    required double totalAmount,
    String? couponCode,
  }) async {
    try {
      // Check if discount is active and within date range
      if (!_isDiscountValid(discount)) {
        return null;
      }

      // Check coupon code if required
      if (discount.mode == 'C' && discount.couponName != couponCode) {
        return null;
      }

      // Check minimum amount requirement
      if (!discount.isValidForAmount(totalAmount)) {
        return null;
      }

      double discountAmount = 0;

      if (discount.type == 'S') {
        // Sales-wise discount - apply to total amount
        discountAmount = discount.calculateDiscountAmount(totalAmount);
      } else if (discount.type == 'P') {
        // Product-wise discount - apply to specific products
        discountAmount = await _calculateProductWiseDiscount(
          discount: discount,
          cartItems: cartItems,
        );
      }

      if (discountAmount > 0) {
        return DiscountResult(
          discountAmount: discountAmount,
          message: _getDiscountMessage(discount, discountAmount),
          appliedDiscount: discount,
        );
      }

      return null;
    } catch (e) {
      log('Error evaluating discount ${discount.discountID}: $e');
      return null;
    }
  }

  /// Calculate product-wise discount
  Future<double> _calculateProductWiseDiscount({
    required Discounts discount,
    required List<SalesTransactionDto> cartItems,
  }) async {
    double totalDiscount = 0;
    final categoryIds = discount.categoryID?.split(',') ?? [];

    for (final item in cartItems) {
      // Check if product belongs to discount categories
      if (await _isProductEligible(item.productId ?? '', categoryIds)) {
        final itemTotal = item.productAmount ?? 0;
        final itemDiscount = _calculateDiscountAmount(
          amount: itemTotal,
          discountValue: discount.discount ?? '0',
          discountType: discount.discountOn ?? 'P',
          maxDiscount: discount.maxDiscount,
        );
        totalDiscount += itemDiscount;
      }
    }

    return totalDiscount;
  }

  /// Check if product is eligible for discount
  Future<bool> _isProductEligible(String productId, List<String> categoryIds) async {
    if (categoryIds.isEmpty) return true;

    try {
      // Get product category from database
      final productDao = await AppDatabase().productDao;
      final products = await productDao.getAllProducts();
      final product = products.firstWhere((p) => p.productId == productId, orElse: () => ProductDto());

      if (product.categoryId != null) {
        return categoryIds.contains(product.categoryId);
      }
    } catch (e) {
      log('Error checking product eligibility: $e');
    }

    return false;
  }

  /// Calculate discount amount based on type
  double _calculateDiscountAmount({
    required double amount,
    required String discountValue,
    required String discountType,
    String? maxDiscount,
  }) {
    final value = double.tryParse(discountValue) ?? 0;
    double discount = 0;

    if (discountType == 'P') {
      // Percentage discount
      discount = (amount * value) / 100;
    } else {
      // Fixed amount discount
      discount = value;
    }

    // Apply maximum discount limit
    if (maxDiscount != null && maxDiscount.isNotEmpty) {
      final maxAmount = double.tryParse(maxDiscount) ?? double.infinity;
      discount = discount > maxAmount ? maxAmount : discount;
    }

    // Ensure discount doesn't exceed the amount
    return discount > amount ? amount : discount;
  }

  /// Check if discount is valid
  bool _isDiscountValid(Discounts discount) {
    return discount.isCurrentlyActive;
  }

  /// Generate discount message
  String _getDiscountMessage(Discounts discount, double discountAmount) {
    final type = discount.type == 'S' ? 'Sales' : 'Product';
    final mode = discount.mode == 'A' ? 'Auto' : 'Coupon';
    return '$type $mode Discount: ₹${discountAmount.toStringAsFixed(2)}';
  }

  /// Apply manual discount
  DiscountResult applyManualDiscount({
    required double totalAmount,
    required double discountValue,
    required bool isPercentage,
    String? reason,
  }) {
    double discountAmount = 0;

    if (isPercentage) {
      discountAmount = (totalAmount * discountValue) / 100;
    } else {
      discountAmount = discountValue;
    }

    // Ensure discount doesn't exceed total amount
    discountAmount = discountAmount > totalAmount ? totalAmount : discountAmount;

    return DiscountResult(
      discountAmount: discountAmount,
      message: 'Manual Discount: ₹${discountAmount.toStringAsFixed(2)}${reason != null ? ' ($reason)' : ''}',
      appliedDiscount: null,
    );
  }

  /// Validate coupon code
  Future<DiscountResult?> validateCouponCode({
    required String couponCode,
    required List<SalesTransactionDto> cartItems,
    required double totalAmount,
    String? workspaceId,
  }) async {
    try {
      await initialize();

      final discount = await _discountDao?.getDiscountByCouponCode(couponCode);
      final discounts = discount != null ? [discount] : <Discounts>[];

      for (final discount in discounts) {
        final result = await _evaluateDiscount(
          discount: discount,
          cartItems: cartItems,
          totalAmount: totalAmount,
          couponCode: couponCode,
        );

        if (result != null) {
          return result;
        }
      }

      return null;
    } catch (e) {
      log('Error validating coupon code: $e');
      return null;
    }
  }
}

/// Result of discount calculation
class DiscountResult {
  final double discountAmount;
  final String message;
  final Discounts? appliedDiscount;

  DiscountResult({
    required this.discountAmount,
    required this.message,
    this.appliedDiscount,
  });
}
