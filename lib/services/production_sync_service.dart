import 'dart:developer';
import '../database/app_database.dart';
import '../database/entities/sales_dto.dart';
import '../database/entities/product.dart';

/// Production-ready sync service for handling data synchronization
class ProductionSyncService {
  /// Sync all pending data to server
  static Future<SyncResult> syncAllData() async {
    try {
      log('Starting full data sync...');

      final results = <String, bool>{};

      // Sync discounts
      final discountResult = await syncDiscounts();
      results['discounts'] = discountResult.success;

      // Sync products
      final productResult = await syncProducts();
      results['products'] = productResult.success;

      // Sync sales
      final salesResult = await syncSales();
      results['sales'] = salesResult.success;

      final allSuccess = results.values.every((success) => success);

      log('Full sync completed. Success: $allSuccess');

      return SyncResult(
        success: allSuccess,
        message: allSuccess
            ? 'All data synced successfully'
            : 'Some data failed to sync: ${results.entries.where((e) => !e.value).map((e) => e.key).join(', ')}',
        syncedItems: results.values.where((success) => success).length,
        failedItems: results.values.where((success) => !success).length,
      );
    } catch (e, stackTrace) {
      log('Error during full sync: $e');
      log('Stack trace: $stackTrace');
      return SyncResult(
        success: false,
        message: 'Sync failed: ${e.toString()}',
        syncedItems: 0,
        failedItems: 1,
      );
    }
  }

  /// Sync pending discounts
  static Future<SyncResult> syncDiscounts() async {
    try {
      log('Syncing discounts...');

      final enhancedDiscountDao = await AppDatabase().enhancedDiscountDao;

      // Get all pending discounts (syncStatus = 0)
      final pendingDiscounts = await enhancedDiscountDao.getDiscountsWithPagination(
        limit: 100,
        offset: 0,
      );

      final pendingCount = pendingDiscounts.where((d) => d.discountSync == 0).length;

      if (pendingCount == 0) {
        return SyncResult(
          success: true,
          message: 'No discounts to sync',
          syncedItems: 0,
          failedItems: 0,
        );
      }

      int syncedCount = 0;
      int failedCount = 0;

      for (final discount in pendingDiscounts.where((d) => d.discountSync == 0)) {
        try {
          // Simulate API call to sync discount
          final syncSuccess = await _simulateApiSync('discount', discount.toMap());

          if (syncSuccess) {
            // Update sync status to complete
            discount.discountSync = 1;
            await enhancedDiscountDao.updateDiscount(discount);
            syncedCount++;
            log('Synced discount: ${discount.discountID}');
          } else {
            failedCount++;
            log('Failed to sync discount: ${discount.discountID}');
          }
        } catch (e) {
          failedCount++;
          log('Error syncing discount ${discount.discountID}: $e');
        }
      }

      return SyncResult(
        success: failedCount == 0,
        message: 'Synced $syncedCount discounts, $failedCount failed',
        syncedItems: syncedCount,
        failedItems: failedCount,
      );
    } catch (e) {
      log('Error syncing discounts: $e');
      return SyncResult(
        success: false,
        message: 'Failed to sync discounts: ${e.toString()}',
        syncedItems: 0,
        failedItems: 1,
      );
    }
  }

  /// Sync pending products
  static Future<SyncResult> syncProducts() async {
    try {
      log('Syncing products...');

      final productDao = await AppDatabase().productDao;
      final products = await productDao.getAllProducts();

      // Filter products that need syncing (you can add a sync status field to ProductDto)
      final pendingProducts = products.where((p) => p.rowStatus == 0).toList();

      if (pendingProducts.isEmpty) {
        return SyncResult(
          success: true,
          message: 'No products to sync',
          syncedItems: 0,
          failedItems: 0,
        );
      }

      int syncedCount = 0;
      int failedCount = 0;

      for (final product in pendingProducts) {
        try {
          // Simulate API call to sync product
          final syncSuccess = await _simulateApiSync('product', product.toMap());

          if (syncSuccess) {
            // Update sync status
            final updatedProduct = ProductDto(
              id: product.id,
              shopId: product.shopId,
              productId: product.productId,
              productName: product.productName,
              createdDate: product.createdDate,
              updatedDate: DateTime.now().toIso8601String(),
              cgst: product.cgst,
              sgst: product.sgst,
              productDiscountAmount: product.productDiscountAmount,
              costOfProduction: product.costOfProduction,
              rowStatus: 1, // Mark as synced
              price: product.price,
              categoryId: product.categoryId,
              parcelAmount: product.parcelAmount,
              productImagePath: product.productImagePath,
            );

            await productDao.updateProduct(updatedProduct);
            syncedCount++;
            log('Synced product: ${product.productId}');
          } else {
            failedCount++;
            log('Failed to sync product: ${product.productId}');
          }
        } catch (e) {
          failedCount++;
          log('Error syncing product ${product.productId}: $e');
        }
      }

      return SyncResult(
        success: failedCount == 0,
        message: 'Synced $syncedCount products, $failedCount failed',
        syncedItems: syncedCount,
        failedItems: failedCount,
      );
    } catch (e) {
      log('Error syncing products: $e');
      return SyncResult(
        success: false,
        message: 'Failed to sync products: ${e.toString()}',
        syncedItems: 0,
        failedItems: 1,
      );
    }
  }

  /// Sync pending sales
  static Future<SyncResult> syncSales() async {
    try {
      log('Syncing sales...');

      final salesDao = await AppDatabase().salesDao;
      final sales = await salesDao.getAllSales();

      // Filter sales that need syncing
      final pendingSales = sales.where((s) => s.rowStatus == 0).toList();

      if (pendingSales.isEmpty) {
        return SyncResult(
          success: true,
          message: 'No sales to sync',
          syncedItems: 0,
          failedItems: 0,
        );
      }

      int syncedCount = 0;
      int failedCount = 0;

      for (final sale in pendingSales) {
        try {
          // Simulate API call to sync sale
          final syncSuccess = await _simulateApiSync('sale', sale.toMap());

          if (syncSuccess) {
            // Update sync status
            final updatedSale = SalesDto(
              id: sale.id,
              salesId: sale.salesId,
              salesTransactionId: sale.salesTransactionId,
              createdDate: sale.createdDate,
              updatedDate: DateTime.now().toIso8601String(),
              totalAmount: sale.totalAmount,
              subtotalAmount: sale.subtotalAmount,
              discountAmount: sale.discountAmount,
              parcelAmount: sale.parcelAmount,
              cgstAmount: sale.cgstAmount,
              sgstAmount: sale.sgstAmount,
              igstAmount: sale.igstAmount,
              cessAmount: sale.cessAmount,
              totalTaxAmount: sale.totalTaxAmount,
              isReverseCharge: sale.isReverseCharge,
              stateCode: sale.stateCode,
              taxConfiguration: sale.taxConfiguration,
              status: sale.status,
              rowStatus: 1, // Mark as synced
              customerId: sale.customerId,
            );

            await salesDao.updateSale(updatedSale);
            syncedCount++;
            log('Synced sale: ${sale.salesId}');
          } else {
            failedCount++;
            log('Failed to sync sale: ${sale.salesId}');
          }
        } catch (e) {
          failedCount++;
          log('Error syncing sale ${sale.salesId}: $e');
        }
      }

      return SyncResult(
        success: failedCount == 0,
        message: 'Synced $syncedCount sales, $failedCount failed',
        syncedItems: syncedCount,
        failedItems: failedCount,
      );
    } catch (e) {
      log('Error syncing sales: $e');
      return SyncResult(
        success: false,
        message: 'Failed to sync sales: ${e.toString()}',
        syncedItems: 0,
        failedItems: 1,
      );
    }
  }

  /// Simulate API sync call (replace with actual API implementation)
  static Future<bool> _simulateApiSync(String dataType, Map<String, dynamic> data) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 100));

      // Simulate 90% success rate
      final random = DateTime.now().millisecondsSinceEpoch % 10;
      final success = random < 9;

      if (success) {
        log('Successfully synced $dataType: ${data['id'] ?? 'unknown'}');
      } else {
        log('Failed to sync $dataType: ${data['id'] ?? 'unknown'}');
      }

      return success;
    } catch (e) {
      log('Error in API sync for $dataType: $e');
      return false;
    }
  }

  /// Get sync status summary
  static Future<SyncStatusSummary> getSyncStatus() async {
    try {
      final enhancedDiscountDao = await AppDatabase().enhancedDiscountDao;
      final productDao = await AppDatabase().productDao;
      final salesDao = await AppDatabase().salesDao;

      final discounts = await enhancedDiscountDao.getDiscountsWithPagination(limit: 1000);
      final products = await productDao.getAllProducts();
      final sales = await salesDao.getAllSales();

      final pendingDiscounts = discounts.where((d) => d.discountSync == 0).length;
      final pendingProducts = products.where((p) => p.rowStatus == 0).length;
      final pendingSales = sales.where((s) => s.rowStatus == 0).length;

      return SyncStatusSummary(
        pendingDiscounts: pendingDiscounts,
        pendingProducts: pendingProducts,
        pendingSales: pendingSales,
        totalPending: pendingDiscounts + pendingProducts + pendingSales,
      );
    } catch (e) {
      log('Error getting sync status: $e');
      return SyncStatusSummary(
        pendingDiscounts: 0,
        pendingProducts: 0,
        pendingSales: 0,
        totalPending: 0,
      );
    }
  }
}

/// Sync result class
class SyncResult {
  final bool success;
  final String message;
  final int syncedItems;
  final int failedItems;

  SyncResult({
    required this.success,
    required this.message,
    required this.syncedItems,
    required this.failedItems,
  });
}

/// Sync status summary
class SyncStatusSummary {
  final int pendingDiscounts;
  final int pendingProducts;
  final int pendingSales;
  final int totalPending;

  SyncStatusSummary({
    required this.pendingDiscounts,
    required this.pendingProducts,
    required this.pendingSales,
    required this.totalPending,
  });

  bool get hasPendingItems => totalPending > 0;
}
