import 'dart:developer';
import '../database/entities/sales_transaction_dto.dart';
import '../database/entities/product.dart';
import '../database/app_database.dart';
import '../features/settings/settings_provider.dart';

/// Professional GST Service for comprehensive tax management
class ProfessionalGSTService {
  static final ProfessionalGSTService _instance = ProfessionalGSTService._internal();
  factory ProfessionalGSTService() => _instance;
  ProfessionalGSTService._internal();

  /// Calculate comprehensive GST with professional features
  Future<ProfessionalGSTCalculation> calculateProfessionalGST({
    required List<SalesTransactionDto> cartItems,
    required double discountAmount,
    required double parcelAmount,
    required SettingsProvider settingsProvider,
    String? stateCode, // For IGST calculation
    Map<String, double>? customTaxRates, // Override tax rates for specific items
    bool applyReverseCharge = false,
  }) async {
    try {
      if (!settingsProvider.isTaxEnabled) {
        return _createZeroTaxCalculation(cartItems, discountAmount, parcelAmount);
      }

      final subtotal = _calculateSubtotal(cartItems);
      final taxableAmount = subtotal - discountAmount;

      List<ProfessionalGSTBreakdownItem> gstBreakdown = [];
      double totalCGST = 0;
      double totalSGST = 0;
      double totalIGST = 0;
      double totalCess = 0;

      // Group items by GST rate and HSN code
      Map<String, List<SalesTransactionDto>> itemsByTaxGroup = {};

      for (final item in cartItems) {
        final product = await _getProductDetails(item.productId ?? '');
        final taxInfo = await _getTaxInformation(product, settingsProvider, customTaxRates, salesItem: item);

        final groupKey = '${taxInfo.gstRate}_${taxInfo.hsnCode}_${taxInfo.cessRate}';

        if (!itemsByTaxGroup.containsKey(groupKey)) {
          itemsByTaxGroup[groupKey] = [];
        }
        itemsByTaxGroup[groupKey]!.add(item);
      }

      // Calculate tax for each group
      for (final entry in itemsByTaxGroup.entries) {
        final items = entry.value;
        final firstItem = items.first;
        final product = await _getProductDetails(firstItem.productId ?? '');
        final taxInfo =
            await _getTaxInformation(product, settingsProvider, customTaxRates, salesItem: firstItem);

        final groupSubtotal = items.fold<double>(0, (sum, item) => sum + (item.productAmount ?? 0));
        final groupDiscountRatio = discountAmount > 0 ? groupSubtotal / subtotal : 0;
        final groupDiscount = discountAmount * groupDiscountRatio;
        final groupTaxableAmount = groupSubtotal - groupDiscount;

        if (taxInfo.gstRate > 0 && groupTaxableAmount > 0) {
          final isInterState = _isInterStateTrade(stateCode);

          double cgst = 0, sgst = 0, igst = 0, cess = 0;

          if (isInterState) {
            // Inter-state: IGST
            igst = (groupTaxableAmount * taxInfo.gstRate) / 100;
          } else {
            // Intra-state: CGST + SGST
            final firstItem = items.first;

            // Check if custom tax rates are being used
            final hasCustomRate = customTaxRates != null &&
                firstItem.productId != null &&
                customTaxRates.containsKey(firstItem.productId);

            if (hasCustomRate) {
              // For custom rates, calculate CGST and SGST as half of the custom rate
              cgst = (groupTaxableAmount * taxInfo.gstRate) / 200; // Half of GST rate
              sgst = (groupTaxableAmount * taxInfo.gstRate) / 200; // Half of GST rate
            } else if (firstItem.cgst != null && firstItem.sgst != null) {
              // Use the actual CGST/SGST values, scaled by the taxable amount ratio
              final originalAmount = firstItem.productAmount ?? 0;
              if (originalAmount > 0) {
                final scaleFactor = groupTaxableAmount / groupSubtotal;
                cgst = (firstItem.cgst ?? 0) * scaleFactor;
                sgst = (firstItem.sgst ?? 0) * scaleFactor;
              }
            } else {
              // Calculate CGST and SGST as half of total GST rate
              cgst = (groupTaxableAmount * taxInfo.gstRate) / 200; // Half of GST rate
              sgst = (groupTaxableAmount * taxInfo.gstRate) / 200; // Half of GST rate
            }
          }

          // Calculate Cess if applicable
          if (taxInfo.cessRate > 0) {
            cess = (groupTaxableAmount * taxInfo.cessRate) / 100;
          }

          totalCGST += cgst;
          totalSGST += sgst;
          totalIGST += igst;
          totalCess += cess;

          gstBreakdown.add(ProfessionalGSTBreakdownItem(
            gstRate: taxInfo.gstRate,
            cessRate: taxInfo.cessRate,
            hsnCode: taxInfo.hsnCode,
            taxableAmount: groupTaxableAmount,
            cgst: cgst,
            sgst: sgst,
            igst: igst,
            cess: cess,
            totalTax: cgst + sgst + igst + cess,
            items: items.map((item) => item.productName ?? '').toList(),
            isReverseCharge: applyReverseCharge,
          ));
        }
      }

      final totalGST = totalCGST + totalSGST + totalIGST;
      final totalTax = totalGST + totalCess;
      final grandTotal = taxableAmount + totalTax + parcelAmount;

      return ProfessionalGSTCalculation(
        subtotal: subtotal,
        discountAmount: discountAmount,
        parcelAmount: parcelAmount,
        cgst: totalCGST,
        sgst: totalSGST,
        igst: totalIGST,
        cess: totalCess,
        totalGST: totalGST,
        totalTax: totalTax,
        grandTotal: grandTotal,
        taxableAmount: taxableAmount,
        gstBreakdown: gstBreakdown,
        isReverseCharge: applyReverseCharge,
        stateCode: stateCode,
      );
    } catch (e) {
      log('Error calculating professional GST: $e');
      return _createZeroTaxCalculation(cartItems, discountAmount, parcelAmount);
    }
  }

  /// Get comprehensive tax information for a product
  Future<ProductTaxInfo> _getTaxInformation(
      ProductDto? product, SettingsProvider settingsProvider, Map<String, double>? customTaxRates,
      {SalesTransactionDto? salesItem}) async {
    // Check for custom tax rates first (highest priority)
    if (customTaxRates != null && salesItem != null && customTaxRates.containsKey(salesItem.productId)) {
      return ProductTaxInfo(
        gstRate: customTaxRates[salesItem.productId] ?? 0,
        cessRate: 0, // Custom rates don't include cess for now
        hsnCode: salesItem.productId ?? '',
      );
    }

    // Check for custom tax rates in product
    if (customTaxRates != null && product != null && customTaxRates.containsKey(product.productId)) {
      return ProductTaxInfo(
        gstRate: customTaxRates[product.productId] ?? 0,
        cessRate: 0, // Custom rates don't include cess for now
        hsnCode: product.productUniqId ?? '',
      );
    }

    // Then, try to use tax information from sales transaction if available
    if (salesItem != null && salesItem.cgst != null && salesItem.sgst != null) {
      final cgst = salesItem.cgst ?? 0;
      final sgst = salesItem.sgst ?? 0;
      final totalGST = cgst + sgst;

      return ProductTaxInfo(
        gstRate: totalGST,
        cessRate: 0,
        hsnCode: salesItem.productId ?? '',
      );
    }

    if (product == null) {
      return ProductTaxInfo(gstRate: 0, cessRate: 0, hsnCode: '');
    }

    // Check settings for tax application
    if (settingsProvider.applyTaxAllProducts || settingsProvider.applyTaxSpecificProduct) {
      final cgst = double.tryParse(product.cgst ?? '0') ?? 0;
      final sgst = double.tryParse(product.sgst ?? '0') ?? 0;
      final totalGST = cgst + sgst;

      return ProductTaxInfo(
        gstRate: totalGST,
        cessRate: 0, // Can be extended to support cess
        hsnCode: product.productUniqId ?? '',
      );
    }

    return ProductTaxInfo(gstRate: 0, cessRate: 0, hsnCode: '');
  }

  /// Check if trade is inter-state
  bool _isInterStateTrade(String? stateCode) {
    // Default to intra-state if no state code provided
    if (stateCode == null || stateCode.isEmpty) return false;

    // For testing purposes, any non-empty state code different from default triggers IGST
    // In production, this should compare with business state code
    const String businessStateCode = '19'; // Default business state (West Bengal)
    return stateCode != businessStateCode;
  }

  /// Get product details from database
  Future<ProductDto?> _getProductDetails(String productId) async {
    try {
      final productDao = await AppDatabase().productDao;
      return await productDao.getProductById(productId);
    } catch (e) {
      log('Error getting product details: $e');
      return null;
    }
  }

  /// Calculate subtotal without tax
  double _calculateSubtotal(List<SalesTransactionDto> cartItems) {
    return cartItems.fold<double>(0, (sum, item) => sum + (item.productAmount ?? 0));
  }

  /// Create zero tax calculation for disabled tax scenarios
  ProfessionalGSTCalculation _createZeroTaxCalculation(
    List<SalesTransactionDto> cartItems,
    double discountAmount,
    double parcelAmount,
  ) {
    final subtotal = _calculateSubtotal(cartItems);
    return ProfessionalGSTCalculation(
      subtotal: subtotal,
      discountAmount: discountAmount,
      parcelAmount: parcelAmount,
      cgst: 0,
      sgst: 0,
      igst: 0,
      cess: 0,
      totalGST: 0,
      totalTax: 0,
      grandTotal: subtotal - discountAmount + parcelAmount,
      taxableAmount: subtotal - discountAmount,
      gstBreakdown: [],
      isReverseCharge: false,
      stateCode: null,
    );
  }

  /// Format currency for display
  String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Get tax summary for display
  String getTaxSummary(ProfessionalGSTCalculation calculation) {
    if (calculation.totalTax == 0) {
      return 'No tax applicable';
    }

    final parts = <String>[];
    if (calculation.cgst > 0) parts.add('CGST: ${formatCurrency(calculation.cgst)}');
    if (calculation.sgst > 0) parts.add('SGST: ${formatCurrency(calculation.sgst)}');
    if (calculation.igst > 0) parts.add('IGST: ${formatCurrency(calculation.igst)}');
    if (calculation.cess > 0) parts.add('Cess: ${formatCurrency(calculation.cess)}');

    return '${parts.join(', ')} (Total: ${formatCurrency(calculation.totalTax)})';
  }
}

/// Product tax information
class ProductTaxInfo {
  final double gstRate;
  final double cessRate;
  final String hsnCode;

  ProductTaxInfo({
    required this.gstRate,
    required this.cessRate,
    required this.hsnCode,
  });
}

/// Professional GST calculation result with comprehensive tax breakdown
class ProfessionalGSTCalculation {
  final double subtotal;
  final double discountAmount;
  final double parcelAmount;
  final double cgst;
  final double sgst;
  final double igst;
  final double cess;
  final double totalGST;
  final double totalTax;
  final double grandTotal;
  final double taxableAmount;
  final List<ProfessionalGSTBreakdownItem> gstBreakdown;
  final bool isReverseCharge;
  final String? stateCode;

  ProfessionalGSTCalculation({
    required this.subtotal,
    required this.discountAmount,
    required this.parcelAmount,
    required this.cgst,
    required this.sgst,
    required this.igst,
    required this.cess,
    required this.totalGST,
    required this.totalTax,
    required this.grandTotal,
    required this.taxableAmount,
    required this.gstBreakdown,
    required this.isReverseCharge,
    required this.stateCode,
  });

  @override
  String toString() {
    return 'ProfessionalGSTCalculation(subtotal: $subtotal, discount: $discountAmount, '
        'parcel: $parcelAmount, CGST: $cgst, SGST: $sgst, IGST: $igst, '
        'Cess: $cess, total: $grandTotal)';
  }
}

/// Detailed GST breakdown item with professional features
class ProfessionalGSTBreakdownItem {
  final double gstRate;
  final double cessRate;
  final String hsnCode;
  final double taxableAmount;
  final double cgst;
  final double sgst;
  final double igst;
  final double cess;
  final double totalTax;
  final List<String> items;
  final bool isReverseCharge;

  ProfessionalGSTBreakdownItem({
    required this.gstRate,
    required this.cessRate,
    required this.hsnCode,
    required this.taxableAmount,
    required this.cgst,
    required this.sgst,
    required this.igst,
    required this.cess,
    required this.totalTax,
    required this.items,
    required this.isReverseCharge,
  });
}
