import 'dart:developer';
import '../database/entities/sales_transaction_dto.dart';
import '../database/entities/product.dart';
import '../database/app_database.dart';
import '../features/settings/settings_provider.dart';

/// Simplified GST Service - Easy to understand and use
/// Handles basic GST calculations without complex features
class SimpleGSTService {
  /// Calculate GST for cart items with simple logic
  Future<SimpleGSTResult> calculateGST({
    required List<SalesTransactionDto> cartItems,
    required double discountAmount,
    required SettingsProvider settingsProvider,
  }) async {
    try {
      // If tax is disabled, return zero tax calculation
      if (!settingsProvider.isTaxEnabled) {
        return _createZeroTaxResult(cartItems, discountAmount);
      }

      final subtotal = _calculateSubtotal(cartItems);
      final taxableAmount = subtotal - discountAmount;

      double totalGST = 0;
      List<SimpleGSTItem> gstItems = [];

      // Calculate GST for each item
      for (final item in cartItems) {
        final product = await _getProductDetails(item.productId ?? '');
        final gstRate = _getGSTRate(product, settingsProvider, salesItem: item);

        if (gstRate > 0) {
          final itemAmount = item.productAmount ?? 0;
          final itemDiscountRatio = discountAmount > 0 ? itemAmount / subtotal : 0;
          final itemDiscount = discountAmount * itemDiscountRatio;
          final itemTaxableAmount = itemAmount - itemDiscount;

          // Only calculate GST if taxable amount is positive
          if (itemTaxableAmount > 0) {
            final itemGST = (itemTaxableAmount * gstRate) / 100;
            totalGST += itemGST;

            gstItems.add(SimpleGSTItem(
              productName: item.productName ?? 'Unknown',
              gstRate: gstRate,
              taxableAmount: itemTaxableAmount,
              gstAmount: itemGST,
            ));
          }
        }
      }

      final grandTotal = taxableAmount + totalGST;

      return SimpleGSTResult(
        subtotal: subtotal,
        discountAmount: discountAmount,
        taxableAmount: taxableAmount,
        totalGST: totalGST,
        grandTotal: grandTotal,
        gstItems: gstItems,
      );
    } catch (e) {
      log('Error calculating simple GST: $e');
      return _createZeroTaxResult(cartItems, discountAmount);
    }
  }

  /// Get GST rate for a product (simplified logic)
  double _getGSTRate(ProductDto? product, SettingsProvider settingsProvider,
      {SalesTransactionDto? salesItem}) {
    // If tax is not enabled, return 0
    if (!settingsProvider.isTaxEnabled) {
      log('Tax is disabled in settings');
      return 0;
    }

    // First, try to get GST rate from sales transaction item (for testing and direct values)
    if (salesItem != null && salesItem.cgst != null && salesItem.sgst != null) {
      final cgst = salesItem.cgst ?? 0;
      final sgst = salesItem.sgst ?? 0;
      log('Using GST rate from sales item: CGST=$cgst, SGST=$sgst');
      return cgst + sgst;
    }

    log('Tax settings - applyTaxAllProducts: ${settingsProvider.applyTaxAllProducts}, applyTaxSpecificProduct: ${settingsProvider.applyTaxSpecificProduct}');

    // If tax is enabled for all products
    if (settingsProvider.applyTaxAllProducts) {
      if (product != null) {
        // Try to get GST rate from product
        final cgst = double.tryParse(product.cgst ?? '0') ?? 0;
        final sgst = double.tryParse(product.sgst ?? '0') ?? 0;
        final totalGST = cgst + sgst;

        log('Product ${product.productName}: CGST=$cgst, SGST=$sgst, Total=$totalGST');

        // If product has GST rate, use it
        if (totalGST > 0) return totalGST;
      }

      // If product doesn't have GST rate, use default 18%
      log('Using default GST rate of 18%');
      return 18.0;
    }

    // If tax is enabled for specific products only
    if (settingsProvider.applyTaxSpecificProduct && product != null) {
      final cgst = double.tryParse(product.cgst ?? '0') ?? 0;
      final sgst = double.tryParse(product.sgst ?? '0') ?? 0;
      final totalGST = cgst + sgst;

      log('Specific product ${product.productName}: CGST=$cgst, SGST=$sgst, Total=$totalGST');
      return totalGST;
    }

    log('No GST applicable');
    return 0;
  }

  /// Get product details from database
  Future<ProductDto?> _getProductDetails(String productId) async {
    try {
      final productDao = await AppDatabase().productDao;
      final product = await productDao.getProductById(productId);
      if (product != null) {
        log('Found product: ${product.productName}, CGST: ${product.cgst}, SGST: ${product.sgst}');
        return product;
      } else {
        log('Product not found for ID: $productId, trying alternative lookup...');

        // Try alternative lookup by getting all products and finding by ID
        final allProducts = await productDao.getAllProducts();
        final foundProduct = allProducts.cast<ProductDto?>().firstWhere(
              (p) => p?.productId == productId,
              orElse: () => null,
            );

        if (foundProduct != null) {
          log('Found product via alternative lookup: ${foundProduct.productName}');
          return foundProduct;
        } else {
          log('Product definitely not found for ID: $productId');
          return null;
        }
      }
    } catch (e) {
      log('Error getting product details: $e');
      return null;
    }
  }

  /// Calculate subtotal of all items
  double _calculateSubtotal(List<SalesTransactionDto> cartItems) {
    return cartItems.fold<double>(0, (sum, item) => sum + (item.productAmount ?? 0));
  }

  /// Create zero tax result when tax is disabled
  SimpleGSTResult _createZeroTaxResult(List<SalesTransactionDto> cartItems, double discountAmount) {
    final subtotal = _calculateSubtotal(cartItems);
    final grandTotal = subtotal - discountAmount;

    return SimpleGSTResult(
      subtotal: subtotal,
      discountAmount: discountAmount,
      taxableAmount: subtotal - discountAmount,
      totalGST: 0,
      grandTotal: grandTotal,
      gstItems: [],
    );
  }

  /// Format GST amount for display
  String formatGSTAmount(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }

  /// Get simple GST summary text
  String getGSTSummary(SimpleGSTResult result) {
    if (result.totalGST == 0) {
      return 'No GST applicable';
    }

    return 'Total GST: ${formatGSTAmount(result.totalGST)}';
  }

  /// Check if product has GST
  bool hasGST(ProductDto? product, SettingsProvider settingsProvider, {SalesTransactionDto? salesItem}) {
    return _getGSTRate(product, settingsProvider, salesItem: salesItem) > 0;
  }

  /// Calculate GST for a single product
  double calculateProductGST({
    required double productAmount,
    required double gstRate,
  }) {
    return (productAmount * gstRate) / 100;
  }

  /// Get CGST and SGST breakdown (for display purposes)
  Map<String, double> getCGSTSGSTBreakdown(double gstAmount) {
    return {
      'cgst': gstAmount / 2,
      'sgst': gstAmount / 2,
    };
  }
}

/// Simple GST calculation result
class SimpleGSTResult {
  final double subtotal;
  final double discountAmount;
  final double taxableAmount;
  final double totalGST;
  final double grandTotal;
  final List<SimpleGSTItem> gstItems;

  SimpleGSTResult({
    required this.subtotal,
    required this.discountAmount,
    required this.taxableAmount,
    required this.totalGST,
    required this.grandTotal,
    required this.gstItems,
  });

  /// Get CGST amount (half of total GST)
  double get cgst => totalGST / 2;

  /// Get SGST amount (half of total GST)
  double get sgst => totalGST / 2;

  /// Check if GST is applicable
  bool get hasGST => totalGST > 0;

  /// Get GST percentage (average across all items)
  double get averageGSTRate {
    if (gstItems.isEmpty || taxableAmount == 0) return 0;
    return (totalGST / taxableAmount) * 100;
  }

  @override
  String toString() {
    return 'SimpleGSTResult(subtotal: $subtotal, discount: $discountAmount, '
        'taxable: $taxableAmount, gst: $totalGST, total: $grandTotal)';
  }
}

/// Individual GST item for breakdown
class SimpleGSTItem {
  final String productName;
  final double gstRate;
  final double taxableAmount;
  final double gstAmount;

  SimpleGSTItem({
    required this.productName,
    required this.gstRate,
    required this.taxableAmount,
    required this.gstAmount,
  });

  /// Get CGST amount for this item
  double get cgst => gstAmount / 2;

  /// Get SGST amount for this item
  double get sgst => gstAmount / 2;

  @override
  String toString() {
    return 'SimpleGSTItem($productName: $gstRate% on ₹$taxableAmount = ₹$gstAmount)';
  }
}

/// Simple GST configuration
class SimpleGSTConfig {
  final String businessName;
  final String? gstin;
  final double defaultGSTRate;
  final bool enableGST;

  SimpleGSTConfig({
    required this.businessName,
    this.gstin,
    this.defaultGSTRate = 18.0,
    this.enableGST = true,
  });

  factory SimpleGSTConfig.fromMap(Map<String, dynamic> map) {
    return SimpleGSTConfig(
      businessName: map['businessName'] ?? '',
      gstin: map['gstin'],
      defaultGSTRate: map['defaultGSTRate']?.toDouble() ?? 18.0,
      enableGST: map['enableGST'] == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'businessName': businessName,
      'gstin': gstin,
      'defaultGSTRate': defaultGSTRate,
      'enableGST': enableGST ? 1 : 0,
    };
  }
}
