import '../repositories/auth_repository.dart';

/// Central service manager for all API repositories
class ApiServiceManager {
  static ApiServiceManager? _instance;
  
  // Repositories
  late final AuthRepository _authRepository;
  
  ApiServiceManager._internal() {
    _authRepository = AuthRepository();
  }
  
  static ApiServiceManager get instance {
    _instance ??= ApiServiceManager._internal();
    return _instance!;
  }
  
  // Repository getters
  AuthRepository get auth => _authRepository;
  
  /// Initialize the service manager
  static void initialize() {
    ApiServiceManager.instance;
  }
}

/// Extension to easily access repositories
extension ApiServiceExtension on ApiServiceManager {
  // Add more repository getters here as you create them
  // ProductRepository get products => _productRepository;
  // SalesRepository get sales => _salesRepository;
  // EmployeeRepository get employees => _employeeRepository;
}
