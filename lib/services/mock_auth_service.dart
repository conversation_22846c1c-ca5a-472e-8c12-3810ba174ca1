import 'dart:developer';
import '../core/network/api_response.dart';
import '../models/user_model.dart';

/// Mock authentication service that simulates API responses
/// This can be easily replaced with real API calls later
class MockAuthService {
  // Simulated delay to mimic network requests
  static const Duration _networkDelay = Duration(seconds: 1);

  // Mock user database - easily replaceable with real API
  // Professional Coffee Shop Demo Credentials
  static final List<Map<String, dynamic>> _mockUsers = [
    // Store Manager - Full Access
    {
      "userId": "MGR_001",
      "userName": "<PERSON>",
      "emailAddress": "<EMAIL>",
      "mobileNo": "9876543210",
      "password": "manager123",
      "role": "manager",
      "isActive": true,
      "workspaceId": "coffee_shop_001",
      "token": "mock_token_manager_001",
      "refreshToken": "refresh_token_manager_001",
      "department": "Management",
      "joinDate": "2023-01-15"
    },
    // Store Owner - Admin Access
    {
      "userId": "OWN_001",
      "userName": "<PERSON>",
      "emailAddress": "<EMAIL>",
      "mobileNo": "9123456789",
      "password": "owner2024",
      "role": "admin",
      "isActive": true,
      "workspaceId": "coffee_shop_001",
      "token": "mock_token_owner_001",
      "refreshToken": "refresh_token_owner_001",
      "department": "Administration",
      "joinDate": "2022-06-01"
    },
    // Barista - Staff Access
    {
      "userId": "BAR_001",
      "userName": "Emma Rodriguez",
      "emailAddress": "<EMAIL>",
      "mobileNo": "9234567890",
      "password": "barista123",
      "role": "user",
      "isActive": true,
      "workspaceId": "coffee_shop_001",
      "token": "mock_token_barista_001",
      "refreshToken": "refresh_token_barista_001",
      "department": "Operations",
      "joinDate": "2023-08-20"
    },
    // Cashier - Limited Access
    {
      "userId": "CSH_001",
      "userName": "David Kumar",
      "emailAddress": "<EMAIL>",
      "mobileNo": "9345678901",
      "password": "cashier123",
      "role": "user",
      "isActive": true,
      "workspaceId": "coffee_shop_001",
      "token": "mock_token_cashier_001",
      "refreshToken": "refresh_token_cashier_001",
      "department": "Sales",
      "joinDate": "2023-11-10"
    },
    // Demo User - Testing
    {
      "userId": "DEMO_001",
      "userName": "Demo User",
      "emailAddress": "<EMAIL>",
      "mobileNo": "9000000000",
      "password": "demo123",
      "role": "user",
      "isActive": true,
      "workspaceId": "coffee_shop_001",
      "token": "mock_token_demo_001",
      "refreshToken": "refresh_token_demo_001",
      "department": "Testing",
      "joinDate": "2024-01-01"
    },
    // Quick Test User - Simple credentials
    {
      "userId": "TEST_001",
      "userName": "Test User",
      "emailAddress": "<EMAIL>",
      "mobileNo": "1111111111",
      "password": "test",
      "role": "user",
      "isActive": true,
      "workspaceId": "coffee_shop_001",
      "token": "mock_token_test_001",
      "refreshToken": "refresh_token_test_001",
      "department": "Testing",
      "joinDate": "2024-01-01"
    }
  ];

  /// Mock login API call
  static Future<ApiResponse<LoginResponse>> login({
    required String identifier, // mobile or email
    required String password,
    String? deviceId,
    String? fcmToken,
  }) async {
    // Simulate network delay
    await Future.delayed(_networkDelay);

    try {
      // Find user by mobile or email
      final user = _mockUsers.firstWhere(
        (u) => (u['mobileNo'] == identifier || u['emailAddress'] == identifier) && u['password'] == password,
        orElse: () => {},
      );

      if (user.isEmpty) {
        return ApiResponse.error(
          message: 'Invalid credentials. Please check your mobile/email and password.',
          statusCode: 401,
        );
      }

      if (user['isActive'] != true) {
        return ApiResponse.error(
          message: 'Your account has been deactivated. Please contact support.',
          statusCode: 403,
        );
      }

      // Create mock login response
      final loginResponse = LoginResponse(
        status: 200,
        message: 'Login successful',
        response: User(
          userId: user['userId'],
          userName: user['userName'],
          emailAddress: user['emailAddress'],
          mobileNo: user['mobileNo'],
          token: user['token'],
          workspaceId: user['workspaceId'],
          workspaceName: 'Coffee Shop Workspace', // Default workspace name
          role: user['role'],
        ),
      );

      return ApiResponse.success(
        message: 'Login successful',
        data: loginResponse,
        statusCode: 200,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'An error occurred during login. Please try again.',
        statusCode: 500,
      );
    }
  }

  /// Mock register API call
  static Future<ApiResponse<LoginResponse>> register({
    required String userName,
    required String email,
    required String password,
    required String phoneNumber,
    String? role,
    String? deviceId,
    String? fcmToken,
  }) async {
    // Simulate network delay
    await Future.delayed(_networkDelay);

    try {
      // Check if user already exists
      final existingUser = _mockUsers.firstWhere(
        (u) => u['emailAddress'] == email || u['mobileNo'] == phoneNumber,
        orElse: () => {},
      );

      if (existingUser.isNotEmpty) {
        return ApiResponse.error(
          message: 'User with this email or mobile number already exists.',
          statusCode: 409,
        );
      }

      // Create new user
      final newUserId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      final newUser = {
        "userId": newUserId,
        "userName": userName,
        "emailAddress": email,
        "mobileNo": phoneNumber,
        "password": password,
        "role": role ?? "user",
        "isActive": true,
        "workspaceId": "ws_001",
        "token": "mock_token_${newUserId}_${DateTime.now().millisecondsSinceEpoch}",
        "refreshToken": "refresh_token_${newUserId}_${DateTime.now().millisecondsSinceEpoch}"
      };

      // Add to mock database
      _mockUsers.add(newUser);

      // Create mock register response
      final loginResponse = LoginResponse(
        status: 201,
        message: 'Registration successful',
        response: User(
          userId: newUser['userId'] as String?,
          userName: newUser['userName'] as String?,
          emailAddress: newUser['emailAddress'] as String?,
          mobileNo: newUser['mobileNo'] as String?,
          token: newUser['token'] as String?,
          workspaceId: newUser['workspaceId'] as String?,
          workspaceName: 'Coffee Shop Workspace', // Default workspace name
          role: newUser['role'] as String?,
        ),
      );

      return ApiResponse.success(
        message: 'Registration successful',
        data: loginResponse,
        statusCode: 201,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'An error occurred during registration. Please try again.',
        statusCode: 500,
      );
    }
  }

  /// Mock forgot password API call
  static Future<ApiResponse<Map<String, dynamic>>> forgotPassword({
    required String email,
  }) async {
    await Future.delayed(_networkDelay);

    try {
      final user = _mockUsers.firstWhere(
        (u) => u['emailAddress'] == email,
        orElse: () => {},
      );

      if (user.isEmpty) {
        return ApiResponse.error(
          message: 'No account found with this email address.',
          statusCode: 404,
        );
      }

      return ApiResponse.success(
        message: 'Password reset instructions have been sent to your email.',
        data: {
          'email': email,
          'resetToken': 'mock_reset_token_${DateTime.now().millisecondsSinceEpoch}',
          'expiresIn': '15 minutes'
        },
        statusCode: 200,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to send reset email. Please try again.',
        statusCode: 500,
      );
    }
  }

  /// Mock get profile API call
  static Future<ApiResponse<User>> getProfile(String token) async {
    await Future.delayed(_networkDelay);

    try {
      final user = _mockUsers.firstWhere(
        (u) => u['token'] == token,
        orElse: () => {},
      );

      if (user.isEmpty) {
        return ApiResponse.error(
          message: 'Invalid or expired token.',
          statusCode: 401,
        );
      }

      final userProfile = User(
        userId: user['userId'] as String?,
        userName: user['userName'] as String?,
        emailAddress: user['emailAddress'] as String?,
        mobileNo: user['mobileNo'] as String?,
        token: user['token'] as String?,
        workspaceId: user['workspaceId'] as String?,
        workspaceName: 'Coffee Shop Workspace', // Default workspace name
        role: user['role'] as String?,
      );

      return ApiResponse.success(
        message: 'Profile retrieved successfully',
        data: userProfile,
        statusCode: 200,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to retrieve profile.',
        statusCode: 500,
      );
    }
  }

  /// Get all mock users (for testing purposes)
  static List<Map<String, dynamic>> getAllMockUsers() {
    return _mockUsers.map((user) {
      final userCopy = Map<String, dynamic>.from(user);
      userCopy.remove('password'); // Don't expose passwords
      return userCopy;
    }).toList();
  }

  /// Add a new mock user (for testing purposes)
  static void addMockUser(Map<String, dynamic> user) {
    _mockUsers.add(user);
  }

  /// Clear all mock users (for testing purposes)
  static void clearMockUsers() {
    _mockUsers.clear();
  }

  /// Print mock users for debugging
  static void printMockUsers() {
    log('=== Mock Users Database ===');
    for (int i = 0; i < _mockUsers.length; i++) {
      final user = _mockUsers[i];
      log('${i + 1}. ${user['userName']} (${user['emailAddress']}) - Password: ${user['password']}');
    }
    log('========================');
  }
}
