import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../core/network/api_endpoints.dart';
import '../core/network/api_response.dart';
import '../core/network/dio_client.dart';
import '../models/user_model.dart';
import '../models/register_user_model.dart';
import '../services/mock_auth_service.dart';

/// Authentication repository for handling all auth-related API calls
class AuthRepository {
  final DioClient _dioClient = DioClient.instance;

  // Configuration flag - controlled by AppConfig
  static bool get _useMockService => AppConfig.useMockAuth;

  /// Login user with mobile/email and password
  Future<ApiResponse<LoginResponse>> login({
    required String identifier, // mobile or email
    required String password,
    String? deviceId,
    String? fcmToken,
  }) async {
    try {
      ApiResponse<LoginResponse> response;

      if (_useMockService) {
        // Use mock service for development/testing
        response = await MockAuthService.login(
          identifier: identifier,
          password: password,
          deviceId: deviceId,
          fcmToken: fcmToken,
        );
      } else {
        // Use real API service
        response = await _dioClient.post<LoginResponse>(
          ApiEndpoints.login,
          data: {
            'identifier': identifier,
            'password': password,
            'device_id': deviceId,
            'fcm_token': fcmToken,
          },
          fromJson: (json) => LoginResponse.fromJson(json),
        );
      }

      // Save auth tokens if login successful
      if (response.success && response.data?.response?.token != null) {
        await _saveAuthTokens(
          token: response.data!.response!.token!,
          refreshToken: response.data?.response?.token ?? '', // Assuming same for now
        );
      }

      return response;
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(
          message: e.message,
          statusCode: e.statusCode,
          errors: e.errors,
        );
      } else if (e is NetworkException) {
        return ApiResponse.error(
          message: 'No internet connection. Please check your network.',
        );
      } else if (e is TimeoutException) {
        return ApiResponse.error(
          message: 'Request timeout. Please try again.',
        );
      } else {
        return ApiResponse.error(
          message: 'An unexpected error occurred. Please try again.',
        );
      }
    }
  }

  /// Register new user
  Future<ApiResponse<LoginResponse>> register({
    required String userName,
    required String email,
    required String password,
    required String phoneNumber,
    String? role,
    String? deviceId,
    String? fcmToken,
  }) async {
    try {
      ApiResponse<LoginResponse> response;

      if (_useMockService) {
        // Use mock service for development/testing
        response = await MockAuthService.register(
          userName: userName,
          email: email,
          password: password,
          phoneNumber: phoneNumber,
          role: role,
          deviceId: deviceId,
          fcmToken: fcmToken,
        );
      } else {
        // Use real API service
        final registerData = RegisterUserModel(
          userName: userName,
          email: email,
          password: password,
          phoneNumber: int.tryParse(phoneNumber),
          role: role ?? 'user',
        );

        response = await _dioClient.post<LoginResponse>(
          ApiEndpoints.register,
          data: {
            ...registerData.toJson(),
            'device_id': deviceId,
            'fcm_token': fcmToken,
          },
          fromJson: (json) => LoginResponse.fromJson(json),
        );
      }

      // Save auth tokens if registration successful
      if (response.success && response.data?.response?.token != null) {
        await _saveAuthTokens(
          token: response.data!.response!.token!,
          refreshToken: response.data?.response?.token ?? '', // Assuming same for now
        );
      }

      return response;
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(
          message: e.message,
          statusCode: e.statusCode,
          errors: e.errors,
        );
      } else if (e is NetworkException) {
        return ApiResponse.error(
          message: 'No internet connection. Please check your network.',
        );
      } else if (e is TimeoutException) {
        return ApiResponse.error(
          message: 'Request timeout. Please try again.',
        );
      } else {
        return ApiResponse.error(
          message: 'An unexpected error occurred. Please try again.',
        );
      }
    }
  }

  /// Refresh authentication token
  Future<ApiResponse<Map<String, dynamic>>> refreshToken() async {
    try {
      final refreshToken = await _getRefreshToken();
      if (refreshToken == null) {
        return ApiResponse.error(message: 'No refresh token available');
      }

      final response = await _dioClient.post<Map<String, dynamic>>(
        ApiEndpoints.refreshToken,
        data: {'refresh_token': refreshToken},
        fromJson: (json) => json,
      );

      // Save new tokens
      if (response.success && response.data != null) {
        final newToken = response.data!['access_token'];
        final newRefreshToken = response.data!['refresh_token'];

        if (newToken != null) {
          await _saveAuthTokens(
            token: newToken,
            refreshToken: newRefreshToken ?? refreshToken,
          );
        }
      }

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: e.toString(),
      );
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    try {
      final response = await _dioClient.post<void>(
        ApiEndpoints.logout,
        data: {},
      );

      // Clear local tokens regardless of API response
      await _clearAuthTokens();

      return response;
    } catch (e) {
      // Clear local tokens even if API call fails
      await _clearAuthTokens();

      return ApiResponse.error(
        message: 'Logout completed locally',
      );
    }
  }

  /// Forgot password
  Future<ApiResponse<Map<String, dynamic>>> forgotPassword({
    required String email,
  }) async {
    try {
      final response = await _dioClient.post<Map<String, dynamic>>(
        ApiEndpoints.forgotPassword,
        data: {'email': email},
        fromJson: (json) => json,
      );

      return response;
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(
          message: e.message,
          statusCode: e.statusCode,
          errors: e.errors,
        );
      } else {
        return ApiResponse.error(
          message: 'Failed to send reset email. Please try again.',
        );
      }
    }
  }

  /// Reset password
  Future<ApiResponse<Map<String, dynamic>>> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      final response = await _dioClient.post<Map<String, dynamic>>(
        ApiEndpoints.resetPassword,
        data: {
          'token': token,
          'password': newPassword,
        },
        fromJson: (json) => json,
      );

      return response;
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(
          message: e.message,
          statusCode: e.statusCode,
          errors: e.errors,
        );
      } else {
        return ApiResponse.error(
          message: 'Failed to reset password. Please try again.',
        );
      }
    }
  }

  /// Verify OTP
  Future<ApiResponse<Map<String, dynamic>>> verifyOtp({
    required String identifier,
    required String otp,
  }) async {
    try {
      final response = await _dioClient.post<Map<String, dynamic>>(
        ApiEndpoints.verifyOtp,
        data: {
          'identifier': identifier,
          'otp': otp,
        },
        fromJson: (json) => json,
      );

      return response;
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(
          message: e.message,
          statusCode: e.statusCode,
          errors: e.errors,
        );
      } else {
        return ApiResponse.error(
          message: 'Failed to verify OTP. Please try again.',
        );
      }
    }
  }

  /// Get current user profile
  Future<ApiResponse<User>> getProfile() async {
    try {
      ApiResponse<User> response;

      if (_useMockService) {
        // Use mock service for development/testing
        final token = await _getAuthToken();
        if (token == null) {
          return ApiResponse.error(
            message: 'No authentication token found',
            statusCode: 401,
          );
        }
        response = await MockAuthService.getProfile(token);
      } else {
        // Use real API service
        response = await _dioClient.get<User>(
          ApiEndpoints.profile,
          fromJson: (json) => User.fromJson(json),
        );
      }

      return response;
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(
          message: e.message,
          statusCode: e.statusCode,
          errors: e.errors,
        );
      } else {
        return ApiResponse.error(
          message: 'Failed to fetch profile. Please try again.',
        );
      }
    }
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await _getAuthToken();
    return token != null && token.isNotEmpty;
  }

  /// Get stored auth token
  Future<String?> _getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      return null;
    }
  }

  /// Get stored refresh token
  Future<String?> _getRefreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('refresh_token');
    } catch (e) {
      return null;
    }
  }

  /// Save authentication tokens
  Future<void> _saveAuthTokens({
    required String token,
    required String refreshToken,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
      await prefs.setString('refresh_token', refreshToken);
    } catch (e) {
      // Handle error silently or log it
    }
  }

  /// Clear authentication tokens
  Future<void> _clearAuthTokens() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('refresh_token');
    } catch (e) {
      // Handle error silently or log it
    }
  }
}
