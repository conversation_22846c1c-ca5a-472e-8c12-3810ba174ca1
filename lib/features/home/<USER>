import 'package:coffee_cofe/features/bill/billing_screen.dart';
import 'package:coffee_cofe/features/inventory/inventory_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sqlite_viewer2/sqlite_viewer.dart';
import '../product/product_list/product_list_page.dart';

class HomeMenu extends StatelessWidget {
  HomeMenu({super.key});

  final List<MenuItem> menuItems = [
    MenuItem(
        icon: Icons.coffee,
        label: "Products",
        color: Colors.blue,
        onTap: () {
          Get.to(const ProductListPage());
        }),
    MenuItem(
        icon: Icons.print,
        label: "Billing",
        color: Colors.brown,
        onTap: () {
          Get.to(const BillingScreen());
        }),
    MenuItem(icon: Icons.bar_chart, label: "Reports", color: Colors.red),
    MenuItem(
      icon: Icons.inventory,
      label: "Inventory",
      color: Colors.lightBlueAccent,
      onTap: () {
        Get.to(const InventoryScreen());
      },
    ),
    // MenuItem(icon: Icons.check_circle_outline_outlined, label: "Attendance", color: Colors.purple,),

    MenuItem(
        icon: Icons.storage_outlined,
        label: "Database",
        color: Colors.purple,
        onTap: () {
          Get.to(() => const DatabaseList());
        }),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Align(
        alignment: Alignment.topCenter,
        child: Wrap(
          spacing: 5,
          runSpacing: 5,
          children: menuItems.map((item) => MenuItemCard(item: item)).toList(),
        ),
      ),
    );
  }
}

class MenuItem {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback? onTap;

  MenuItem({required this.icon, required this.label, required this.color, this.onTap});
}

class MenuItemCard extends StatelessWidget {
  final MenuItem item;

  const MenuItemCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    double screenwidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenwidth > 600 ? screenwidth * 0.25 : screenwidth * 0.45,
      height: 85,
      child: Card(
        color: item.color.withOpacity(0.9),
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: item.onTap,
          borderRadius: BorderRadius.circular(15),
          splashColor: Colors.white.withOpacity(0.2),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                item.label,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              Icon(item.icon, size: 40, color: Colors.white60),
            ],
          ),
        ),
      ),
    );
  }
}
