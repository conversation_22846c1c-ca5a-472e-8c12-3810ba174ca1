import 'package:flutter/material.dart';
import '../../widgets/common_appbar.dart';
import '../profile/profile_settings_controller.dart';
import 'menu_section.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
            appBar: CommonAppBar(
              isEnableBrandLogo: true,
              appBarColor: bgColor,
              title: '    Buy coffee best coffee  ',
            ),

            // AppBar(
            //   title: Text(
            //     "Coffee POS - Home",
            //     style: Theme.of(context).textTheme.titleLarge,
            //   ),
            //   centerTitle: true,
            // ),
            body: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  HomeMenu(),
                  const Sized<PERSON><PERSON>(height: 20),
                ],
              ),
            ),
          );
        });
  }
}
