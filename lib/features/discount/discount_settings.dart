// ignore_for_file: non_constant_identifier_names

import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';

import '../../utils/function_utils.dart';
import '../../database/entities/workspace_dto.dart';
import '../../utils/db_stubs.dart' as stubs;
import '../../widgets/common_drawer.dart';
import 'create_discount.dart';
import 'manage_discount.dart';

class DiscountSettings extends StatefulWidget {
  const DiscountSettings({super.key});

  @override
  State<DiscountSettings> createState() => _DiscountSettingsState();
}

class _DiscountSettingsState extends State<DiscountSettings> {
  FnUtilities fnUtilities = FnUtilities();
  bool isLock = false;
  bool enableDiscount = true;
  String? selectedDiscountType = 'Sales Wise';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  _loadSettings() async {
    // Load discount settings from database if needed
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(
        title: "Discount Settings",
      ),
      backgroundColor: Colors.grey[100],
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Enable Discount Toggle
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Enable Discount',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Switch(
                    value: enableDiscount,
                    onChanged: (value) {
                      setState(() {
                        enableDiscount = value;
                      });
                    },
                    activeColor: Colors.purple,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Discount Type Dropdown
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Discount Type',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: selectedDiscountType,
                      icon: const Icon(Icons.keyboard_arrow_down),
                      items: <String>['Sales Wise', 'Product Wise'].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      onChanged: (String? val) async {
                        setState(() {
                          selectedDiscountType = val;
                        });
                        WorkspaceSettingsDto settings = WorkspaceSettingsDto();
                        settings.wSSettingKey = "DiscountType";
                        settings.wSSettingValue = selectedDiscountType == "Sales Wise" ? "Sales" : "Product";
                        settings.workSsettingSync = 0;
                        settings.workSpaceId = stubs.MyApp.activeWorkspace.workspaceId;
                        await fnUtilities.updateSettingDetails(settings);
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      if (!isLock) {
                        isLock = true;
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const CreateDiscount()),
                        ).then((_) => isLock = false);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Create Discount',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      if (!isLock) {
                        isLock = true;
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const ManageDiscount()),
                        ).then((_) => isLock = false);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'View Discount',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
