import 'package:flutter/material.dart';
import '../../utils/function_utils.dart';
import '../../models/discounts.dart';
import '../../utils/db_stubs.dart';
import '../../widgets/common_appbar.dart';
import '../../database/app_database.dart';
import '../../database/dao/enhanced_discount_dao.dart';
import 'create_discount.dart';

//PRODUCT LIST VIEW CLASS
class ManageDiscount extends StatefulWidget {
  const ManageDiscount({super.key});

  @override
  ManageDiscountState createState() => ManageDiscountState();
}

class ManageDiscountState extends State<ManageDiscount> {
  DiscountDB discountDB = DiscountDB();
  CommonDB commonDB = CommonDB();
  FnUtilities function = FnUtilities();
  EnhancedDiscountDao? enhancedDiscountDao;
  List<Discounts> discountList = [];
  bool isLock = false;
  double width = 0;
  double height = 0;
  bool isLoading = true;

  //GET ALL DISCOUNT DETAILS
  getAllDiscount() async {
    try {
      setState(() {
        isLoading = true;
      });

      // Initialize the DAO if not already done
      if (enhancedDiscountDao == null) {
        final database = await AppDatabase().database;
        enhancedDiscountDao = EnhancedDiscountDao(database);
      }

      // Get all discounts using the enhanced DAO
      final discounts = await enhancedDiscountDao!.getDiscountsWithPagination(
        limit: 100, // Get first 100 discounts
        offset: 0,
      );

      setState(() {
        discountList = discounts;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading discounts: $e');
      setState(() {
        isLoading = false;
      });
      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading discounts: $e')),
        );
      }
    }
  }

  @override
  void initState() {
    getAllDiscount();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: const CommonAppBar(title: " Discount"),
      body: Column(
        children: [
          SizedBox(
            height: 3,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    height: height / 1.3,
                    child: Column(
                      children: [
                        Container(
                          height: 40,
                          child: Card(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  width: width / 5,
                                  child: TextWidget("Mode",
                                      textColor: primaryTextColor,
                                      fontFamily: fontBold,
                                      fontSize: textSizeMedium,
                                      isCentered: true),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: width / 4,
                                  child: TextWidget("Discount",
                                      textColor: primaryTextColor,
                                      fontFamily: fontBold,
                                      fontSize: textSizeMedium,
                                      isCentered: true),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: width / 4,
                                  child: TextWidget("Min",
                                      textColor: primaryTextColor,
                                      fontFamily: fontBold,
                                      fontSize: textSizeMedium,
                                      isCentered: true),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: width / 4,
                                  child: TextWidget("Max",
                                      textColor: primaryTextColor,
                                      fontFamily: fontBold,
                                      fontSize: textSizeMedium,
                                      isCentered: true),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.topCenter,
                            child: isLoading
                                ? const Center(
                                    child: CircularProgressIndicator(),
                                  )
                                : discountList.isEmpty
                                    ? const Center(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.discount_outlined,
                                              size: 64,
                                              color: Colors.grey,
                                            ),
                                            SizedBox(height: 16),
                                            Text(
                                              'No discounts found',
                                              style: TextStyle(
                                                fontSize: 18,
                                                color: Colors.grey,
                                              ),
                                            ),
                                            SizedBox(height: 8),
                                            Text(
                                              'Create your first discount to get started',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : ListView.builder(
                                        padding: const EdgeInsets.only(right: 0.0),
                                        scrollDirection: Axis.vertical,
                                        itemCount: discountList.length,
                                        shrinkWrap: true,
                                        physics: const ScrollPhysics(),
                                        itemBuilder: (context, index) {
                                          //DELETE ALERT DIALOG FOR DISCOUNT
                                          var item = discountList[index];
                                          function.splitValueFromFormula(item);
                                          return GestureDetector(
                                            onLongPress: () {
                                              if (PermissionFunctions.checkPermission(150)) {
                                                showDialog(
                                                    context: context,
                                                    builder: (BuildContext context) {
                                                      return AlertDialog(
                                                        content:
                                                            Text("Are you sure, do you want to delete ?"),
                                                        actions: <Widget>[
                                                          GestureDetector(
                                                            onTap: () async {
                                                              if (!isLock) {
                                                                isLock = true;
                                                                final syncNo =
                                                                    await commonDB.checkNonSyncCommonFunction(
                                                                        "Discount",
                                                                        "discountID",
                                                                        item.discountID.toString(),
                                                                        "discountSync");
                                                                if (syncNo == 0) {
                                                                  item.rowStatus = 2;
                                                                  item.discountSync = 0;
                                                                  await discountDB.updateDiscount(item);
                                                                } else {
                                                                  await commonDB
                                                                      .permanentDeleteCommonFunction(
                                                                          "Discount",
                                                                          "discountID",
                                                                          item.discountID.toString());
                                                                }

                                                                setState(() {
                                                                  getAllDiscount();
                                                                });
                                                                if (context.mounted) {
                                                                  Navigator.pop(context);
                                                                }
                                                                isLock = false;
                                                              }
                                                            },
                                                            child: Container(
                                                              alignment: Alignment.center,
                                                              height: 40,
                                                              width: width / 5,
                                                              child: TextWidget("Yes",
                                                                  textColor: secondaryTextColor,
                                                                  isCentered: true),
                                                              decoration: boxDecoration(
                                                                  bgColor: buttonThemeColor, radius: 8.0),
                                                            ),
                                                          ),
                                                          GestureDetector(
                                                            onTap: () {
                                                              if (!isLock) {
                                                                isLock = true;
                                                                Navigator.pop(context);
                                                                isLock = false;
                                                              }
                                                            },
                                                            child: Container(
                                                              alignment: Alignment.center,
                                                              height: 40,
                                                              width: width / 5,
                                                              child: TextWidget("Cancel",
                                                                  textColor: secondaryTextColor,
                                                                  isCentered: true),
                                                              decoration: boxDecoration(
                                                                  bgColor: buttonThemeColor, radius: 8.0),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    });
                                              } else {
                                                showToast("You don't have permission.");
                                              }
                                              // return res;
                                            },
                                            child: Card(
                                              margin: EdgeInsets.all(0.0),
                                              color: index % 2 == 0 ? secondaryTextColor : Colors.blue[50],
                                              child: InkWell(
                                                onDoubleTap: () async {
                                                  if (PermissionFunctions.checkPermission(149)) {
                                                    Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                            builder: (context) => CreateDiscount(
                                                                  discounts: item,
                                                                ))).then((value) async {
                                                      await getAllDiscount();
                                                    });
                                                  }
                                                },
                                                child: Column(
                                                  children: <Widget>[
                                                    Container(
                                                      margin: EdgeInsets.only(top: 10, bottom: 10),
                                                      child: Column(
                                                        children: [
                                                          Row(
                                                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                            children: <Widget>[
                                                              Container(
                                                                width: width / 5,
                                                                alignment: Alignment.centerLeft,
                                                                child: TextWidget(
                                                                    item.mode == "A" ? "Auto" : "Coupon",
                                                                    fontSize: textSizeSMedium,
                                                                    isCentered: true),
                                                              ),
                                                              Container(
                                                                width: width / 4,
                                                                alignment: Alignment.centerRight,
                                                                child: TextWidget(
                                                                    item.discountOn == "P"
                                                                        ? "${item.discount}%"
                                                                        : item.discount ?? "",
                                                                    textColor: primaryTextColor,
                                                                    fontSize: textSizeMedium,
                                                                    fontFamily: fontSemibold,
                                                                    isCentered: false,
                                                                    isLongText: true),
                                                              ),
                                                              Container(
                                                                width: width / 4,
                                                                alignment: Alignment.centerRight,
                                                                child: TextWidget(
                                                                    double.parse(item.minDiscount ?? "0.0")
                                                                        .toStringAsFixed(2),
                                                                    textColor: primaryTextColor,
                                                                    fontSize: textSizeMedium,
                                                                    fontFamily: fontSemibold),
                                                              ),
                                                              Container(
                                                                width: width / 4,
                                                                alignment: Alignment.centerRight,
                                                                child: TextWidget(
                                                                    item.maxDiscount == null ||
                                                                            item.maxDiscount == ""
                                                                        ? ""
                                                                        : double.parse(
                                                                                item.maxDiscount ?? "0.0")
                                                                            .toStringAsFixed(2),
                                                                    textColor: primaryTextColor,
                                                                    fontSize: textSizeMedium,
                                                                    fontFamily: fontSemibold),
                                                              ),
                                                            ],
                                                          ),
                                                          // Show coupon name if it's a coupon-based discount
                                                          if (item.mode == "C" &&
                                                              item.couponName != null &&
                                                              item.couponName!.isNotEmpty)
                                                            Padding(
                                                              padding: const EdgeInsets.only(top: 8.0),
                                                              child: Row(
                                                                children: [
                                                                  const Icon(
                                                                    Icons.local_offer,
                                                                    size: 16,
                                                                    color: Colors.blue,
                                                                  ),
                                                                  const SizedBox(width: 4),
                                                                  Expanded(
                                                                    child: Text(
                                                                      'Coupon: ${item.couponName}',
                                                                      style: const TextStyle(
                                                                        fontSize: 12,
                                                                        color: Colors.blue,
                                                                        fontWeight: FontWeight.w500,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          // Show discount type
                                                          if (item.type != null)
                                                            Padding(
                                                              padding: const EdgeInsets.only(top: 4.0),
                                                              child: Row(
                                                                children: [
                                                                  const Icon(
                                                                    Icons.category,
                                                                    size: 16,
                                                                    color: Colors.green,
                                                                  ),
                                                                  const SizedBox(width: 4),
                                                                  Text(
                                                                    'Type: ${item.type == "S" ? "Sales Wise" : "Product Wise"}',
                                                                    style: const TextStyle(
                                                                      fontSize: 12,
                                                                      color: Colors.green,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          // SingleChildScrollView(
                                                          //   scrollDirection:
                                                          //       Axis.horizontal,
                                                          //   child: Row(
                                                          //     mainAxisAlignment:
                                                          //         MainAxisAlignment
                                                          //             .spaceBetween,
                                                          //     children: <Widget>[
                                                          //       TextWidget(
                                                          //           "Formula: " +
                                                          //               item.formula,
                                                          //           fontSize:
                                                          //               textSizeSMedium,
                                                          //           isCentered:
                                                          //               false),
                                                          //       // Container(
                                                          //       //   width: width / 6,
                                                          //       //   alignment:
                                                          //       //       Alignment.center,
                                                          //       //   child:
                                                          //       //       item.discountActive ==
                                                          //       //               1
                                                          //       //           ? Icon(
                                                          //       //               Icons
                                                          //       //                   .check,
                                                          //       //               color: Colors
                                                          //       //                   .green,
                                                          //       //             )
                                                          //       //           : Icon(
                                                          //       //               Icons
                                                          //       //                   .remove,
                                                          //       //               color: Colors
                                                          //       //                   .red,
                                                          //       //             ),
                                                          //       // ),
                                                          //     ],
                                                          //   ),
                                                          // ),
                                                        ],
                                                      ),
                                                    ),
                                                    Divider(height: 0.5, color: t5ViewColor)
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        }),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateDiscount(),
            ),
          ).then((value) async {
            // Refresh the discount list after creating/updating
            await getAllDiscount();
          });
        },
        child: const Icon(Icons.add),
        tooltip: 'Create New Discount',
      ),
    );
  }
}
