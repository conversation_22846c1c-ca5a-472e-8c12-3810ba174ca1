import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

import '../../core/constants/app_colors.dart';
import '../../core/styles.dart';
import '../../widgets/radio_button.dart';
import 'profile_settings_controller.dart';

class ExpandableThemeSelector extends StatelessWidget {
  const ExpandableThemeSelector({super.key});

  void _showColorPicker(BuildContext context) {
    Color tempColor = ProfileController.themeColorNotifier.value;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Pick a Custom Color',
            style: black16w500,
          ),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: tempColor,
              onColorChanged: (color) {
                tempColor = color;
              },
              labelTypes: const [ColorLabelType.rgb, ColorLabelType.hsv, ColorLabelType.hsl],
              pickerAreaHeightPercent: 0.8,
            ),
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.pop(context),
            ),
            TextButton(
              child: const Text('Apply'),
              onPressed: () {
                ProfileController.changeTheme('Custom', tempColor);
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: Text(
        'Select Theme Color',
        style: black16w500,
      ),
      children: [
        ValueListenableBuilder<String>(
          valueListenable: ProfileController.themeOptionNotifier,
          builder: (context, selectedOption, child) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomRadioButton(
                  title: 'Yellow',
                  value: 'Yellow',
                  groupValue: selectedOption,
                  onChanged: (value) => ProfileController.changeTheme(value, AppColors.secondary),
                ),
                CustomRadioButton(
                  title: 'Green',
                  value: 'Green',
                  groupValue: selectedOption,
                  onChanged: (value) => ProfileController.changeTheme(value, AppColors.primary),
                ),
                CustomRadioButton(
                  title: 'Custom',
                  value: 'Custom',
                  groupValue: selectedOption,
                  onChanged: (value) {
                    ProfileController.changeTheme(value, null);
                    _showColorPicker(context);
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}
