import 'package:coffee_cofe/core/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../utils/cache_storeage_services.dart';

class ProfileController extends GetxController {
  // Use Get.find to access StorageService
  final StorageService _storageService = Get.put<StorageService>(StorageService());
  static final ValueNotifier<Color> themeColorNotifier = ValueNotifier(AppColors.primary);
  static final ValueNotifier<String> themeOptionNotifier = ValueNotifier('Green');

  static void changeTheme(String option, Color? color) {
    themeOptionNotifier.value = option;
    themeColorNotifier.value = color ?? themeColorNotifier.value;
  }

  RxString userName = ''.obs;
  RxString profilePath = ''.obs;
  RxBool isProfileOpen = false.obs;
  static var notificationEnabled = false.obs; // This should be Rx<bool>

  static void toggleNotification(bool value) {
    notificationEnabled.value = value;
  }

  @override
  void onInit() {
    super.onInit();
    _loadProfile();
  }

  void _loadProfile() {
    userName.value = _storageService.userName;
    profilePath.value = _storageService.profilePath;
  }

  Future<void> updateName(String newName) async {
    await _storageService.saveUserName(newName);
    userName.value = newName;
  }

  Future<void> updateProfilePath(String newPath) async {
    await _storageService.saveProfilePath(newPath);
    profilePath.value = newPath;
  }

  Future<void> pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      await updateProfilePath(pickedFile.path);
    }
  }

  void toggleProfile() => isProfileOpen.toggle();

  void closeProfile() => isProfileOpen.value = false;
}
