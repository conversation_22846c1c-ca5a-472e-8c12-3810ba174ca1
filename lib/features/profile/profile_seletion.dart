import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/styles.dart';
import 'profile_settings_controller.dart';

class ExpandableProfileSection extends StatelessWidget {
  final ProfileController controller = Get.find<ProfileController>();

  ExpandableProfileSection({super.key});

  void _showEditNameDialog(BuildContext context) {
    final TextEditingController nameController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Edit Name'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(hintText: 'Enter new name'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (nameController.text.isNotEmpty) {
                  controller.updateName(nameController.text);
                }
                Navigator.pop(context);
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return ListTile(
        title: Text(controller.userName.value, style: black16w500),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _showEditNameDialog(context),
      );
    });
  }
}
