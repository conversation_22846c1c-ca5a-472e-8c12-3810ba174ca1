import 'dart:io';

import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:coffee_cofe/widgets/common_drawer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/styles.dart';
import '../../database/app_database.dart';
import 'profile_seletion.dart';
import 'profile_settings_controller.dart';
import 'theme_selection_widget.dart';

class ProfileScreen extends StatelessWidget {
  ProfileScreen({super.key});

  final controller = Get.find<ProfileController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(title: 'Profile'),
      body: Column(
        children: [
          const SizedBox(height: 20),
          Column(
            children: [
              Stack(
                children: [
                  Obx(() {
                    final path = controller.profilePath.value;
                    return CircleAvatar(
                      radius: 50,
                      backgroundImage: path.startsWith('assets/')
                          ? AssetImage(path) as ImageProvider
                          : FileImage(File(path)),
                    );
                  }),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () => controller.pickImage(),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: const BoxDecoration(
                          color: Colors.blue,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(Icons.edit, color: Colors.white, size: 18),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Obx(() {
                return Text(controller.userName.value); // Reactive userName update
              }),
              const Text('Admin'),
            ],
          ),
          const SizedBox(height: 20),
          ExpandableProfileSection(),
          const ExpandableNotificationSettings(),
          ListTile(
            title: Text(
              'Support',
              style: black16w500,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {},
          ),
          const ExpandableThemeSelector(),
          GestureDetector(
            onTap: () => logout(context),
            child: const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Log out',
                style: TextStyle(color: Colors.red, fontSize: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> logout(BuildContext context) async {
    // 1. Clear SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    // 2. Clear entire SQLite database (delete from all tables)
    final db = await AppDatabase().database;

    // Get all table names except internal sqlite tables
    final tables =
        await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");

    final batch = db.batch();
    for (var table in tables) {
      final tableName = table['name'] as String;
      batch.delete(tableName);
    }
    await batch.commit(noResult: true);

    // 3. Navigate to login screen and remove all backstack
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }
}

class ExpandableNotificationSettings extends StatelessWidget {
  const ExpandableNotificationSettings({super.key});

  @override
  Widget build(BuildContext context) {
    // Wrapping only the parts that depend on reactive variables with Obx
    return Obx(() {
      return ExpansionTile(
        title: Text(
          'Notification Settings',
          style: black16w500,
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        children: [
          SwitchListTile(
            title: Text(
              ProfileController.notificationEnabled.value ? 'Enabled' : 'Disabled',
              style: black16w500,
            ),
            value: ProfileController.notificationEnabled.value,
            onChanged: (value) {
              ProfileController.toggleNotification(value);
            },
          ),
        ],
      );
    });
  }
}
