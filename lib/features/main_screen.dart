import 'package:coffee_cofe/features/profile/profile_settings_controller.dart';
import 'package:flutter/material.dart';
import 'home/home_screen.dart';
import 'settings/settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with SingleTickerProviderStateMixin {
  int _selectedIndex = 1;
  late AnimationController _animationController;

  final List<Widget> _pages = [const HomeScreen(), const SettingsScreen()];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    )..forward(); // Ensure it starts without errors
  }

  void _onItemTapped(int index) {
    _animationController.forward(from: 0.0);
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
            backgroundColor: const Color(0xFFF1F8E9), // Light Green Tint
            body: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return FadeTransition(opacity: animation, child: child);
              },
              child: _pages[_selectedIndex],
            ),
            bottomNavigationBar: BottomNavigationBar(
              currentIndex: _selectedIndex,
              onTap: _onItemTapped,
              selectedItemColor: Colors.white, // Green Theme
              unselectedItemColor: Colors.black,
              backgroundColor: bgColor,
              type: BottomNavigationBarType.fixed,
              showSelectedLabels: true,
              showUnselectedLabels: true,
              elevation: 10,
              items: const [
                BottomNavigationBarItem(icon: Icon(Icons.paste), label: "Reports"),
                BottomNavigationBarItem(icon: Icon(Icons.receipt_long), label: "Dashboard"),
                BottomNavigationBarItem(icon: Icon(Icons.settings), label: "Settings"),
              ],
            ),
          );
        });
  }
}
