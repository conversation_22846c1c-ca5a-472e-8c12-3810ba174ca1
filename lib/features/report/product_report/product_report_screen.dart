import 'package:coffee_cofe/core/styles.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../widgets/common_drawer.dart';
import '../../../widgets/common_dropdown.dart';
import 'product_report_provider.dart';

class ProductReportScreen extends StatefulWidget {
  const ProductReportScreen({super.key});

  @override
  State<ProductReportScreen> createState() => _ProductReportScreenState();
}

class _ProductReportScreenState extends State<ProductReportScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      initFunction();
    });
    // _loadTransactions();
  }

  initFunction() async {
    final reportProvider = Provider.of<ProductReportProvider>(context, listen: false);
    await reportProvider.initializeDAO();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(title: 'Product Report'),
      body: Consumer<ProductReportProvider>(
        builder: (_, productProvider, __) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                CommonDropdown(
                  enableMargine: false,
                  items: productProvider.dropDownProducts,
                  selectedItem: productProvider.selectedProductForFilter,
                  onChanged: (val) async {
                    if (val != null) {
                      await productProvider.onChageProductFilter(val);
                    }
                  },
                ),

                // ListView inside Expanded to take available space
                Expanded(
                  child: ListView.builder(
                    itemCount: productProvider.groupedProductReports.length,
                    itemBuilder: (context, index) {
                      final item = productProvider.groupedProductReports[index];
                      return Card(
                        color: Colors.white,
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 4.0),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                          child: Row(
                            children: [
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(item.productName, style: black16w500),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${item.totalQty}',
                                        textAlign: TextAlign.center,
                                        style: black16w500,
                                      ),
                                      Text(
                                        '  *  ',
                                        style: black16w500,
                                      ),
                                      Text(
                                        ' ${item.unitPrice}',
                                        textAlign: TextAlign.center,
                                        style: black16w500,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              Spacer(),
                              Text('₹${item.totalAmount.toStringAsFixed(2)}',
                                  textAlign: TextAlign.center, style: black18w700),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Totals section
                Card(
                  elevation: 3,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text('Total Qty: ${productProvider.totalQty}', style: black16w500),
                        Text(
                          'Total Amount: ₹${productProvider.totalAmount.toStringAsFixed(2)}',
                          style: black18w700,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
