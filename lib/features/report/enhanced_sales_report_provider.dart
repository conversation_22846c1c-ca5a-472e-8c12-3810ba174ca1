import 'package:flutter/material.dart';
import '../../database/dao/sales_transation_dao.dart';
import '../../database/entities/sales_dto.dart';
import '../../database/entities/sales_transaction_dto.dart';
import '../../database/dao/sales_dao.dart';
import '../../database/app_database.dart';

class EnhancedSalesReportProvider extends ChangeNotifier {
  List<SalesDto> _sales = [];
  List<SalesTransactionDto> _transactions = [];
  SalesDao? _salesDao;
  SalesTransactionDao? _salesTransactionDao;

  DateTime? fromDate;
  DateTime? toDate;
  List<SalesDto> filteredSales = [];
  List<SalesTransactionDto> filteredTransactions = [];

  // Tax summary data
  Map<String, dynamic> _taxSummary = {};
  List<Map<String, dynamic>> _gstBreakdown = [];
  Map<String, double> _taxRateWiseSales = {};

  // Getters
  List<SalesDto> get sales => _sales;
  List<SalesTransactionDto> get transactions => _transactions;
  Map<String, dynamic> get taxSummary => _taxSummary;
  List<Map<String, dynamic>> get gstBreakdown => _gstBreakdown;
  Map<String, double> get taxRateWiseSales => _taxRateWiseSales;

  /// Initialize DAOs
  Future<void> initializeDAOs() async {
    _salesDao = await AppDatabase().salesDao;
    _salesTransactionDao = await AppDatabase().salesTransactionDao;
  }

  /// Load all sales and transactions
  Future<void> loadAllData() async {
    await initializeDAOs();

    final salesResult = await _salesDao!.getAllSales();
    final transactionsResult = await _salesTransactionDao!.getAllTransactions();

    _sales = salesResult;
    _transactions = transactionsResult;

    _calculateTaxSummary();
    notifyListeners();
  }

  /// Get sales and transactions between dates
  Future<void> getDataBetweenDates(DateTime from, DateTime to) async {
    await initializeDAOs();

    fromDate = from;
    toDate = to;

    // Format dates for database query
    final fromStr = from.toIso8601String().split('T')[0];
    final toStr = to.toIso8601String().split('T')[0];

    // Get filtered sales
    filteredSales = await _salesDao!.getSalesBetweenDates(fromStr, toStr);

    // Get filtered transactions
    filteredTransactions = await _salesTransactionDao!.getTransactionsBetweenDates(from, to);

    _calculateFilteredTaxSummary();
    notifyListeners();
  }

  /// Calculate comprehensive tax summary
  void _calculateTaxSummary() {
    _calculateTaxSummaryForData(_sales, _transactions);
  }

  /// Calculate tax summary for filtered data
  void _calculateFilteredTaxSummary() {
    _calculateTaxSummaryForData(filteredSales, filteredTransactions);
  }

  /// Calculate tax summary for given data
  void _calculateTaxSummaryForData(List<SalesDto> salesData, List<SalesTransactionDto> transactionData) {
    double totalSales = 0;
    double totalSubtotal = 0;
    double totalDiscount = 0;
    double totalParcel = 0;
    double totalCGST = 0;
    double totalSGST = 0;
    double totalIGST = 0;
    double totalCess = 0;
    double totalTax = 0;

    // Tax rate wise breakdown
    Map<double, Map<String, dynamic>> taxRateBreakdown = {};
    _taxRateWiseSales.clear();

    // Process sales data
    for (final sale in salesData) {
      totalSales += sale.totalAmount;
      totalSubtotal += sale.subtotalAmount ?? 0;
      totalDiscount += sale.discountAmount ?? 0;
      totalParcel += sale.parcelAmount ?? 0;
      totalCGST += sale.cgstAmount ?? 0;
      totalSGST += sale.sgstAmount ?? 0;
      totalIGST += sale.igstAmount ?? 0;
      totalCess += sale.cessAmount ?? 0;
      totalTax += sale.totalTaxAmount ?? 0;
    }

    // Process transaction data for detailed breakdown
    for (final transaction in transactionData) {
      final gstRate = transaction.gstRate ?? 0;
      final amount = transaction.productAmount ?? 0;
      final cgst = transaction.cgst ?? 0;
      final sgst = transaction.sgst ?? 0;
      final igst = transaction.igst ?? 0;
      final cess = transaction.cess ?? 0;

      if (!taxRateBreakdown.containsKey(gstRate)) {
        taxRateBreakdown[gstRate] = {
          'rate': gstRate,
          'taxableAmount': 0.0,
          'cgst': 0.0,
          'sgst': 0.0,
          'igst': 0.0,
          'cess': 0.0,
          'totalTax': 0.0,
          'itemCount': 0,
        };
      }

      taxRateBreakdown[gstRate]!['taxableAmount'] += amount;
      taxRateBreakdown[gstRate]!['cgst'] += cgst;
      taxRateBreakdown[gstRate]!['sgst'] += sgst;
      taxRateBreakdown[gstRate]!['igst'] += igst;
      taxRateBreakdown[gstRate]!['cess'] += cess;
      taxRateBreakdown[gstRate]!['totalTax'] += cgst + sgst + igst + cess;
      taxRateBreakdown[gstRate]!['itemCount']++;

      // Tax rate wise sales
      final gstRateKey = gstRate.toString();
      _taxRateWiseSales[gstRateKey] = (_taxRateWiseSales[gstRateKey] ?? 0) + amount;
    }

    // Update tax summary
    _taxSummary = {
      'totalSales': totalSales,
      'totalSubtotal': totalSubtotal,
      'totalDiscount': totalDiscount,
      'totalParcel': totalParcel,
      'totalCGST': totalCGST,
      'totalSGST': totalSGST,
      'totalIGST': totalIGST,
      'totalCess': totalCess,
      'totalTax': totalTax,
      'salesCount': salesData.length,
      'transactionCount': transactionData.length,
      'averageOrderValue': salesData.isNotEmpty ? totalSales / salesData.length : 0,
      'taxPercentage': totalSubtotal > 0 ? (totalTax / totalSubtotal) * 100 : 0,
    };

    // Update GST breakdown
    _gstBreakdown = taxRateBreakdown.values.toList()
      ..sort((a, b) => (a['rate'] as double).compareTo(b['rate'] as double));
  }

  /// Get sales summary by date
  Map<String, Map<String, dynamic>> getSalesByDate() {
    Map<String, Map<String, dynamic>> dateWiseSales = {};

    final dataToProcess = filteredSales.isNotEmpty ? filteredSales : _sales;

    for (final sale in dataToProcess) {
      final date = sale.createdDate.split('T')[0]; // Extract date part

      if (!dateWiseSales.containsKey(date)) {
        dateWiseSales[date] = {
          'date': date,
          'totalSales': 0.0,
          'totalTax': 0.0,
          'salesCount': 0,
          'avgOrderValue': 0.0,
        };
      }

      dateWiseSales[date]!['totalSales'] += sale.totalAmount;
      dateWiseSales[date]!['totalTax'] += sale.totalTaxAmount ?? 0;
      dateWiseSales[date]!['salesCount']++;
    }

    // Calculate average order value
    dateWiseSales.forEach((date, data) {
      data['avgOrderValue'] = data['totalSales'] / data['salesCount'];
    });

    return dateWiseSales;
  }

  /// Get top products by sales
  List<Map<String, dynamic>> getTopProductsBySales({int limit = 10}) {
    Map<String, Map<String, dynamic>> productSales = {};

    final dataToProcess = filteredTransactions.isNotEmpty ? filteredTransactions : _transactions;

    for (final transaction in dataToProcess) {
      final productId = transaction.productId ?? 'unknown';
      final productName = transaction.productName ?? 'Unknown Product';
      final amount = transaction.productAmount ?? 0;
      final qty = transaction.productQty ?? 0;

      if (!productSales.containsKey(productId)) {
        productSales[productId] = {
          'productId': productId,
          'productName': productName,
          'totalSales': 0.0,
          'totalQty': 0,
          'totalTax': 0.0,
          'avgPrice': 0.0,
        };
      }

      productSales[productId]!['totalSales'] += amount;
      productSales[productId]!['totalQty'] += qty;
      productSales[productId]!['totalTax'] += (transaction.totalTaxAmount ?? 0);
    }

    // Calculate average price and sort by sales
    final sortedProducts = productSales.values.toList();
    for (final product in sortedProducts) {
      product['avgPrice'] = product['totalQty'] > 0 ? product['totalSales'] / product['totalQty'] : 0.0;
    }

    sortedProducts.sort((a, b) => (b['totalSales'] as double).compareTo(a['totalSales'] as double));

    return sortedProducts.take(limit).toList();
  }

  /// Get tax compliance summary
  Map<String, dynamic> getTaxComplianceSummary() {
    final dataToProcess = filteredTransactions.isNotEmpty ? filteredTransactions : _transactions;

    int totalItems = dataToProcess.length;
    int taxedItems = dataToProcess.where((t) => (t.gstRate ?? 0) > 0).length;
    int customTaxItems = dataToProcess.where((t) => t.customTaxApplied == true).length;

    return {
      'totalItems': totalItems,
      'taxedItems': taxedItems,
      'taxFreeItems': totalItems - taxedItems,
      'customTaxItems': customTaxItems,
      'taxComplianceRate': totalItems > 0 ? (taxedItems / totalItems) * 100 : 0,
      'customTaxRate': totalItems > 0 ? (customTaxItems / totalItems) * 100 : 0,
    };
  }

  /// Export tax report data
  Map<String, dynamic> getExportData() {
    return {
      'reportGenerated': DateTime.now().toIso8601String(),
      'dateRange': {
        'from': fromDate?.toIso8601String(),
        'to': toDate?.toIso8601String(),
      },
      'taxSummary': _taxSummary,
      'gstBreakdown': _gstBreakdown,
      'taxRateWiseSales': _taxRateWiseSales,
      'salesByDate': getSalesByDate(),
      'topProducts': getTopProductsBySales(),
      'complianceSummary': getTaxComplianceSummary(),
    };
  }

  /// Clear filtered data
  void clearFilters() {
    fromDate = null;
    toDate = null;
    filteredSales.clear();
    filteredTransactions.clear();
    _calculateTaxSummary();
    notifyListeners();
  }
}
