import 'dart:developer';
import 'package:flutter/material.dart';

import '../../../database/app_database.dart';
import '../../../database/dao/sales_transation_dao.dart';
import '../../../database/entities/sales_transaction_dto.dart';

class SalesReportProvider extends ChangeNotifier {
  List<SalesTransactionDto> transactions = [];
  SalesTransactionDao? salesTransactionDao;

  DateTime? fromDate;
  DateTime? toDate;
  List<SalesTransactionDto> filteredTransactions = [];

  // Load all transactions from the database
  Future<void> loadTransactions() async {
    salesTransactionDao = await AppDatabase().salesTransactionDao;

    // List<SalesTransactionDto> transactions = await salesTransactionDao!.getAllTransactions();
    final result = await salesTransactionDao!.getAllTransactions();
    transactions = result; // ✅ correctly assign to the class property
    notifyListeners();
  }

  Future<void> getTransactionsBetweenDates(DateTime from, DateTime to) async {
    try {
      salesTransactionDao ??= await AppDatabase().salesTransactionDao;

      fromDate = from;
      toDate = to;

      // Get transactions between the specified dates
      final result = await salesTransactionDao!.getTransactionsBetweenDates(from, to);
      filteredTransactions = result;

      log('Found ${filteredTransactions.length} transactions between ${from.toIso8601String()} and ${to.toIso8601String()}');
      notifyListeners();
    } catch (e) {
      log('Error getting transactions between dates: $e');
      // Fallback to all transactions if date filtering fails
      final result = await salesTransactionDao!.getAllTransactions();
      filteredTransactions = result;
      notifyListeners();
    }
  }
}
