import 'package:coffee_cofe/features/report/sales_report/report_provider.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../widgets/common_drawer.dart';
import '../../product/product_provider.dart';
import '../../profile/profile_settings_controller.dart';

class SalesReportScreen extends StatefulWidget {
  const SalesReportScreen({super.key});

  @override
  State<SalesReportScreen> createState() => _SalesReportScreenState();
}

class _SalesReportScreenState extends State<SalesReportScreen> {
  // List<SalesTransactionDto> reportProvider.transactions = [];
  // SalesTransactionDao? _salesTransactionDao;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      initFunction();
    });
    // _loadTransactions();
  }

  initFunction() {
    final reportProvider = Provider.of<SalesReportProvider>(context, listen: false);
    reportProvider.loadTransactions();
  }

  @override
  Widget build(BuildContext context) {
    final productProvider = Provider.of<ProductProvider>(context);
    final reportProvider = Provider.of<SalesReportProvider>(
      context,
    );

    // Calculate total amount
    double totalAmount = reportProvider.transactions.fold(0, (sum, transaction) {
      return sum + (transaction.productAmount ?? 0);
    });

    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
            drawer: const CommonDrawer(),
            appBar: const CommonAppBar(title: 'Sales Transaction Report'),
            body: Padding(
              padding: const EdgeInsets.all(0.0),
              child: DefaultTabController(
                length: 2,
                child: Column(
                  children: [
                    // TabBar for two tabs
                    Container(
                      color: bgColor.withOpacity(0.2),
                      child: TabBar(
                        labelColor: bgColor,
                        unselectedLabelColor: Colors.black,
                        indicatorColor: bgColor,
                        dividerColor: Colors.white,
                        tabs: const [
                          Tab(text: 'Two days report'),
                          Tab(text: 'Date to date report'),
                        ],
                      ),
                    ),
                    // TabBarView for content of each tab
                    Expanded(
                      child: TabBarView(
                        children: [
                          // Completed Transactions Tab
                          lastTwoDaysTransactionWidget(productProvider, reportProvider),
                          // Pending Transactions Tab
                          dateRangeFilterWidget(productProvider, reportProvider, bgColor),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Divider(),
                    ),
                    // Display total amount
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Total Amount',
                            style: TextStyle(fontSize: 25, fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '₹${totalAmount.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 25,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  lastTwoDaysTransactionWidget(final productProvider, final reportProvider) {
    return reportProvider.transactions.isEmpty
        ? const Center(child: CircularProgressIndicator()) // Show loading spinner while fetching data
        : Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8.0),
            child: ListView.builder(
              itemCount: reportProvider.transactions.length,
              itemBuilder: (context, index) {
                final transaction = reportProvider.transactions[index];
                final product = productProvider.products.firstWhere(
                  (prod) => prod.productId == transaction.productId,
                );

                return Card(
                  color: Colors.white70,
                  elevation: 1,
                  margin: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ListTile(
                    title: Text(product.productName ?? '-'),
                    subtitle: Text('Qty: ${transaction.productQty} | Amount: ₹${transaction.productAmount}'),
                    trailing: Text(
                      'Status: ${transaction.status == 1 ? 'Completed' : 'Pending'}',
                      style: TextStyle(
                        color: transaction.status == 1 ? Colors.green : Colors.red,
                      ),
                    ),
                  ),
                );
              },
            ),
          );
  }

  Widget dateRangeFilterWidget(final productProvider, SalesReportProvider reportProvider, Color bgcolor) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: reportProvider.fromDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (picked != null) {
                      setState(() {
                        reportProvider.fromDate = picked;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    height: 45,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: bgcolor),
                    child: Text(
                        reportProvider.fromDate == null
                            ? 'Select From Date'
                            : 'From: ${reportProvider.fromDate!.toLocal().toString().split(' ')[0]}',
                        style:
                            const TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.w600)),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: InkWell(
                  onTap: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: reportProvider.toDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (picked != null) {
                      setState(() {
                        reportProvider.toDate = picked;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    height: 45,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: bgcolor),
                    child: Text(
                        reportProvider.toDate == null
                            ? 'Select To Date'
                            : 'To: ${reportProvider.toDate!.toLocal().toString().split(' ')[0]}',
                        style:
                            const TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.w600)),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          InkWell(
            onTap: () async {
              if (reportProvider.fromDate != null && reportProvider.toDate != null) {
                await reportProvider.getTransactionsBetweenDates(
                    reportProvider.fromDate!, reportProvider.toDate!);
                setState(() {});
              }
            },
            child: Container(
              padding: const EdgeInsets.all(8.0),
              height: 45,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.0),
                  color: Colors.white,
                  border: Border.all(color: bgcolor)),
              child:
                  Text('Submit', style: TextStyle(fontSize: 14, color: bgcolor, fontWeight: FontWeight.w600)),
            ),
          ),
          // ElevatedButton(
          //   onPressed: () async {
          //     if (reportProvider.fromDate != null && reportProvider.toDate != null) {
          //       await reportProvider.getTransactionsBetweenDates(reportProvider.fromDate!, reportProvider.toDate!);
          //       setState(() {});
          //     }
          //   },
          //   child: const Text('Submit'),
          // ),
          const SizedBox(height: 10),
          // Summary Card
          if (reportProvider.filteredTransactions.isNotEmpty)
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Summary',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Total Transactions: ${reportProvider.filteredTransactions.length}'),
                        Text(
                            'Total Amount: ₹${reportProvider.filteredTransactions.fold<double>(0, (sum, t) => sum + (t.productAmount ?? 0)).toStringAsFixed(2)}'),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          const SizedBox(height: 10),
          Expanded(
            child: reportProvider.filteredTransactions.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.receipt_long, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text("No transactions found", style: TextStyle(fontSize: 18, color: Colors.grey)),
                        SizedBox(height: 8),
                        Text("Try selecting different dates",
                            style: TextStyle(fontSize: 14, color: Colors.grey)),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: reportProvider.filteredTransactions.length,
                    itemBuilder: (context, index) {
                      final transaction = reportProvider.filteredTransactions[index];

                      // Safely find the product, handle case where product might not exist
                      dynamic product;
                      try {
                        product = productProvider.products.firstWhere(
                          (prod) => prod.productId == transaction.productId,
                        );
                      } catch (e) {
                        // Product not found, set to null
                        product = null;
                      }

                      final productName =
                          product?.productName ?? transaction.productName ?? 'Unknown Product';
                      final createdDate = transaction.createdAt != null
                          ? DateTime.parse(transaction.createdAt!).toLocal().toString().split(' ')[0]
                          : 'Unknown Date';

                      return Card(
                        color: Colors.white70,
                        elevation: 1,
                        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: ListTile(
                          title: Text(
                            productName,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                  'Qty: ${transaction.productQty} | Amount: ₹${transaction.productAmount?.toStringAsFixed(2) ?? '0.00'}'),
                              Text('Date: $createdDate',
                                  style: const TextStyle(fontSize: 12, color: Colors.grey)),
                            ],
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: transaction.status == 1 ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              transaction.status == 1 ? 'Completed' : 'Pending',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          )
        ],
      ),
    );
  }
}
