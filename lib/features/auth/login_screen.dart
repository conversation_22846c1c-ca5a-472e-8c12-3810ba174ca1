import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import '../../core/styles.dart';
import '../../core/routes/app_routes.dart';
import '../../widgets/input_form_field.dart';
import '../../widgets/common_button.dart';
import 'login_provider.dart';
import 'mock_users_demo.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    Provider.of<LoginProvider>(context, listen: false).disposeControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
          child: Consumer<LoginProvider>(
            builder: (context, provider, __) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Spacer(),
                  const Center(
                    child: Text('Login', style: TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
                  ),
                  const SizedBox(height: 8),
                  const Center(
                    child: Text(
                      'Please login to continue',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 40),

                  /// Mobile Field
                  buildFormRow(
                    "Mobile or User ID",
                    InputFormField(
                      controller: provider.mobileController,
                      hint: "Enter your mobile number",
                      inputType: TextInputType.phone,
                      validator: (value) => value == null || value.isEmpty ? 'Mobile is required' : null,
                    ),
                  ),
                  const SizedBox(height: 20),

                  /// Password Field
                  buildFormRow(
                    "Password",
                    InputFormField(
                      controller: provider.passwordController,
                      hint: "Enter your password",
                      inputType: TextInputType.text,
                      isPassword: true,
                      validator: (value) => value == null || value.isEmpty ? 'Password is required' : null,
                      suffixIcon: IconButton(
                        icon: Icon(
                          provider.isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                          color: Colors.grey,
                        ),
                        onPressed: provider.togglePasswordVisibility,
                      ),
                      enableSuggestions: false,
                    ),
                  ),
                  const SizedBox(height: 10),

                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: () {},
                      child: const Text('Forget password ?', style: TextStyle(color: Colors.grey)),
                    ),
                  ),

                  const SizedBox(height: 10),

                  /// Sign In Button
                  CommonButton(
                      title: provider.isLoading ? 'Signing in...' : 'Sign In',
                      isLoading: provider.isLoading,
                      onTap: () async {
                        if (_formKey.currentState!.validate()) {
                          await provider.login();
                        }
                      }),
                  const SizedBox(height: 20),

                  /// Sign Up Prompt
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text("Don't have account? "),
                      GestureDetector(
                        onTap: () => Get.toNamed(AppRoutes.signup),
                        child: const Text(
                          'Sign Up',
                          style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),

                  // Show mock users demo in development mode
                  const MockUsersDemo(),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget buildFormRow(String label, Widget child) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: black16w500),
          const SizedBox(height: 6),
          child,
        ],
      ),
    );
  }
}
