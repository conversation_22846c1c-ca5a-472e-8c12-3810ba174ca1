import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/routes/app_routes.dart';
import '../../../models/user_model.dart';
import '../../../repositories/auth_repository.dart';
import '../../../utils/user_services.dart';
import '../../../utils/workspace_functions.dart';

class SignupProvider with ChangeNotifier {
  final AuthRepository _authRepository = AuthRepository();
  final WorkspaceFunctions _workspace = WorkspaceFunctions();

  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController userNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;

  bool get isPasswordVisible => _isPasswordVisible;
  bool get isConfirmPasswordVisible => _isConfirmPasswordVisible;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  void togglePasswordVisibility() {
    _isPasswordVisible = !_isPasswordVisible;
    notifyListeners();
  }

  void toggleConfirmPasswordVisibility() {
    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<bool> register() async {
    final fullName = fullNameController.text.trim();
    final email = emailController.text.trim();
    final password = passwordController.text.trim();
    final confirmPassword = confirmPasswordController.text.trim();

    // Validation
    if (fullName.isEmpty || email.isEmpty || password.isEmpty) {
      _setError('Please fill in all fields');
      return false;
    }

    if (password != confirmPassword) {
      _setError('Passwords do not match');
      return false;
    }

    if (password.length < 6) {
      _setError('Password must be at least 6 characters');
      return false;
    }

    if (!GetUtils.isEmail(email)) {
      _setError('Please enter a valid email address');
      return false;
    }

    _setLoading(true);
    _setError(null);

    try {
      final response = await _authRepository.register(
        userName: fullName,
        email: email,
        password: password,
        phoneNumber: '1234567890', // Default or get from form
        role: 'user',
      );

      if (response.success && response.data?.response != null) {
        final user = response.data!.response!;

        // Save user session
        await SessionManager.saveUserSession({
          'userId': user.userId ?? '',
          'name': user.userName ?? '',
          'mobile': user.mobileNo ?? '',
          'email': user.emailAddress ?? email,
          'token': user.token ?? '',
          'workspaceId': user.workspaceId ?? '',
        });

        // Create workspace for new user using workspace ID from signup response
        await _workspace.workSpaceCreationNewUser(Workspace(
          userRole: user.role ?? '1',
          workspaceId: user.workspaceId ?? 'default_workspace',
          workspaceName: user.workspaceName ?? 'Default Workspace',
          workspaceUserId: user.userId ?? '',
          workspaceUserName: user.userName ?? '',
        ));

        Get.snackbar(
          'Registration Success',
          'Welcome ${user.userName ?? 'User'}! Your account has been created.',
          backgroundColor: Colors.greenAccent,
          colorText: Colors.black,
        );

        Get.offAllNamed(AppRoutes.billingScreen);
        return true;
      } else {
        _setError(response.message);
        Get.snackbar(
          'Registration Failed',
          response.message,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      Get.snackbar(
        'Registration Failed',
        'An error occurred. Please try again.',
        backgroundColor: Colors.redAccent,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void disposeControllers() {
    fullNameController.dispose();
    userNameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
  }

  @override
  void dispose() {
    disposeControllers();
    super.dispose();
  }
}
