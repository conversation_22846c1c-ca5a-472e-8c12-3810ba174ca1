import 'package:coffee_cofe/widgets/common_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/styles.dart';
import '../../../widgets/input_form_field.dart';
import 'signup_provider.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
          child: SingleChildScrollView(
            child: Consumer<SignupProvider>(
              builder: (_, provider, __) {
                return Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 24),
                      const Center(
                        child: Text(
                          'Register',
                          style: TextStyle(
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Center(
                        child: Text(
                          'Please register to login',
                          style: TextStyle(color: Colors.black54),
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Full name
                      buildFormRow(
                        "Full Name ",
                        InputFormField(
                          inputType: TextInputType.text,
                          hint: "'Enter your full name",
                          controller: provider.fullNameController,
                        ),
                      ),
                      // User name
                      const SizedBox(height: 16),

                      buildFormRow(
                        "User Name",
                        InputFormField(
                          inputType: TextInputType.phone,
                          hint: "Enter your number",
                          controller: provider.userNameController,
                        ),
                      ),
                      // Email
                      const SizedBox(height: 16),

                      buildFormRow(
                        "Email",
                        InputFormField(
                          inputType: TextInputType.emailAddress,
                          hint: "Enter your Email",
                          controller: provider.emailController,
                        ),
                      ),
                      // Password
                      const SizedBox(height: 16),

                      buildFormRow(
                        "Password",
                        InputFormField(
                            inputType: TextInputType.emailAddress,
                            hint: "Enter your Password",
                            controller: provider.passwordController,
                            isPassword: true),
                      ),
                      // Confirm password
                      const SizedBox(height: 16),

                      buildFormRow(
                        "Confirm passwor",
                        InputFormField(
                            inputType: TextInputType.emailAddress,
                            hint: "Enter your Confirm passwor",
                            controller: provider.confirmPasswordController,
                            isPassword: true),
                      ),
                      const SizedBox(height: 24),

                      // Sign Up Button

                      CommonButton(
                        title: provider.isLoading ? "Signing Up..." : "Sign Up",
                        isLoading: provider.isLoading,
                        onTap: () async {
                          if (_formKey.currentState!.validate()) {
                            await provider.register();
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Bottom Text
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text("Already have account? "),
                          GestureDetector(
                            onTap: () {
                              Get.toNamed(AppRoutes.login);
                            },
                            child: const Text(
                              'Sign In',
                              style: TextStyle(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

Widget buildFormRow(String label, Widget child) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 10.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: black16w500),
        const SizedBox(height: 6),
        child,
      ],
    ),
  );
}
