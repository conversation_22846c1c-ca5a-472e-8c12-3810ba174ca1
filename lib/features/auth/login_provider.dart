import 'package:coffee_cofe/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/routes/app_routes.dart';
import '../../repositories/auth_repository.dart';
import '../../utils/user_services.dart';
import '../../utils/workspace_functions.dart';

class LoginProvider with ChangeNotifier {
  final AuthRepository _authRepository = AuthRepository();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final WorkspaceFunctions workspace = WorkspaceFunctions();

  bool _isPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;
  User? _currentUser;

  bool get isPasswordVisible => _isPasswordVisible;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  User? get currentUser => _currentUser;

  void togglePasswordVisibility() {
    _isPasswordVisible = !_isPasswordVisible;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Fill login form with demo credentials
  void fillDemoCredentials(String identifier, String password) {
    mobileController.text = identifier;
    passwordController.text = password;
    clearError(); // Clear any previous errors
    notifyListeners();
  }

  /// Clear login form
  void clearForm() {
    mobileController.clear();
    passwordController.clear();
    clearError();
    notifyListeners();
  }

  void disposeControllers() {
    mobileController.dispose();
    passwordController.dispose();
  }

  Future<bool> login() async {
    final mobile = mobileController.text.trim();
    final password = passwordController.text.trim();

    if (mobile.isEmpty || password.isEmpty) {
      _setError('Please fill in all fields');
      return false;
    }

    _setLoading(true);
    _setError(null);

    try {
      final response = await _authRepository.login(
        identifier: mobile,
        password: password,
      );

      if (response.success && response.data?.response != null) {
        _currentUser = response.data!.response!;

        // Save user session using existing SessionManager
        await SessionManager.saveUserSession({
          'userId': _currentUser!.userId ?? '',
          'name': _currentUser!.userName ?? '',
          'mobile': _currentUser!.mobileNo ?? mobile,
          'email': _currentUser!.emailAddress ?? '',
          'token': _currentUser!.token ?? '',
          'workspaceId': _currentUser!.workspaceId ?? '',
        });

        // Create workspace for new user using workspace ID from login response
        await workspace.workSpaceCreationNewUser(Workspace(
          userRole: _currentUser!.role ?? '1',
          workspaceId: _currentUser!.workspaceId ?? 'default_workspace',
          workspaceName: _currentUser!.workspaceName ?? 'Default Workspace',
          workspaceUserId: _currentUser!.userId ?? '',
          workspaceUserName: _currentUser!.userName ?? '',
        ));

        Get.snackbar(
          'Login Success',
          'Welcome ${_currentUser!.userName ?? 'User'}',
          backgroundColor: Colors.greenAccent,
          colorText: Colors.black,
        );

        Get.offAllNamed(AppRoutes.billingScreen);
        return true;
      } else {
        _setError(response.message);
        Get.snackbar(
          'Login Failed',
          response.message,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      Get.snackbar(
        'Login Failed',
        'An error occurred. Please try again.',
        backgroundColor: Colors.redAccent,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _authRepository.logout();
      await SessionManager.clearUserSession();

      _currentUser = null;
      mobileController.clear();
      passwordController.clear();

      Get.snackbar(
        'Logout Success',
        'You have been logged out successfully',
        backgroundColor: Colors.greenAccent,
        colorText: Colors.black,
      );

      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      Get.snackbar(
        'Logout',
        'Logged out successfully',
        backgroundColor: Colors.greenAccent,
        colorText: Colors.black,
      );
      Get.offAllNamed(AppRoutes.login);
    } finally {
      _setLoading(false);
    }
  }
}
