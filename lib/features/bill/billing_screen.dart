import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'package:coffee_cofe/core/routes/app_routes.dart';
import 'package:coffee_cofe/core/styles.dart';
import 'package:coffee_cofe/database/entities/product.dart';
import 'package:coffee_cofe/features/product/product_provider.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:coffee_cofe/widgets/common_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pie_menu/pie_menu.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/theme.dart';
import '../../database/entities/sales_transaction_dto.dart';
// import '../../widgets/sales_popup_menu_items.dart';
import '../../widgets/common_drawer.dart';
import '../../widgets/pie_popup_menu_widget.dart';
import '../profile/profile_settings_controller.dart';
import '../settings/settings_provider.dart';
import 'billing_screen_provider.dart';
import 'bottom_sheet.dart';

class BillingScreen extends StatefulWidget {
  const BillingScreen({super.key});

  @override
  State<BillingScreen> createState() => _BillingScreenState();
}

class _BillingScreenState extends State<BillingScreen> {
  List<ProductDto> _products = [];
  List<ProductDto> get products => _products;

  double screenWidth = 0.0;
  double screenHeight = 0.0;
  bool isTabScreen = false;

  @override
  void initState() {
    super.initState();
    initFunction();
  }

  initFunction() async {
    final provi = Provider.of<ProductProvider>(context, listen: false);
    await provi.initFunction();
    setState(() {});
  }

  dynamic wKey;
  @override
  Widget build(BuildContext context) {
    screenHeight = MediaQuery.of(context).size.height;
    screenWidth = MediaQuery.of(context).size.width;
    isTabScreen = screenWidth > 600;
    final productProvider = Provider.of<ProductProvider>(context);
    final salesProvider = Provider.of<BillingScreenProvider>(context);
    _products = productProvider.products;
    wKey = math.Random().hashCode;
    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return PieCanvas(
            key: Key("$wKey"),
            theme: AppTheme.defaultPieTheme,
            onMenuToggle: (bool menuOpen) {
              log('PieMenu is ${menuOpen ? 'opened' : 'closed'}');
            },
            child: Scaffold(
              backgroundColor: Colors.white,
              drawer: const CommonDrawer(),
              appBar: CommonAppBar(
                appBarColor: bgColor,
                title: '',
              ),
              body: isTabScreen
                  ? Row(
                      children: [
                        productsWidget(bgColor),
                        const SizedBox(
                          width: 2,
                        ),
                        billingItemsWidget(salesProvider, bgColor),
                      ],
                    )
                  : Column(
                      children: [
                        billingItemsWidget(salesProvider, bgColor),
                        const SizedBox(
                          height: 2,
                        ),
                        productsWidget(bgColor)
                      ],
                    ),
            ),
          );
        });
  }

  billingItemsWidget(BillingScreenProvider provider, Color bgColor) {
    return SizedBox(
        width: isTabScreen ? screenWidth * 0.4 : screenWidth,
        height: isTabScreen ? screenHeight : screenHeight * 0.36,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            /// Billing Board Text
            Container(
                color: const Color.fromARGB(255, 255, 234, 208),
                width: isTabScreen ? screenWidth * 0.4 : screenWidth,
                alignment: Alignment.bottomLeft,
                padding: const EdgeInsets.all(6.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Billing Board',
                        style: TextStyle(fontSize: 14, color: Colors.black, fontWeight: FontWeight.bold)),
                    Consumer<SettingsProvider>(
                      builder: (context, settingsProvider, child) {
                        // Calculate final total with discounts and GST
                        final subtotal = provider.calculateTotalAmount();
                        final discount = provider.totalDiscount;
                        final gstCalculation = provider.gstCalculation;
                        final finalTotal = gstCalculation?.grandTotal ?? (subtotal - discount);

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            if (discount > 0)
                              Text('Discount: -₹${discount.toStringAsFixed(2)}',
                                  style: const TextStyle(fontSize: 12, color: Colors.red)),
                            if (gstCalculation != null && gstCalculation.totalGST > 0)
                              Text('GST: +₹${gstCalculation.totalGST.toStringAsFixed(2)}',
                                  style: const TextStyle(fontSize: 12, color: Colors.blue)),
                            Text('Total: ₹${finalTotal.toStringAsFixed(2)}',
                                style: const TextStyle(
                                    fontSize: 16, color: Colors.black, fontWeight: FontWeight.bold)),
                          ],
                        );
                      },
                    ),
                  ],
                )),

            Padding(
              padding: const EdgeInsets.only(top: 20.0, right: 10, left: 10),
              child: Container(
                color: Colors.grey[200],
                child: Padding(
                  padding: const EdgeInsets.only(top: 5.0),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 1.0, right: 10, left: 10),
                        child: Row(
                          children: [
                            SizedBox(
                              width: isTabScreen ? screenWidth * 0.12 : screenWidth * 0.36,
                              child: const Text("Product",
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.grey, fontWeight: FontWeight.w600)),
                            ),
                            SizedBox(
                              width: isTabScreen ? screenWidth * 0.1 : screenWidth * 0.15,
                              child: const Text("Qty",
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.grey, fontWeight: FontWeight.w600)),
                            ),
                            SizedBox(
                              width: isTabScreen ? screenWidth * 0.1 : screenWidth * 0.18,
                              child: const Text("Rate",
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.grey, fontWeight: FontWeight.w600)),
                            )
                          ],
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 10.0, right: 10),
                        child: Divider(
                          color: Colors.grey,
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: provider.selectedProducts.isEmpty
                  ? const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.hourglass_empty_outlined),
                        Text(
                          "Your current order is empty.",
                          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                        ),
                        SizedBox(
                          height: 0,
                        ),
                        Text(
                          "Please add some products from menu.",
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
                        ),
                      ],
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: provider.selectedProducts.length,
                      itemBuilder: (context, index) {
                        final item = provider.selectedProducts[index];
                        var pName = products
                                .firstWhereOrNull((prod) => prod.productId == item.productId)
                                ?.productName ??
                            '';
                        return Padding(
                          padding: const EdgeInsets.only(top: 0.0, right: 10, left: 10),
                          child: Container(
                            color: Colors.grey[200],
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(top: 1.0, right: 10, left: 10),
                                  child: Row(
                                    children: [
                                      SizedBox(
                                        width: isTabScreen ? screenWidth * 0.12 : screenWidth * 0.36,
                                        child: Text(pName,
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey,
                                                fontWeight: FontWeight.w600)),
                                      ),
                                      SizedBox(
                                        width: isTabScreen ? screenWidth * 0.1 : screenWidth * 0.15,
                                        child: Text(item.productQty.toString(),
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey,
                                                fontWeight: FontWeight.w600)),
                                      ),
                                      SizedBox(
                                        width: isTabScreen ? screenWidth * 0.1 : screenWidth * 0.18,
                                        child: Text("₹ ${item.productAmount}",
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey,
                                                fontWeight: FontWeight.w600)),
                                      ),
                                      // PopupMenuExample(
                                      //   onEdit: () {},
                                      //   onDelete: () {
                                      //     provider.deleteSalesTransation(
                                      //         item.salesTransactionId!);
                                      //   },
                                      // )
                                    ],
                                  ),
                                ),
                                const Padding(
                                  padding: EdgeInsets.only(top: 0.0, right: 10, left: 10),
                                  child: Divider(
                                    color: Colors.grey,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      }),
            ),

            Padding(
              padding: const EdgeInsets.only(left: 10.0, right: 10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                      onTap: () {},
                      child: Text("View Bill",
                          style: TextStyle(fontSize: 15, color: bgColor, fontWeight: FontWeight.bold))),
                  const Spacer(),
                  InkWell(
                    onTap: () {
                      if (provider.selectedProducts.isNotEmpty) {
                        provider.resetSales();
                      }
                    },
                    child: const Row(
                      children: [
                        Icon(
                          Icons.delete_rounded,
                          size: 18,
                        ),
                        SizedBox(
                          width: 1.0,
                        ),
                        Text("Clear",
                            style: TextStyle(fontSize: 15, color: Colors.black, fontWeight: FontWeight.bold)),
                        SizedBox(
                          width: 12.0,
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      if (provider.selectedProducts.isNotEmpty) {
                        _showAddSalesSheet(context, products);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      width: 120,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.0),
                          color: provider.selectedProducts.isEmpty
                              ? const Color.fromARGB(255, 206, 201, 201)
                              : bgColor),
                      child: const Text("Create Bill",
                          style: TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.w600)),
                    ),
                  ),
                ],
              ),
            )
          ],
        ));
  }

  productsWidget(Color bgColor) {
    return Consumer2<ProductProvider, BillingScreenProvider>(
      builder: (context, productProvider, salesProvider, child) {
        _products = salesProvider.filteredProducts.isNotEmpty
            ? salesProvider.filteredProducts
            : productProvider.products;
        return Container(
          padding: const EdgeInsets.only(left: 10, right: 10),
          width: isTabScreen ? screenWidth * 0.58 : screenWidth,
          // color: Colors.orange,
          height: isTabScreen ? screenHeight : screenHeight * 0.40,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 40,
                    width: isTabScreen ? screenWidth * 0.3 : screenWidth * 0.5,
                    child: TextFormField(
                      controller: salesProvider.searchController,
                      onChanged: (value) => salesProvider.searchProducts(value, productProvider.products),
                      decoration: InputDecoration(
                        hintText: 'Search',
                        filled: true,
                        fillColor: Colors.grey[200],
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    // width: isTabScreen ? screenWidth * 0.3 : screenWidth * 0.5,
                    child: Container(
                      height: 40,
                      padding: const EdgeInsets.only(left: 8.0),
                      child: CommonDropdown(
                        enableMargine: false,
                        items: productProvider.categoriesList,
                        selectedItem: productProvider.selectedCategory,
                        onChanged: (val) async {
                          if (val != null) {
                            productProvider.onChageCat(val);
                            salesProvider.filterByCategory(
                                productProvider.selectedCategory!.id, productProvider.products);
                          }
                        },
                        // itemLabel: (DropdownItem item) => item.label,
                      ),
                    ),
                  ),
                  PieMenuIcon(
                    (action) {
                      // Handle pie menu actions here
                      debugPrint('PieMenu action selected: $action');
                      // You can implement your logic based on action here
                    },
                    key: Key('$wKey'),
                  ),
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              Expanded(
                child: GridView.builder(
                  itemCount: _products.length + 1, // +1 for the "Add Product" card
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isTabScreen ? 4 : 3, // Number of columns
                    crossAxisSpacing: 1, // Space between columns
                    mainAxisSpacing: 1, // Space between rows
                  ),
                  itemBuilder: (context, index) {
                    if (index == _products.length) {
                      // "Add Product" card at the end
                      return GestureDetector(
                        onTap: () {
                          Get.toNamed(AppRoutes.productsScreen)?.then((value) {
                            productProvider.fetchProducts();
                          });
                        },
                        child: Card(
                          elevation: 4,
                          color: Colors.black45,
                          margin: const EdgeInsets.all(4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(5.0),
                                decoration: BoxDecoration(
                                    color: Colors.white, borderRadius: BorderRadius.circular(50)),
                                child: Icon(
                                  Icons.add_rounded,
                                  size: 25,
                                  color: bgColor,
                                ),
                              ),
                              const SizedBox(height: 20),
                              const Text(
                                'Add Product',
                                style:
                                    TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.w600),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    // Product card
                    final product = _products[index];
                    return GestureDetector(
                        onTap: () {
                          var uuid = const Uuid();

                          SalesTransactionDto productSalesDto = SalesTransactionDto(
                              salesTransactionId: uuid.v4(),
                              productAmount: double.tryParse(product.costOfProduction ?? "0.0") ?? 0.0,
                              productName: product.productName,
                              productId: product.productId,
                              status: 1,
                              rowStatus: 1);
                          salesProvider.addOrUpdateBillingProduct(productSalesDto);
                        },
                        child: Card(
                          elevation: 4,
                          margin: const EdgeInsets.all(4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: (product.productImagePath == null || product.productImagePath!.isEmpty)
                                  ? Colors.black54
                                  : null,
                            ),
                            child: Stack(
                              children: [
                                // 🔹 Semi-transparent overlay for better text contrast
                                Container(
                                  decoration: BoxDecoration(
                                    color: (product.productImagePath == null ||
                                            product.productImagePath!.isEmpty)
                                        ? null
                                        : Colors.black54, // Dark overlay
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),

                                // 🔹 Center the content
                                Center(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (product.productImagePath != null &&
                                          product.productImagePath!.isNotEmpty) ...[
                                        Padding(
                                          padding: const EdgeInsets.all(6.0),
                                          child: Container(
                                            height: 75,
                                            width: 105,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(8),
                                              border: Border.all(color: Colors.white),
                                            ),
                                            child: ClipRRect(
                                              borderRadius: BorderRadius.circular(8),
                                              child: Stack(
                                                fit: StackFit.expand,
                                                children: [
                                                  // 🖼️ Product Image
                                                  Image.file(
                                                    File(product.productImagePath!),
                                                    fit: BoxFit.cover,
                                                  ),

                                                  // 💰 Outlined Price Text (black stroke + white fill)
                                                  Align(
                                                    alignment: Alignment.topRight,
                                                    child: Stack(
                                                      children: [
                                                        // Black Stroke Text
                                                        Text(
                                                          '₹ ${product.costOfProduction ?? "0.00"}',
                                                          style: white22w500.copyWith(
                                                            foreground: Paint()
                                                              ..style = PaintingStyle.stroke
                                                              ..strokeWidth = 2.5
                                                              ..color = Colors.black,
                                                          ),
                                                        ),
                                                        // White Fill Text
                                                        Text(
                                                          '₹ ${product.costOfProduction ?? "0.00"}',
                                                          style: white22w500,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                          child: Text(
                                            product.productName ?? "Unknown Product",
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                              shadows: [
                                                const Shadow(
                                                  blurRadius: 4,
                                                  color: Colors.black,
                                                  offset: Offset(1, 1),
                                                ),
                                              ],
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                      if (product.productImagePath?.isEmpty == true) ...[
                                        Center(
                                          child: Stack(
                                            children: [
                                              // Black Stroke Text
                                              Text(
                                                '₹ ${product.costOfProduction ?? "0.00"}',
                                                style: white22w500.copyWith(
                                                  foreground: Paint()
                                                    ..style = PaintingStyle.stroke
                                                    ..strokeWidth = 2.5
                                                    ..color = Colors.black,
                                                ),
                                              ),
                                              // White Fill Text
                                              Text(
                                                '₹ ${product.costOfProduction ?? "0.00"}',
                                                style: white22w500,
                                              ),
                                            ],
                                          ),
                                        ), // ✅ Product Name
                                        Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                          child: Text(
                                            product.productName ?? "Unknown Product",
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                              shadows: [
                                                const Shadow(
                                                  blurRadius: 4,
                                                  color: Colors.black,
                                                  offset: Offset(1, 1),
                                                ),
                                              ],
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],

                                      const SizedBox(height: 4),

                                      // ✅ Product Price
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ));
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showAddSalesSheet(BuildContext context, List<ProductDto> products) {
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      isScrollControlled: true, // Full-screen behavior if needed
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => AddSalesBottomSheet(products: products),
    );
  }
}
