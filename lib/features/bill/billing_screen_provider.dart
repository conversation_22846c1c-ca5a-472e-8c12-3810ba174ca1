import 'dart:developer';

import 'package:coffee_cofe/database/dao/sales_dao.dart';
import 'package:coffee_cofe/database/dao/sales_transation_dao.dart';
import 'package:coffee_cofe/database/entities/product.dart';
import 'package:coffee_cofe/database/entities/sales_dto.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../database/app_database.dart';
import '../../database/dao/inventory_dao.dart';
import '../../database/dao/discount_log_dao.dart';
import '../../database/entities/sales_transaction_dto.dart';
import '../../database/entities/discount_log_dto.dart';
import '../../widgets/success_alert_dialog_widget.dart';
import '../../services/discount_service.dart';
import '../../services/gst_service.dart';
import '../../services/simple_gst_service.dart';
import '../settings/settings_provider.dart';

class BillingScreenProvider with ChangeNotifier {
  List<ProductDto> products = [];
  List<ProductDto> get product => products;
  final TextEditingController customerNameController = TextEditingController();
  final TextEditingController customerMobileController = TextEditingController();
  final TextEditingController salesNoteController = TextEditingController(text: '');
  List<SalesTransactionDto> _selectedProducts = [];
  List<SalesTransactionDto> get selectedProducts => _selectedProducts;
  SalesDao? _salesDao;
  SalesTransactionDao? _salesTransactionDao;
  InventoryDao? _inventoryDAO;
  DiscountLogDao? _discountLogDao;

  TextEditingController searchController = TextEditingController();
  String _selectedPaymentMethod = "Cash"; // default payment method

  // Discount and GST related properties
  final DiscountService _discountService = DiscountService();
  final GSTService _gstService = GSTService();
  final SimpleGSTService _simpleGstService = SimpleGSTService();
  final List<DiscountResult> _appliedDiscounts = [];
  double _totalDiscount = 0.0;
  GSTCalculation? _gstCalculation;
  SimpleGSTResult? _simpleGstResult;

  // Simplified discount properties
  double _manualDiscountPercentage = 0.0;
  String? _appliedCouponCode;
  bool _hasSimplifiedDiscount = false;

  String get selectedPaymentMethod => _selectedPaymentMethod;
  List<DiscountResult> get appliedDiscounts => _appliedDiscounts;
  double get totalDiscount => _totalDiscount;
  GSTCalculation? get gstCalculation => _gstCalculation;

  // Simplified discount getters
  double get manualDiscountPercentage => _manualDiscountPercentage;
  String? get appliedCouponCode => _appliedCouponCode;
  bool get hasSimplifiedDiscount => _hasSimplifiedDiscount;
  SimpleGSTResult? get simpleGstResult => _simpleGstResult;

  void setPaymentMethod(String method) {
    _selectedPaymentMethod = method;
    notifyListeners();
  }

  void addOrUpdateBillingProduct(SalesTransactionDto product) {
    int index = _selectedProducts.indexWhere((item) => item.productId == product.productId);

    if (index != -1) {
      // Increase the quantity and update amount
      _selectedProducts[index].productQty = (_selectedProducts[index].productQty ?? 0) + 1;
      double unitPrice = product.productAmount ?? 0.0;
      _selectedProducts[index].productAmount = unitPrice * _selectedProducts[index].productQty!;
    } else {
      // Add new product with quantity 1
      product.productQty = 1;
      _selectedProducts.add(product);
    }

    notifyListeners();
  }

  BillingScreenProvider() {
    iniFunction();
  }
  iniFunction() async {
    _salesDao = await AppDatabase().salesDao;
    _salesTransactionDao = await AppDatabase().salesTransactionDao;
    _inventoryDAO = await AppDatabase().inventoryDao;
    _discountLogDao = await AppDatabase().discountLogDao;
    filteredProducts = [];
  }

  double calculateTotalAmount() {
    return _selectedProducts.fold(0, (total, item) {
      double itemPrice = item.productAmount ?? 0.0;
      return total + itemPrice;
    });
  }

  /// Calculate final total including discounts and GST (Original method)
  Future<double> calculateFinalTotal(SettingsProvider settingsProvider) async {
    await calculateGST(settingsProvider);
    return _gstCalculation?.grandTotal ?? (calculateTotalAmount() - _totalDiscount);
  }

  /// Calculate final total using simplified GST
  Future<double> calculateSimpleFinalTotal(SettingsProvider settingsProvider) async {
    await calculateSimpleGST(settingsProvider);
    return _simpleGstResult?.grandTotal ?? (calculateTotalAmount() - _totalDiscount);
  }

  /// Get applicable auto-discounts for current order
  Future<List<DiscountResult>> getAutoApplyDiscounts({String? workspaceId}) async {
    try {
      return await _discountService.calculateApplicableDiscounts(
        cartItems: _selectedProducts,
        totalAmount: calculateTotalAmount(),
        workspaceId: workspaceId,
      );
    } catch (e) {
      log('Error getting auto-apply discounts: $e');
      return [];
    }
  }

  /// Apply manual discount
  void applyManualDiscount({
    required double discountValue,
    required bool isPercentage,
    String? reason,
  }) {
    final result = _discountService.applyManualDiscount(
      totalAmount: calculateTotalAmount(),
      discountValue: discountValue,
      isPercentage: isPercentage,
      reason: reason,
    );

    _appliedDiscounts.add(result);
    _calculateTotalDiscount();
    notifyListeners();
  }

  /// Apply coupon code
  Future<bool> applyCouponCode(String couponCode, {String? workspaceId}) async {
    try {
      final result = await _discountService.validateCouponCode(
        couponCode: couponCode,
        cartItems: _selectedProducts,
        totalAmount: calculateTotalAmount(),
        workspaceId: workspaceId,
      );

      if (result != null) {
        _appliedDiscounts.add(result);
        _calculateTotalDiscount();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      log('Error applying coupon code: $e');
      return false;
    }
  }

  /// Remove discount
  void removeDiscount(int index) {
    if (index >= 0 && index < _appliedDiscounts.length) {
      _appliedDiscounts.removeAt(index);
      _calculateTotalDiscount();
      notifyListeners();
    }
  }

  // Simplified discount methods

  /// Apply simplified manual discount by percentage
  void applySimplifiedManualDiscount(double percentage) {
    _clearSimplifiedDiscounts();
    _manualDiscountPercentage = percentage;
    _hasSimplifiedDiscount = true;

    // Calculate discount amount
    final subtotal = calculateTotalAmount();
    final discountAmount = subtotal * (percentage / 100);

    // Create a discount result for compatibility
    final result = DiscountResult(
      discountAmount: discountAmount,
      message: 'Manual ${percentage.toStringAsFixed(1)}% discount',
      appliedDiscount: null,
    );

    _appliedDiscounts.clear();
    _appliedDiscounts.add(result);
    _calculateTotalDiscount();
    notifyListeners();
  }

  /// Apply simplified coupon code
  Future<bool> applySimplifiedCouponCode(String couponCode) async {
    try {
      _clearSimplifiedDiscounts();

      // For now, simulate coupon validation with some basic coupons
      final discountPercentage = _getSimulatedCouponDiscount(couponCode);

      if (discountPercentage > 0) {
        _appliedCouponCode = couponCode;
        _hasSimplifiedDiscount = true;

        // Calculate discount amount
        final subtotal = calculateTotalAmount();
        final discountAmount = subtotal * (discountPercentage / 100);

        // Create a discount result for compatibility
        final result = DiscountResult(
          discountAmount: discountAmount,
          message: 'Coupon "$couponCode" - ${discountPercentage.toStringAsFixed(1)}% off',
          appliedDiscount: null,
        );

        _appliedDiscounts.clear();
        _appliedDiscounts.add(result);
        _calculateTotalDiscount();
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      log('Error applying simplified coupon code: $e');
      return false;
    }
  }

  /// Remove simplified discount
  void removeSimplifiedDiscount() {
    _clearSimplifiedDiscounts();
    _appliedDiscounts.clear();
    _calculateTotalDiscount();
    notifyListeners();
  }

  /// Clear simplified discount state
  void _clearSimplifiedDiscounts() {
    _manualDiscountPercentage = 0.0;
    _appliedCouponCode = null;
    _hasSimplifiedDiscount = false;
  }

  /// Simulate coupon validation (replace with real validation later)
  double _getSimulatedCouponDiscount(String couponCode) {
    final coupons = {
      'SAVE10': 10.0,
      'SAVE15': 15.0,
      'SAVE20': 20.0,
      'WELCOME5': 5.0,
      'STUDENT10': 10.0,
      'FIRST25': 25.0,
    };

    return coupons[couponCode.toUpperCase()] ?? 0.0;
  }

  /// Calculate total discount amount
  void _calculateTotalDiscount() {
    _totalDiscount = _appliedDiscounts.fold(0.0, (sum, discount) => sum + discount.discountAmount);
  }

  /// Calculate GST (Original method - kept for compatibility)
  Future<void> calculateGST(SettingsProvider settingsProvider) async {
    try {
      _gstCalculation = await _gstService.calculateGST(
        cartItems: _selectedProducts,
        discountAmount: _totalDiscount,
        settingsProvider: settingsProvider,
      );
      notifyListeners();
    } catch (e) {
      log('Error calculating GST: $e');
    }
  }

  /// Calculate GST using simplified service
  Future<void> calculateSimpleGST(SettingsProvider settingsProvider) async {
    try {
      _simpleGstResult = await _simpleGstService.calculateGST(
        cartItems: _selectedProducts,
        discountAmount: _totalDiscount,
        settingsProvider: settingsProvider,
      );
      notifyListeners();
    } catch (e) {
      log('Error calculating simple GST: $e');
    }
  }

  /// Check if stackable discounts can be combined
  bool canStackDiscounts(List<DiscountResult> discounts) {
    // Stub implementation - for now, allow stacking
    // In a real implementation, this would check discount stacking rules
    return discounts.length <= 3; // Allow up to 3 discounts to be stacked
  }

  void saveSales({
    bool printReceipt = false,
    SettingsProvider? settingsProvider,
  }) async {
    try {
      var salesId = const Uuid().v4();

      String customerName = customerNameController.text;
      String customerMobile = customerMobileController.text;
      String salesNote = salesNoteController.text;

      final originalTotal = calculateTotalAmount();

      // Calculate GST if settings provider is available
      if (settingsProvider != null) {
        await calculateGST(settingsProvider);
      }

      final finalTotal = _gstCalculation?.grandTotal ?? (originalTotal - _totalDiscount);

      await _salesDao?.insertSale(SalesDto(
        createdDate: DateTime.now().toIso8601String(),
        updatedDate: '',
        salesId: salesId,
        salesTransactionId: '',
        totalAmount: finalTotal, // Save the final amount after discount
        customerId: 'new',
        status: 1,
        rowStatus: 0,
      ));

      _selectedProducts = _selectedProducts.map((item) {
        return SalesTransactionDto(
            salesId: salesId, // Set the new salesTransactionId
            salesTransactionId: item.salesTransactionId,
            productAmount: item.productAmount,
            productName: item.productName,
            productId: item.productId,
            status: item.status,
            rowStatus: item.rowStatus,
            productQty: item.productQty, // Ensure this property is included if needed
            createdAt: DateTime.now().toIso8601String());
      }).toList();
      await _salesTransactionDao?.insertMultipleTransactions(selectedProducts);
      await _inventoryDAO?.updateInventoryStockQty(selectedProducts
          .map((item) => ProductWithQty(item.productId, double.tryParse(item.productQty.toString()) ?? 0))
          .toList());

      // Log discount applications if any
      if (_appliedDiscounts.isNotEmpty) {
        for (final discountResult in _appliedDiscounts) {
          if (discountResult.appliedDiscount?.discountID != null) {
            final discountLog = DiscountLogDto(
              discountID: discountResult.appliedDiscount!.discountID!,
              salesId: salesId,
              appliedBy: 'current_user', // Should come from app state
              appliedDate: DateTime.now().toIso8601String(),
              originalAmount: originalTotal,
              discountAmount: discountResult.discountAmount,
              finalAmount: finalTotal,
              discountType: 'applied',
              workSpaceId: 'default_workspace', // Should come from app state
            );

            await _discountLogDao?.insertDiscountLog(discountLog);
          }
        }
      }

      log(selectedProducts.firstOrNull?.productName ?? '');
      log(selectedProducts.length.toString());
      // Perform save operation
      log("Customer: $customerName, Mobile: $customerMobile, Note: $salesNote");

      if (printReceipt) {
        log("Printing receipt...");
      }

      showSuccessAlert(
        message: 'sales success fully completed',
        onClose: () {
          Get.back();
        },
      );
    } catch (e) {
      log(e.toString());
    } finally {
      resetSales();
      calculateTotalAmount();
      notifyListeners();
    }
  }

  resetSales() {
    _selectedProducts.clear();
    _appliedDiscounts.clear();
    _totalDiscount = 0.0;
    _gstCalculation = null;
    calculateTotalAmount();
    notifyListeners();
  }

  deleteSalesTransation(String salesTId) {
    _selectedProducts.removeWhere((item) => item.salesTransactionId == salesTId);
    notifyListeners();
  }

  List<ProductDto> filteredProducts = [];

  void searchProducts(String query, List<ProductDto> allProducts) {
    if (query.isEmpty) {
      filteredProducts = allProducts;
    } else {
      filteredProducts = allProducts
          .where((product) => product.productName!.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
    notifyListeners(); // very important to refresh the UI
  }

  void filterByCategory(String catId, List<ProductDto> allProducts) {
    filteredProducts.clear();
    if (catId.isEmpty || catId == 'None') {
      filteredProducts = allProducts;
    } else {
      filteredProducts = allProducts.where((product) => product.categoryId == catId).toList();
    }
    notifyListeners();
  }
}

class ProductWithQty {
  String? productId;
  double? qty;
  ProductWithQty(this.productId, this.qty);
}
