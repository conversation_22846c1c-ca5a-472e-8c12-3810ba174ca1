import 'package:coffee_cofe/core/styles.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../database/entities/product.dart';
import '../../widgets/common_button.dart';
import '../../widgets/input_form_field.dart';
import '../../widgets/simplified_discount_widget.dart';
import '../../services/discount_service.dart';
import '../settings/settings_provider.dart';
import 'billing_screen_provider.dart';

class AddSalesBottomSheet extends StatefulWidget {
  final List<ProductDto> products;
  const AddSalesBottomSheet({super.key, required this.products});

  @override
  State<AddSalesBottomSheet> createState() => _AddSalesBottomSheetState();
}

class _AddSalesBottomSheetState extends State<AddSalesBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  bool isTabScreen = false;
  SizedBox height10 = const SizedBox(height: 10);

  Future<void> _checkAutoDiscounts([BillingScreenProvider? salesProvider]) async {
    salesProvider ??= Provider.of<BillingScreenProvider>(context, listen: false);

    try {
      final autoDiscounts = await salesProvider.getAutoApplyDiscounts();

      if (autoDiscounts.isNotEmpty) {
        // Show dialog to user about available auto-discounts
        _showAutoDiscountDialog(autoDiscounts);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No automatic discounts available for this order'),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking auto-discounts: $e');
    }
  }

  void _showAutoDiscountDialog(List<DiscountResult> autoDiscounts) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Available Discounts'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('The following discounts can be automatically applied:'),
              const SizedBox(height: 12),
              ...autoDiscounts.map((discount) => Card(
                    child: ListTile(
                      leading: const Icon(Icons.local_offer, color: Colors.green),
                      title: Text(discount.message),
                      subtitle: Text('Save \$${discount.discountAmount.toStringAsFixed(2)}'),
                      trailing: Checkbox(
                        value: true,
                        onChanged: (value) {
                          // Handle individual discount selection
                        },
                      ),
                    ),
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              // Apply selected auto-discounts
              // Note: Auto-discounts are now handled by the provider automatically
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Applied ${autoDiscounts.length} automatic discounts'),
                ),
              );
            },
            child: const Text('Apply All'),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // Check for auto-discounts when the bottom sheet opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAutoDiscounts();
    });
  }

  @override
  Widget build(BuildContext context) {
    final salesProvider = Provider.of<BillingScreenProvider>(context);
    screenHeight = MediaQuery.of(context).size.height;
    screenWidth = MediaQuery.of(context).size.width;
    isTabScreen = screenWidth > 600;
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Padding(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                "Add Sales",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              height10,

              Form(
                key: _formKey,
                child: Column(
                  children: [
                    InputFormField(
                      controller: salesProvider.customerNameController,
                      hint: "Enter customer name",
                      label: "Customer Name",
                      validator: (value) => value!.isEmpty ? "Customer name is required" : null,
                    ),
                    const SizedBox(height: 10),
                    InputFormField(
                      controller: salesProvider.customerMobileController,
                      hint: "Enter mobile number",
                      label: "Customer Mobile No",
                      inputType: TextInputType.phone,
                    ),
                  ],
                ),
              ),

              height10,

              Column(
                children: [
                  // List of products
                  ...salesProvider.selectedProducts.map((item) {
                    var pName = widget.products
                            .firstWhereOrNull((prod) => prod.productId == item.productId)
                            ?.productName ??
                        '';

                    return Container(
                      color: Colors.grey[200],
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 1.0, right: 10, left: 10),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: isTabScreen ? screenWidth * 0.12 : screenWidth * 0.46,
                                  child: Text(pName, style: labelStyle),
                                ),
                                SizedBox(
                                  width: isTabScreen ? screenWidth * 0.1 : screenWidth * 0.20,
                                  child: Text(item.productQty.toString(), style: labelStyle),
                                ),
                                const Spacer(),
                                SizedBox(
                                  child: Text("₹ ${item.productAmount}", style: labelStyle),
                                ),
                              ],
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.only(top: 0.0, right: 10, left: 10),
                            child: Divider(color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  }),

                  // Subtotal and Total Amount
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.only(right: 10, left: 10),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Subtotal: ', style: black15w500),
                            Text(
                              '₹${salesProvider.calculateTotalAmount().toStringAsFixed(2)}',
                              style: black15w500,
                            ),
                          ],
                        ),
                        if (salesProvider.totalDiscount > 0) ...[
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Discount: ', style: black15w500),
                              Text(
                                '-₹${salesProvider.totalDiscount.toStringAsFixed(2)}',
                                style: black15w500.copyWith(color: Colors.red[600]),
                              ),
                            ],
                          ),
                        ],
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Total: ',
                              style: black15w500.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Consumer<SettingsProvider>(
                              builder: (context, settingsProvider, child) {
                                // Trigger simplified GST calculation
                                WidgetsBinding.instance.addPostFrameCallback((_) {
                                  salesProvider.calculateSimpleGST(settingsProvider);
                                });

                                final simpleGstResult = salesProvider.simpleGstResult;
                                final finalTotal = simpleGstResult?.grandTotal ??
                                    (salesProvider.calculateTotalAmount() - salesProvider.totalDiscount);
                                return Text(
                                  '₹${finalTotal.toStringAsFixed(2)}',
                                  style: black15w500.copyWith(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.green[600],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              height10,

              // Payment Method Buttons
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Select Payment Method *",
                  style: hitStyle,
                ),
              ),
              height10,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _paymentButton("Cash", Colors.green, Icons.payments),
                  _paymentButton("GPay", Colors.blue, Icons.account_balance_wallet),
                  _paymentButton("Card", Colors.orange, Icons.credit_card),
                  _paymentButton("UPI", Colors.purple, Icons.qr_code),
                ],
              ),

              height10,

              Row(
                children: [
                  Icon(Icons.circle, color: _getPaymentColor(salesProvider.selectedPaymentMethod), size: 15),
                  const SizedBox(width: 6),
                  Text(
                    "${salesProvider.selectedPaymentMethod} payment selected",
                    style: black15w500,
                  ),
                ],
              ),

              height10,

              // Simplified Discount Section
              SimplifiedDiscountWidget(
                onManualDiscountApplied: (percentage) {
                  salesProvider.applySimplifiedManualDiscount(percentage);
                },
                onDiscountRemoved: () {
                  salesProvider.removeSimplifiedDiscount();
                },
                currentDiscountPercentage: salesProvider.manualDiscountPercentage,
                hasDiscount: salesProvider.hasSimplifiedDiscount,
              ),

              height10,

              // GST Breakdown
              _buildGSTBreakdown(salesProvider),

              height10,

              // Sales Note
              InputFormField(
                controller: salesProvider.salesNoteController,
                label: "Sales Note",
                hint: "Enter sales note",
              ),

              height10,

              // Save Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonButton(
                    borderColor: AppColors.primary,
                    width: screenWidth * 0.43,
                    isOutlined: true,
                    backgroundColor: AppColors.surface,
                    onTap: () => salesProvider.saveSales(
                      settingsProvider: Provider.of<SettingsProvider>(context, listen: false),
                    ),
                    icon: Icons.save,
                    title: "Save",
                    textStyle: green15w500,
                  ),
                  CommonButton(
                    width: screenWidth * 0.43,
                    backgroundColor: AppColors.primary,
                    onTap: () => salesProvider.saveSales(
                      printReceipt: true,
                      settingsProvider: Provider.of<SettingsProvider>(context, listen: false),
                    ),
                    icon: Icons.print,
                    title: "Print & Save",
                    textStyle: white16w500,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _paymentButton(String label, Color color, IconData icon) {
    final salesProvider = Provider.of<BillingScreenProvider>(context, listen: false);
    final isSelected = salesProvider.selectedPaymentMethod == label;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: () {
            salesProvider.setPaymentMethod(label);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected ? color : color.withOpacity(0.2),
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: isSelected ? const BorderSide(color: Colors.grey, width: 1) : BorderSide.none,
            ),
          ),
          icon: Icon(icon, size: 18, color: Colors.white),
          label: Text(label, style: const TextStyle(color: Colors.white)),
        ),
      ),
    );
  }

  Color _getPaymentColor(String method) {
    switch (method) {
      case "Cash":
        return Colors.green;
      case "GPay":
        return Colors.blue;
      case "Card":
        return Colors.orange;
      case "UPI":
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Widget _buildGSTBreakdown(BillingScreenProvider salesProvider) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        // Trigger simplified GST calculation
        WidgetsBinding.instance.addPostFrameCallback((_) {
          salesProvider.calculateSimpleGST(settingsProvider);
        });

        final simpleGstResult = salesProvider.simpleGstResult;

        if (!settingsProvider.isTaxEnabled || simpleGstResult == null || !simpleGstResult.hasGST) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Tax Breakdown',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              _buildGSTRow('Subtotal', simpleGstResult.subtotal),
              if (salesProvider.totalDiscount > 0)
                _buildGSTRow('Discount', -salesProvider.totalDiscount, color: Colors.red),
              _buildGSTRow('Taxable Amount', simpleGstResult.taxableAmount),
              if (simpleGstResult.cgst > 0) _buildGSTRow('CGST', simpleGstResult.cgst),
              if (simpleGstResult.sgst > 0) _buildGSTRow('SGST', simpleGstResult.sgst),
              const Divider(),
              _buildGSTRow(
                'Grand Total',
                simpleGstResult.grandTotal,
                isTotal: true,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGSTRow(String label, double amount, {bool isTotal = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '₹${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color ?? (isTotal ? Colors.black : Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }
}
