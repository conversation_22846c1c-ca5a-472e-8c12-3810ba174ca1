import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import '../../../core/constants/app_colors.dart';
import '../../../widgets/common_drawer.dart';
import '../../../widgets/employe_cared_widget.dart';
import '../create_employee/create_employee_provider.dart';
import '../create_employee/create_employee_screen.dart';
import '../create_employee/edit_employee_screen.dart';

// screens/employee_list_screen.dart
class EmployeeListScreen extends StatelessWidget {
  const EmployeeListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(title: 'Employees'),
      body: Consumer<EmployeeProvider>(
        builder: (context, provider, child) {
          if (provider.employees.isEmpty) {
            return const Center(child: Text('No employees found'));
          }

          return ListView.builder(
            itemCount: provider.employees.length,
            itemBuilder: (context, index) {
              final employee = provider.employees[index];
              return EmployeeCard(
                employee: employee,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditEmployeeScreen(
                        employeeIndex: index, // Pass the index directly
                      ),
                    ),
                  );
                },
                onDelete: () => _showDeleteDialog(context, index, provider),
              );
            },
          );
        },
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 10.0, bottom: 25, right: 25),
        child: FloatingActionButton(
          backgroundColor: AppColors.primary,
          onPressed: () async {
            final result = await Get.to(() => const AddEmployeeScreen());
            if (result == 'refresh') {
              // await Provider.of<ProductLEmployeeProvideristProvider>(context, listen: false).fetchProducts();
            }
          },
          child: const Icon(
            Icons.add,
            size: 35,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Future<void> _showDeleteDialog(BuildContext context, int index, EmployeeProvider provider) async {
    // Update dialog to use index
    provider.deleteEmployeeByIndex(index);
    // [Rest of dialog code]
  }
}
