// screens/edit_employee_screen.dart
import 'dart:io';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/add_employee_details_model.dart';
import '../../../widgets/common_drawer.dart';
import '../../../widgets/image_picker_widget.dart';
import 'create_employee_provider.dart';

class EditEmployeeScreen extends StatefulWidget {
  final int employeeIndex;

  const EditEmployeeScreen({
    super.key,
    required this.employeeIndex,
  });

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  late Employee _employee;
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dobController = TextEditingController();
  final _addressController = TextEditingController();
  File? _profileImage;
  late String _selectedRole;
  final List<String> _roles = ['Developer', 'Designer', 'Manager', 'HR', 'Tester'];

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<EmployeeProvider>(context, listen: false);
    _employee = provider.getEmployeeByIndex(widget.employeeIndex)!;

    _firstNameController.text = _employee.firstName;
    _lastNameController.text = _employee.lastName;
    _emailController.text = _employee.email;
    _phoneController.text = _employee.phoneNumber;
    _selectedRole = _employee.role;
    _dobController.text = _employee.dateOfBirth;
    _addressController.text = _employee.address;
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _dobController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _dobController.text = "${picked.month}/${picked.day}/${picked.year}";
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final updatedEmployee = Employee(
        id: _employee.id,
        profileImagePath: _profileImage?.path ?? _employee.profileImagePath,
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        email: _emailController.text,
        phoneNumber: _phoneController.text,
        role: _selectedRole,
        dateOfBirth: _dobController.text,
        address: _addressController.text,
      );

      Provider.of<EmployeeProvider>(context, listen: false)
          .updateEmployeeByIndex(widget.employeeIndex, updatedEmployee);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Employee updated successfully'),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(title: 'Edit Employee'),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: ImagePickerWidget(
                    onImageSelected: (image) {
                      setState(() {
                        _profileImage = image;
                      });
                    },
                    initialImagePath: _employee.profileImagePath,
                  ),
                ),
                const SizedBox(height: 20),
                // First Name
                const Text('First Name *', style: TextStyle(fontWeight: FontWeight.bold)),
                TextFormField(
                  controller: _firstNameController,
                  decoration: _inputDecoration('Enter first name'),
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 20),
                // Last Name
                const Text('Last Name *', style: TextStyle(fontWeight: FontWeight.bold)),
                TextFormField(
                  controller: _lastNameController,
                  decoration: _inputDecoration('Enter last name'),
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 20),
                // Email
                const Text('Email *', style: TextStyle(fontWeight: FontWeight.bold)),
                TextFormField(
                  controller: _emailController,
                  decoration: _inputDecoration('Enter email'),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Required';
                    } else if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
                        .hasMatch(value!)) {
                      return 'Invalid email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                // Phone Number
                const Text('Phone Number *', style: TextStyle(fontWeight: FontWeight.bold)),
                TextFormField(
                  controller: _phoneController,
                  decoration: _inputDecoration('Enter phone number'),
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 20),
                // Role
                const Text('Role *', style: TextStyle(fontWeight: FontWeight.bold)),
                DropdownButtonFormField<String>(
                  value: _selectedRole,
                  items: _roles.map((role) {
                    return DropdownMenuItem(
                      value: role,
                      child: Text(role),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedRole = value!;
                    });
                  },
                  decoration: _inputDecoration('Select role'),
                  validator: (value) => value == null ? 'Required' : null,
                ),
                const SizedBox(height: 20),
                // Date of Birth
                const Text('Date of Birth *', style: TextStyle(fontWeight: FontWeight.bold)),
                TextFormField(
                  controller: _dobController,
                  readOnly: true,
                  onTap: () => _selectDate(context),
                  decoration: _inputDecoration('Select date of birth'),
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 20),
                // Address
                const Text('Address *', style: TextStyle(fontWeight: FontWeight.bold)),
                TextFormField(
                  controller: _addressController,
                  decoration: _inputDecoration('Enter address'),
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                Center(
                  child: ElevatedButton(
                    onPressed: _submitForm,
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(200, 50),
                      backgroundColor: Colors.green,
                    ),
                    child: const Text('SAVE', style: TextStyle(color: Colors.white)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  InputDecoration _inputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      filled: true,
      fillColor: Colors.grey[200],
      border: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.green),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 15),
    );
  }
}
