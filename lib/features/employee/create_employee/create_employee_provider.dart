import 'package:flutter/material.dart';

import '../../../models/add_employee_details_model.dart';

// providers/employee_provider.dart
class EmployeeProvider with ChangeNotifier {
  final List<Employee> _employees = [];

  List<Employee> get employees => [..._employees];

  Employee? getEmployeeByIndex(int index) {
    if (index >= 0 && index < _employees.length) {
      return _employees[index];
    }
    return null;
  }

  void addEmployee(Employee employee) {
    _employees.add(employee);
    notifyListeners();
  }

  void updateEmployeeByIndex(int index, Employee updatedEmployee) {
    if (index >= 0 && index < _employees.length) {
      _employees[index] = updatedEmployee;
      notifyListeners();
    }
  }

  void deleteEmployeeByIndex(int index) {
    if (index >= 0 && index < _employees.length) {
      _employees.removeAt(index);
      notifyListeners();
    }
  }
}
