import 'package:flutter/material.dart';
import '../../database/app_database.dart';
import '../../database/dao/settings_dao.dart';
import '../../database/entities/settings_dto.dart';
import 'settings_screen.dart';

class SettingsProvider with ChangeNotifier {
  late SettingsDAO _settingsDao;

  bool isTaxEnabled = false;
  bool isParcelEnabled = false;
  bool includeTax = false;
  bool excludeTax = false;
  bool applyTaxSpecificProduct = false;
  bool applyTaxAllProducts = false;
  bool parcelSpecificProduct = false;
  bool parcelAllProducts = false;

  initProviderFunction( ) async{
  await  _initializeDAO();
   await _loadSettings();
  }
  Future<void> _initializeDAO() async {
    _settingsDao = await AppDatabase().settingsDao;
  }
  Future<void> _loadSettings() async {
   

  


    final List<SettingsDto> settingsList = await _settingsDao.getAllSettings('default');
    
    // Convert list to a map for efficient lookup
    final Map<String, SettingsDto> settingsMap = {
      for (var setting in settingsList) setting.keyName: setting
    };

    isTaxEnabled = _getBool(settingsMap, dbKeyEnableTax);
    isParcelEnabled = _getBool(settingsMap, dbKeyEnableParcel);
    includeTax = _getBool(settingsMap, dbKeyIncludeTax);
    excludeTax = _getBool(settingsMap, dbKeyExcludeTax);
    applyTaxSpecificProduct = _getBool(settingsMap, dbKeyApplyTaxSpecificProduct);
    applyTaxAllProducts = _getBool(settingsMap, dbKeyApplyTaxAllProducts);
    parcelSpecificProduct = _getBool(settingsMap, dbKeyParcelSpecificProduct);
    parcelAllProducts = _getBool(settingsMap, dbKeyParcelAllProducts);

    notifyListeners();
  }

  bool _getBool(Map<String, SettingsDto> settings, String key) {
    return settings[key]?.value == '1';
  }

  Future<void> _saveSetting(String key, bool value) async {
    final setting = SettingsDto(
      id: 0,
      shopUId: 'default',
      keyName: key,
      value: value ? '1' : '0',
    );
    await _settingsDao.saveSetting(setting);
  }

  void toggleTax(bool value) async {
    isTaxEnabled = value;
    await _saveSetting(dbKeyEnableTax, value);
    notifyListeners();
  }

  void toggleParcel(bool value) async {
    isParcelEnabled = value;
    await _saveSetting(dbKeyEnableParcel, value);
    notifyListeners();
  }

  void updateSetting(String key, bool value) async {
    switch (key) {
      case dbKeyIncludeTax:
        includeTax = value;
        break;
      case dbKeyExcludeTax:
        excludeTax = value;
        break;
      case dbKeyApplyTaxSpecificProduct:
        applyTaxSpecificProduct = value;
        break;
      case dbKeyApplyTaxAllProducts:
        applyTaxAllProducts = value;
        break;
      case dbKeyParcelSpecificProduct:
        parcelSpecificProduct = value;
        break;
      case dbKeyParcelAllProducts:
        parcelAllProducts = value;
        break;
    }
    await _saveSetting(key, value);
    notifyListeners();
  }
}
