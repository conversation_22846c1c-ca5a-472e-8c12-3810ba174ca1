import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../../widgets/common_appbar.dart';
import '../profile/profile_settings_controller.dart';
import 'settings_provider.dart';

const String dbKeyIncludeTax = 'includeTax';
const String dbKeyExcludeTax = 'excludeTax';
const String dbKeyApplyTaxSpecificProduct = 'applyTaxSpecificProduct';
const String dbKeyApplyTaxAllProducts = 'applyTaxAllProducts';
const String dbKeyParcelSpecificProduct = 'parcelSpecificProduct';
const String dbKeyParcelAllProducts = 'parcelAllProducts';
const String dbKeyEnableTax = 'enableTax';
const String dbKeyEnableParcel = 'enableParcel';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  void initState() {
    super.initState();
    initFunction();
  }

  initFunction() async {
    await Provider.of<SettingsProvider>(Get.context!).initProviderFunction();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
            appBar: CommonAppBar(
              appBarColor: bgColor,
              title: 'Settings',
            ),
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Consumer<SettingsProvider>(
                builder: (context, settings, child) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMainToggle(
                        title: "Enable Tax Section",
                        value: settings.isTaxEnabled,
                        onChanged: settings.toggleTax,
                      ),
                      if (settings.isTaxEnabled)
                        _buildExpandableContainer(children: [
                          subTitleWidget('Tax mode'),
                          _buildRowSwitch(
                            title: "Include Tax",
                            value: settings.includeTax,
                            onChanged: (val) => settings.updateSetting(dbKeyIncludeTax, val),
                          ),
                          _buildRowSwitch(
                            title: "Exclude Tax",
                            value: settings.excludeTax,
                            onChanged: (val) => settings.updateSetting(dbKeyExcludeTax, val),
                          ),
                          subTitleWidget('Apply Tax for'),
                          _buildRowSwitch(
                            title: "Specific Product",
                            value: settings.applyTaxSpecificProduct,
                            onChanged: (val) => settings.updateSetting(dbKeyApplyTaxSpecificProduct, val),
                          ),
                          _buildRowSwitch(
                            title: "All Products",
                            value: settings.applyTaxAllProducts,
                            onChanged: (val) => settings.updateSetting(dbKeyApplyTaxAllProducts, val),
                          ),
                        ]),
                      const SizedBox(height: 20),
                      _buildMainToggle(
                        title: "Enable Parcel Settings",
                        value: settings.isParcelEnabled,
                        onChanged: settings.toggleParcel,
                      ),
                      if (settings.isParcelEnabled)
                        _buildExpandableContainer(children: [
                          _buildRowSwitch(
                            title: "Specific Product",
                            value: settings.parcelSpecificProduct,
                            onChanged: (val) => settings.updateSetting(dbKeyParcelSpecificProduct, val),
                          ),
                          _buildRowSwitch(
                            title: "All Products",
                            value: settings.parcelAllProducts,
                            onChanged: (val) => settings.updateSetting(dbKeyParcelAllProducts, val),
                          ),
                        ]),
                    ],
                  );
                },
              ),
            ),
          );
        });
  }

  Widget _buildMainToggle(
      {required String title, required bool value, required ValueChanged<bool> onChanged}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        Switch(value: value, onChanged: onChanged, activeColor: Colors.green),
      ],
    );
  }

  Widget _buildExpandableContainer({required List<Widget> children}) {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildRowSwitch(
      {required String title, required bool value, required ValueChanged<bool> onChanged}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: const TextStyle(fontSize: 14)),
        Switch(value: value, onChanged: onChanged, activeColor: Colors.blue),
      ],
    );
  }

  Widget subTitleWidget(String text) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(text, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
    );
  }
}
