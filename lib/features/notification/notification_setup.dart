import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:path_provider/path_provider.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  Future<void> init() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (_) {},
    );

    await _createNotificationChannel();
  }

  Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'download_channel',
      'Download Notifications',
      description: 'Notifications for download progress',
      importance: Importance.low,
      showBadge: false,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<void> downloadWithNotification(String url, String fileName) async {
    final int notificationId = DateTime.now().millisecondsSinceEpoch % 100000;

    try {
      await _showNotification(
        id: notificationId,
        title: 'Downloading $fileName',
        body: 'Preparing download...',
        progress: 0,
        isOngoing: true,
      );

      final Directory? directory = await getDownloadsDirectory();
      final String savePath = '${directory?.path}/$fileName';

      await Dio().download(
        url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = ((received / total) * 100).toInt();
            _showNotification(
              id: notificationId,
              title: 'Downloading $fileName',
              body: '$progress% completed',
              progress: progress,
              isOngoing: progress < 100,
            );
          }
        },
      );

      await _showNotification(
        id: notificationId,
        title: 'Download Complete',
        body: 'Tap to open $fileName',
        progress: 100,
        isOngoing: false,
      );
    } catch (e) {
      await _showNotification(
        id: notificationId,
        title: 'Download Failed',
        body: e.toString(),
        progress: 0,
        isOngoing: false,
      );
    }
  }

  Future<void> _showNotification({
    required int id,
    String? title,
    String? body,
    required int progress,
    required bool isOngoing,
  }) async {
    final AndroidNotificationDetails android = AndroidNotificationDetails(
      'download_channel',
      'Download Progress',
      channelDescription: 'Shows file download progress',
      importance: Importance.low,
      priority: Priority.low,
      onlyAlertOnce: true,
      showProgress: true,
      maxProgress: 100,
      progress: progress,
      ongoing: isOngoing,
      autoCancel: !isOngoing,
    );

    final NotificationDetails details = NotificationDetails(android: android);
    await flutterLocalNotificationsPlugin.show(id, title, body, details);
  }
}
