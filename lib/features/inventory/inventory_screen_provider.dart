import 'dart:developer';

import 'package:coffee_cofe/database/dao/inventory_dao.dart';
import 'package:coffee_cofe/database/entities/inventory_category_dto.dart';
import 'package:coffee_cofe/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../database/app_database.dart';
import '../../widgets/common_dropdown.dart';

class InventoryScreenProvider extends ChangeNotifier {
  // List of units
  List<DropdownItem> units = [
    DropdownItem(id: 'kg', label: 'Kg'),
    DropdownItem(id: 'liter', label: 'Liter'),
    DropdownItem(id: 'no', label: 'No'),
    DropdownItem(id: 'pack', label: 'Pack'),
  ];

  DropdownItem? selectedUnit;

  // You can use this to set the selected unit from dropdown
  void onChageunit(DropdownItem? value) {
    selectedUnit = value;
    notifyListeners(); // Notify UI update
  }

  // final List<String> units = ['Kg', 'Litres', 'No', 'Pack'];
  // String? selectedUnit;
  InventoryDto? _selectedInventory;
  InventoryDto? get selectedInventory => _selectedInventory;
  // List<ProductDto> _products = [];

  InventoryDao? _inventoryDAO;

  List<InventoryDto> _inventoryList = [];
  List<InventoryDto> get inventoryList => _inventoryList;

  final TextEditingController nameController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();

  oneEdit(InventoryDto value) {
    _selectedInventory = value;
    nameController.text = _selectedInventory?.inventoyProductName ?? '';
    quantityController.text = _selectedInventory?.inventoryQunatity ?? '';
    log(selectedInventory?.inventoryUnit ?? '-');
    selectedUnit = units.firstWhereOrNull(
        (item) => item.label.toLowerCase() == _selectedInventory?.inventoryUnit?.toLowerCase());
    notifyListeners();
  }

  initFunction() async {
    await _initializeDAO();
    await loadInventories();
    notifyListeners();
  }

  Future _initializeDAO() async {
    try {
      _inventoryDAO = await AppDatabase().inventoryDao;
    } catch (e) {
      // _errorMessage = 'Failed to initialize the database: $e';
      notifyListeners();
    }
  }

  Future<bool> saveCategory() async {
    try {
      final inventory = InventoryDto(
        shopId: MyApp.shopId,
        inventoryId: selectedInventory?.inventoryId ?? const Uuid().v4(), // reuse ID if editing
        inventoyProductName: nameController.text.trim(),
        inventoryQunatity: (double.tryParse(quantityController.text.trim()) ?? 0.0).toString(),
        inventoryUnit: selectedUnit?.label,
        createdDate: selectedInventory?.createdDate ?? DateTime.now().toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
      );

      if (selectedInventory == null) {
        // Insert new
        await _inventoryDAO?.insertInventory(inventory);
      } else {
        // Update existing
        await _inventoryDAO?.updateInventory(inventory);
      }

      await loadInventories(); // Refresh list
      await clearCategoryForm();

      return true; // success
    } catch (e) {
      await clearCategoryForm();
      print('Error saving inventory: $e');
      return false; // failed
    }
  }

  clearCategoryForm() {
    quantityController.clear();
    nameController.clear();
    _selectedInventory = null;
    selectedUnit = null;
    notifyListeners();
  }

  Future loadInventories() async {
    _inventoryList.clear();
    var list = await _inventoryDAO?.getAllInventoryList();
    _inventoryList = list ?? [];

    notifyListeners();
  }

  List<InventoryDto> filteredInventoryList(String query) {
    if (query.isEmpty) {
      return _inventoryList;
    } else {
      return _inventoryList
          .where((item) => item.inventoyProductName!.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
  }
}
