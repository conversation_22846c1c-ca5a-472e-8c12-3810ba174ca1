import 'package:flutter/material.dart';
import '../../database/database_reset_utility.dart';
import '../../config/app_config.dart';

/// Debug screen for database operations - only available in development mode
class DatabaseDebugScreen extends StatefulWidget {
  const DatabaseDebugScreen({super.key});

  @override
  State<DatabaseDebugScreen> createState() => _DatabaseDebugScreenState();
}

class _DatabaseDebugScreenState extends State<DatabaseDebugScreen> {
  bool _isLoading = false;
  String _output = '';

  @override
  Widget build(BuildContext context) {
    // Only show in development mode
    if (!AppConfig.isDevelopment) {
      return Scaffold(
        appBar: AppBar(title: const Text('Debug')),
        body: const Center(
          child: Text('Debug features are only available in development mode'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Debug'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Warning card
            Card(
              color: Colors.orange.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Database Debug Tools - Use with caution!',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Action buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _checkDatabaseHealth,
                  icon: const Icon(Icons.health_and_safety),
                  label: const Text('Health Check'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _quickFix,
                  icon: const Icon(Icons.build),
                  label: const Text('Quick Fix'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _fixDatabaseIssues,
                  icon: const Icon(Icons.construction),
                  label: const Text('Fix Issues'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _exportSchema,
                  icon: const Icon(Icons.download),
                  label: const Text('Export Schema'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _resetDatabase,
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('Reset Database'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Loading indicator
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),

            // Output area
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.terminal),
                          const SizedBox(width: 8),
                          const Text(
                            'Output',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_output.isNotEmpty)
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _output = '';
                                });
                              },
                              child: const Text('Clear'),
                            ),
                        ],
                      ),
                      const Divider(),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _output.isEmpty ? 'No output yet. Run a command to see results.' : _output,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkDatabaseHealth() async {
    setState(() {
      _isLoading = true;
      _output = 'Running database health check...\n';
    });

    try {
      await DatabaseResetUtility.checkDatabaseHealth();
      setState(() {
        _output += 'Health check completed successfully!\n';
        _output += 'Check the console/logs for detailed output.\n';
      });
    } catch (e) {
      setState(() {
        _output += 'Error during health check: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _quickFix() async {
    setState(() {
      _isLoading = true;
      _output = 'Applying quick fix for WorkSpaceSettings conflict...\n';
    });

    try {
      await DatabaseResetUtility.quickFixWorkspaceSettingsConflict();
      setState(() {
        _output += 'Quick fix completed successfully!\n';
        _output += 'The WorkSpaceSettings table conflict should be resolved.\n';
      });
    } catch (e) {
      setState(() {
        _output += 'Error during quick fix: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _fixDatabaseIssues() async {
    setState(() {
      _isLoading = true;
      _output = 'Fixing database issues...\n';
    });

    try {
      await DatabaseResetUtility.fixDatabaseIssues();
      setState(() {
        _output += 'Database issues fixed successfully!\n';
      });
    } catch (e) {
      setState(() {
        _output += 'Error fixing database issues: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportSchema() async {
    setState(() {
      _isLoading = true;
      _output = 'Exporting database schema...\n';
    });

    try {
      final schema = await DatabaseResetUtility.exportDatabaseSchema();
      setState(() {
        _output += schema;
      });
    } catch (e) {
      setState(() {
        _output += 'Error exporting schema: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetDatabase() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Database'),
        content: const Text(
          'This will delete all data and recreate the database. '
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
      _output = 'Resetting database...\n';
    });

    try {
      await DatabaseResetUtility.resetDatabase();
      setState(() {
        _output += 'Database reset completed successfully!\n';
        _output += 'All tables have been recreated.\n';
      });
    } catch (e) {
      setState(() {
        _output += 'Error resetting database: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
