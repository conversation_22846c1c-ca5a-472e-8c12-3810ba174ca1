import 'package:flutter/material.dart';

import '../../models/staff_attendance.dart';

class AttendanceProvider with ChangeNotifier {
  final List<AttendanceEmployee> _employees = [
    AttendanceEmployee(
      employeeId: 'E001',
      employeeName: 'John',
      punchStatus: 0,
      punchInfo: 'Not yet check-in',
      role: 'Manager',
    ),
    AttendanceEmployee(
      employeeId: 'E002',
      employeeName: 'Alice',
      punchStatus: 1,
      punchInfo: 'Punch In 9:15 AM',
      punchInTime: const TimeOfDay(hour: 9, minute: 15),
      role: 'Developer',
    ),
    AttendanceEmployee(
      employeeId: 'E003',
      employeeName: 'Bob',
      punchStatus: 2,
      punchInfo: 'Punch Out 6:00 PM',
      punchInTime: const TimeOfDay(hour: 9, minute: 0),
      punchOutTime: const TimeOfDay(hour: 18, minute: 0),
      role: 'Support',
    ),
    AttendanceEmployee(
      employeeId: 'E004',
      employeeName: 'Carol',
      punchStatus: 0,
      punchInfo: 'Not yet check-in',
      role: 'HR',
    ),
    AttendanceEmployee(
      employeeId: 'E005',
      employeeName: 'David',
      punchStatus: 1,
      punchInfo: 'Punch In 9:45 AM',
      punchInTime: const TimeOfDay(hour: 9, minute: 45),
      role: 'Developer',
    ),
    AttendanceEmployee(
      employeeId: 'E006',
      employeeName: 'Eva',
      punchStatus: 2,
      punchInfo: 'Punch Out 5:55 PM',
      punchInTime: const TimeOfDay(hour: 9, minute: 5),
      punchOutTime: const TimeOfDay(hour: 17, minute: 55),
      role: 'Sales',
    ),
    AttendanceEmployee(
      employeeId: 'E007',
      employeeName: 'Frank',
      punchStatus: 0,
      punchInfo: 'Not yet check-in',
      role: 'Support',
    ),
    AttendanceEmployee(
      employeeId: 'E008',
      employeeName: 'Grace',
      punchStatus: 1,
      punchInfo: 'Punch In 10:00 AM',
      punchInTime: const TimeOfDay(hour: 10, minute: 0),
      role: 'Developer',
    ),
    AttendanceEmployee(
      employeeId: 'E009',
      employeeName: 'Hank',
      punchStatus: 2,
      punchInfo: 'Punch Out 6:15 PM',
      punchInTime: const TimeOfDay(hour: 9, minute: 30),
      punchOutTime: const TimeOfDay(hour: 18, minute: 15),
      role: 'Admin',
    ),
    AttendanceEmployee(
      employeeId: 'E010',
      employeeName: 'Ivy',
      punchStatus: 0,
      punchInfo: 'Not yet check-in',
      role: 'Intern',
    ),
    AttendanceEmployee(
      employeeId: 'E011',
      employeeName: 'Jack',
      punchStatus: 1,
      punchInfo: 'Punch In 9:00 AM',
      punchInTime: const TimeOfDay(hour: 9, minute: 0),
      role: 'Team Lead',
    ),
    AttendanceEmployee(
      employeeId: 'E012',
      employeeName: 'Kara',
      punchStatus: 2,
      punchInfo: 'Punch Out 5:50 PM',
      punchInTime: const TimeOfDay(hour: 8, minute: 55),
      punchOutTime: const TimeOfDay(hour: 17, minute: 50),
      role: 'Developer',
    ),
    AttendanceEmployee(
      employeeId: 'E013',
      employeeName: 'Leo',
      punchStatus: 0,
      punchInfo: 'Not yet check-in',
      role: 'Support',
    ),
    AttendanceEmployee(
      employeeId: 'E014',
      employeeName: 'Mona',
      punchStatus: 1,
      punchInfo: 'Punch In 9:30 AM',
      punchInTime: const TimeOfDay(hour: 9, minute: 30),
      role: 'HR',
    ),
    AttendanceEmployee(
      employeeId: 'E015',
      employeeName: 'Nate',
      punchStatus: 2,
      punchInfo: 'Punch Out 6:05 PM',
      punchInTime: const TimeOfDay(hour: 9, minute: 10),
      punchOutTime: const TimeOfDay(hour: 18, minute: 5),
      role: 'Marketing',
    ),
  ];

  String _search = '';
  DateTime? _reportDate;
  DateTime? get reportDate => _reportDate;

  List<AttendanceEmployee> get employees =>
      _employees.where((e) => e.employeeName!.toLowerCase().contains(_search.toLowerCase())).toList();

  void updateSearch(String query) {
    _search = query;
    notifyListeners();
  }

  void updatePunchStatus(int index, int status, String punchInfo, TimeOfDay time) {
    _employees[index].punchStatus = status;
    _employees[index].punchInfo = punchInfo;

    if (status == 1) {
      _employees[index].punchInTime = time;
    } else if (status == 2) {
      _employees[index].punchOutTime = time;
    }

    notifyListeners();
  }

  AttendanceProvider() {
    initFunction();
  }
  initFunction() {
    onChangeReportDate(DateTime.now());
  }

  void onChangeReportDate(DateTime date) {
    _reportDate = date;
    notifyListeners();
  }
}
