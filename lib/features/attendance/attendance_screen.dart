import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../../core/styles.dart';
import '../../models/staff_attendance.dart';
import '../../widgets/common_drawer.dart';
import '../../widgets/date_picker_widget.dart';
import 'attendance_provider.dart';

class AttendanceScreen extends StatelessWidget {
  const AttendanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      appBar: const CommonAppBar(title: "Staff Attendance"),
      body: Consumer<AttendanceProvider>(
        builder: (_, provider, __) {
          return Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                DateTimePicker(
                  label: 'Report Date',
                  selectedDate: provider.reportDate,
                  showDate: true,
                  showTime: false,
                  minDate: DateTime.now(),
                  maxDate: DateTime(2100),
                  onChanged: (value) {
                    provider.onChangeReportDate(value);
                  },
                ),
                const SizedBox(
                  height: 10,
                ),
                TextField(
                  decoration: InputDecoration(
                    hintText: "Search employee...",
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  onChanged: provider.updateSearch,
                ),
                Expanded(
                  child: Consumer<AttendanceProvider>(
                    builder: (context, provider, _) {
                      final employees = provider.employees;

                      if (employees.isEmpty) {
                        return const Center(child: Text("No employees found"));
                      }

                      return ListView.builder(
                        itemCount: employees.length,
                        itemBuilder: (context, index) {
                          final emp = employees[index];

                          return GestureDetector(
                              onTap: () {
                                if (emp.punchStatus! < 2) {
                                  showPunchDialogGetX(context, emp, index, provider);
                                }
                              },
                              child: Card(
                                margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                child: ListTile(
                                    leading: emp.profileImageUrl?.isNotEmpty == true
                                        ? CircleAvatar(
                                            backgroundImage: NetworkImage(emp.profileImageUrl!),
                                          )
                                        : CircleAvatar(
                                            backgroundColor: Colors.blueAccent,
                                            child: Text(
                                              emp.employeeName?.isNotEmpty == true
                                                  ? emp.employeeName![0].toUpperCase()
                                                  : "?",
                                              style: const TextStyle(color: Colors.white),
                                            ),
                                          ),
                                    title: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          emp.employeeName ?? '',
                                          style: black16w500,
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              emp.role ?? '',
                                              style: black15w500,
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 4),
                                              child: Text(
                                                '•',
                                                style: gray15w500,
                                              ),
                                            ),
                                            Text(
                                              emp.employeeId ?? '',
                                              style: black15w500,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    subtitle: (emp.punchInTime == null && emp.punchOutTime == null)
                                        ? Text(
                                            'Not yet checked in',
                                            style: gray15w500,
                                          )
                                        : Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  const Icon(Icons.login, color: Colors.green, size: 18),
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    emp.punchInTime != null
                                                        ? 'In: ${emp.punchInTime!.format(context)}'
                                                        : 'In: --',
                                                    style: TextStyle(
                                                      fontSize: 13,
                                                      color: emp.punchInTime != null
                                                          ? Colors.green
                                                          : Colors.grey,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Row(
                                                children: [
                                                  const Icon(Icons.logout, color: Colors.red, size: 18),
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    emp.punchOutTime != null
                                                        ? 'Out: ${emp.punchOutTime!.format(context)}'
                                                        : 'Out: --',
                                                    style: TextStyle(
                                                      fontSize: 13,
                                                      color:
                                                          emp.punchOutTime != null ? Colors.red : Colors.grey,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          )),
                              ));
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void showPunchDialogGetX(
    BuildContext context,
    AttendanceEmployee emp,
    int index,
    AttendanceProvider controller,
  ) {
    final now = TimeOfDay.now();
    TimeOfDay pickedTime = now;

    Get.dialog(
      AlertDialog(
        title: Text(emp.employeeName ?? ''),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                  ),
                  child: Text(
                    MaterialLocalizations.of(context).formatTimeOfDay(
                      pickedTime,
                      alwaysUse24HourFormat: false,
                    ),
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                  onPressed: () async {
                    final result = await showTimePicker(
                      context: context,
                      initialTime: pickedTime,
                      builder: (context, child) {
                        return MediaQuery(
                          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
                          child: child!,
                        );
                      },
                    );
                    if (result != null) {
                      setState(() {
                        pickedTime = result;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: emp.punchStatus == 0 ? Colors.green : Colors.red,
                  ),
                  onPressed: () {
                    final formattedTime = pickedTime.format(context);
                    final newInfo =
                        emp.punchStatus == 0 ? "Punch In $formattedTime" : "Punch Out $formattedTime";
                    final newStatus = emp.punchStatus == 0 ? 1 : 2;

                    controller.updatePunchStatus(index, newStatus, newInfo, pickedTime);
                    Get.back();
                  },
                  child: Text(
                    emp.punchStatus == 0 ? "Punch In" : "Punch Out",
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
