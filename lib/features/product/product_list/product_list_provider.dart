import 'dart:developer';

import 'package:flutter/material.dart';

import '../../../database/app_database.dart';
import '../../../database/dao/product_dao.dart';
import '../../../database/entities/product.dart';

class ProductListProvider with ChangeNotifier {
  ProductDAO? _productDAO;
  List<ProductDto> _products = [];
  bool _isLoading = false;
  String? _errorMessage;
  ProductListProvider() {
    _initializeDAO();
  }

  Future<void> _initializeDAO() async {
    try {
      _productDAO = await AppDatabase().productDao;
      await fetchProducts();
    } catch (e) {
      _errorMessage = 'Failed to initialize the database: $e';
      notifyListeners();
    }
  }

  List<ProductDto> get products => _products;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  Future<void> fetchProducts() async {
    _setLoading(true);
    try {
      if (_productDAO != null) {
        _products = await _productDAO!.getAllProducts();
        _errorMessage = null;
      } else {
        _errorMessage = 'Database not initialized';
      }
    } catch (e) {
      log(e.toString());
      _errorMessage = 'Failed to fetch products: $e';
    } finally {
      _setLoading(false);
    }
  }
}
