import 'dart:developer';

import 'package:coffee_cofe/core/styles.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/images.dart';
import '../../database/entities/inventory_category_dto.dart';
import '../../widgets/common_drawer.dart';
import '../../widgets/common_dropdown.dart';
import '../../widgets/input_form_field.dart';
import '../profile/profile_settings_controller.dart';
import 'product_provider.dart';
import 'recipe_bottomsheet_widget.dart';

class ProductScreen extends StatefulWidget {
  const ProductScreen({super.key, this.selectedIndex = -1});

  final int selectedIndex;

  @override
  _ProductScreenState createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      initFunction();
    });
  }

  initFunction() async {
    final productProvider = Provider.of<ProductProvider>(context, listen: false);

    // Reset and initialize fields
    await productProvider.resetFields();
    await productProvider.initFunction();

    // Conditionally assign values based on selected index
    if (widget.selectedIndex != -1 && widget.selectedIndex.isBlank == false) {
      await productProvider.assignTextFormFields(widget.selectedIndex);
    }

    if (mounted) {
      setState(() {}); // Call setState only if the widget is still mounted
    }
  }

  double width = 0.0;
  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
            drawer: const CommonDrawer(),
            appBar: CommonAppBar(
              title: widget.selectedIndex != -1 && widget.selectedIndex.isBlank == false
                  ? 'Update Product'
                  : 'Create Product',
              appBarColor: bgColor,
            ),
            body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                child: Consumer<ProductProvider>(builder: (context, provider, child) {
                  return SingleChildScrollView(
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 10),
                          buildFormRow(
                            "Product Name *",
                            InputFormField(
                              hint: "Enter product name",
                              controller: provider.nameController,
                              validator: (value) {
                                if (value == null || value == "") {
                                  return 'Enter a product name ';
                                } else {
                                  return null;
                                }
                              },
                            ),
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: buildFormRow(
                                  'Category',
                                  CommonDropdown(
                                    items: provider.categoriesList,
                                    selectedItem: provider.selectedCategory,
                                    onChanged: (value) {
                                      if (value != null) {
                                        provider.onChageCat(value);
                                      }
                                    },
                                    isSearchable: false,
                                    // itemLabel: (DropdownItem item) => item.label,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: IconButton(
                                    icon: const Icon(Icons.add), onPressed: () => _addCategory(provider)),
                              ),
                            ],
                          ),
                          buildFormRow(
                            "MRP $rupeeSymbol *",
                            InputFormField(
                                hint: "Enter product MRP",
                                controller: provider.priceController,
                                validator: (value) {
                                  if (value == null || value == "") {
                                    return 'Enter a product price ';
                                  } else {
                                    return null;
                                  }
                                },
                                onChanged: (value) => provider.feedCostOfProduction(),
                                inputType: const TextInputType.numberWithOptions(decimal: true)),
                          ),
                          // buildFormRow(
                          //   "GST",
                          //   InputFormField(
                          //       hint: "Enter product GST",
                          //       onChanged: (value) => provider.feedCostOfProduction(),
                          //       controller: provider.gstController,
                          //       inputType: const TextInputType.numberWithOptions(decimal: true)),
                          // ),
                          buildFormRow(
                            "Cost of Production",
                            InputFormField(
                                hint: "Cost of Production",
                                controller: provider.copController,
                                inputType: const TextInputType.numberWithOptions(decimal: true)),
                          ),
                          // buildFormRow(
                          //   "Discount",
                          //   InputFormField(
                          //       hint: "Enter Discount",
                          //       controller: provider.discountController,
                          //       inputType: const TextInputType.numberWithOptions(decimal: true)),
                          // ),
                          buildFormRow(
                            'Recipe',
                            TextButton.icon(
                              onPressed: () async {
                                // Pass existing recipe data if available
                                showInventoryBottomSheet(context, provider,
                                    existingRecipe:
                                        provider.recipeData1); // Or provide an existingRecipe for updating
                              },
                              style: TextButton.styleFrom(
                                backgroundColor: AppColors.primary, // Custom background color
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12), // Rounded corners
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16), // Add padding
                                side: const BorderSide(color: AppColors.secondary), // Optional border color
                              ),
                              label: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    provider.recipeData1.isEmpty
                                        ? 'Add Recipe'
                                        : 'Update Recipe', // Changed text to 'Update Recipe'
                                    style: black16w500.copyWith(
                                      color: AppColors.buttonText,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Icon(
                                    Icons.edit,
                                    color: AppColors.buttonText,
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          buildFormRow(
                            "Add Product Image",
                            Align(
                              alignment: Alignment.center,
                              child: InkWell(
                                onTap: () {
                                  provider.pickImage();
                                },
                                child: Container(
                                  height: 100,
                                  width: 120,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: provider.imageFile != null
                                      ? ClipRRect(
                                          borderRadius: BorderRadius.circular(10),
                                          child: Image.file(
                                            provider.imageFile!,
                                            fit: BoxFit.cover,
                                          ),
                                        )
                                      : const Icon(Icons.image_outlined, size: 40, color: Colors.grey),
                                ),
                              ),
                            ),
                          ),
                          buildFormRow(
                            "Description",
                            InputFormField(
                              hint: "Enter Description",
                              controller: provider.descriptionController,
                            ),
                          ),
                          buildFormRow(
                            "Parcel Charges $rupeeSymbol ",
                            InputFormField(
                                hint: "Enter Product Parcel Charges",
                                controller: provider.parcelAmountController,
                                inputType: const TextInputType.numberWithOptions(decimal: true)),
                          ),
                          const SizedBox(height: 10),
                          // ignore: unnecessary_null_comparison
                          widget.selectedIndex != -1 && widget.selectedIndex != null
                              ? Center(
                                  child: InkWell(
                                    onTap: () {
                                      if (_formKey.currentState!.validate()) {
                                        provider.addOrUpdateProduct(addMore: false);
                                      }
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(8.0),
                                      width: width * 0.45,
                                      height: 45,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10.0), color: bgColor),
                                      child: const Text("Update Product",
                                          style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w600)),
                                    ),
                                  ),
                                )
                              : Row(
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        if (_formKey.currentState!.validate()) {
                                          provider.addOrUpdateProduct(addMore: false);
                                        }
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(8.0),
                                        width: width * 0.45,
                                        height: 45,
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.0), color: bgColor),
                                        child: const Text("Add Product",
                                            style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.white,
                                                fontWeight: FontWeight.w600)),
                                      ),
                                    ),
                                    const Spacer(),
                                    InkWell(
                                      onTap: () {},
                                      child: Container(
                                        padding: const EdgeInsets.all(8.0),
                                        width: width * 0.45,
                                        height: 45,
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(10.0), color: bgColor),
                                        child: const Text("Add More",
                                            style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.white,
                                                fontWeight: FontWeight.w600)),
                                      ),
                                    ),
                                  ],
                                ),
                          const SizedBox(
                            height: 10,
                          )
                        ],
                      ),
                    ),
                  );
                })),
          );
        });
  }

  Widget buildFormRow(String label, Widget child) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: black16w500),
          const SizedBox(height: 2),
          child,
        ],
      ),
    );
  }

  void _addCategory(ProductProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: 20,
            bottom: MediaQuery.of(context).viewInsets.bottom + 20, // Adjust for keyboard
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputFormField(
                autoFocus: true,
                controller: provider.categoryNamwController,
                decoration: const InputDecoration(labelText: 'Category Name'),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () async {
                  if (provider.categoryNamwController.text.isNotEmpty) {
                    await provider.saveCategory();

                    Get.back();
                  }
                },
                child: const Text('Add Category'),
              ),
            ],
          ),
        );
      },
    ).whenComplete(() {
      provider.loadCategories();
      provider.onChageCat(provider.categoriesList.last);
      setState(() {});
    });
  }

  void showInventoryBottomSheet(
    BuildContext context,
    ProductProvider prov, {
    List<InventoryDto>? existingRecipe,
  }) async {
    final List<InventoryDto>? selectedRecipes = await showModalBottomSheet<List<InventoryDto>>(
      context: context,
      isScrollControlled: true,
      builder: (_) => InventoryBottomSheet(
        existingRecipe: existingRecipe,
      ),
    );
    if (selectedRecipes != null && selectedRecipes.isNotEmpty) {
      // Use the selected recipes here
      log("Selected recipes: ${selectedRecipes.map((e) => e.inventoyProductName).toList()}");
      for (var itms in selectedRecipes) {
        prov.addRecipe(itms);
      }
    } else {
      log("No recipes selected");
    }
  }
}
