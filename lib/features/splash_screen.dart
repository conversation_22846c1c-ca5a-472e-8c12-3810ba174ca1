import 'package:coffee_cofe/core/routes/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../core/constants/images.dart';
import '../utils/user_services.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      checkLoginStatus();
    });
  }

  void checkLoginStatus() async {
    bool loggedIn = await SessionManager.isLoggedIn();
    if (loggedIn) {
      Get.offAllNamed(AppRoutes.billingScreen);
    } else {
      Get.offAllNamed(AppRoutes.login);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // Or any background color
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Your Logo or Brand Image
            Image.asset(
              brandLogo, // <-- your logo path
              width: 100,
              height: 100,
            ),
            const SizedBox(height: 20),
            const Text(
              'Buy Coffee',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 40),
            const CircularProgressIndicator(
              color: Colors.orange,
              strokeWidth: 2,
            ),
          ],
        ),
      ),
    );
  }
}
