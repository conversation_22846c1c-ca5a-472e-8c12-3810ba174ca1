# Mock Login System Test Guide

## ✅ System Status: READY

The mock login system has been successfully implemented and is ready for testing.

## 🎯 Quick Test Instructions

### 1. **Enable Mock Mode**
The system is already configured for mock authentication:
- `AppConfig.useMockAuth = true` ✅
- `AppConfig.showMockUserList = true` ✅

### 2. **Available Test Users**

| User | Email | Mobile | Password | Role |
|------|-------|--------|----------|------|
| <PERSON> | <EMAIL> | ********** | 123456 | admin |
| <PERSON> | <EMAIL> | 9876543210 | password | user |
| Admin User | <EMAIL> | 5555555555 | admin123 | admin |
| Test User | <EMAIL> | 1111111111 | test | user |

### 3. **Test Login Flow**

1. **Open the app** and navigate to login screen
2. **See demo users card** showing available test credentials
3. **Tap any credential** to copy to clipboard
4. **Paste and login** with the copied credentials
5. **Verify success** - should navigate to billing screen

### 4. **Test Registration Flow**

1. Go to **Sign Up** screen
2. Enter new user details:
   - Name: "New User"
   - Email: "<EMAIL>"
   - Mobile: "**********"
   - Password: "newpass"
3. **Register** - should create account and login automatically
4. **Verify** - should navigate to billing screen

## 🔧 Configuration

### Current Settings (`lib/config/app_config.dart`):
```dart
static const bool useMockAuth = true;        // ✅ Mock enabled
static const bool enableDebugMode = true;    // ✅ Debug enabled
static const bool showMockUserList = true;   // ✅ Demo users shown
```

### To Switch to Real API:
```dart
static const bool useMockAuth = false;       // 🔄 Use real API
static const bool showMockUserList = false;  // 🔄 Hide demo users
```

## 📱 UI Features

### Login Screen Enhancements:
- ✅ **Demo Users Card**: Shows all available test users
- ✅ **Copy to Clipboard**: Tap credentials to copy
- ✅ **Role Indicators**: Color-coded user roles
- ✅ **Development Badge**: Shows "DEMO" for test users

### Mock Users Demo Widget:
- ✅ **Auto-hide in Production**: Only visible when `useMockAuth = true`
- ✅ **User Cards**: Each user shown with avatar and credentials
- ✅ **Copy Functionality**: Tap to copy email/mobile and password
- ✅ **Warning Message**: Reminds this is development mode

## 🚀 Testing Scenarios

### ✅ Valid Login Tests:
- Login with email: `<EMAIL>` / `123456`
- Login with mobile: `**********` / `123456`
- Login with different users and verify role differences

### ✅ Invalid Login Tests:
- Wrong password: Should show "Invalid credentials" error
- Non-existent user: Should show "Invalid credentials" error
- Empty fields: Should show validation errors

### ✅ Registration Tests:
- New user registration: Should create account successfully
- Duplicate email: Should show "User already exists" error
- Invalid email format: Should show validation error

### ✅ Network Simulation:
- 1-second delay simulates real network requests
- Loading states work correctly
- Error handling works as expected

## 🔍 Debug Features

### Print Mock Users:
```dart
MockAuthService.printMockUsers();
```
Output:
```
=== Mock Users Database ===
1. John Doe (<EMAIL>) - Password: 123456
2. Jane Smith (<EMAIL>) - Password: password
3. Admin User (<EMAIL>) - Password: admin123
4. Test User (<EMAIL>) - Password: test
========================
```

### Print App Config:
```dart
AppConfig.printConfig();
```
Output:
```
=== App Configuration ===
Environment: development
Base URL: https://dev-api.coffeepos.com/v1
Use Mock Auth: true
Debug Mode: true
App Version: 1.0.0
========================
```

## 📁 Implementation Files

### Core Files:
- ✅ `lib/config/app_config.dart` - Configuration settings
- ✅ `lib/services/mock_auth_service.dart` - Mock authentication service
- ✅ `lib/repositories/auth_repository.dart` - Repository with mock/real switching
- ✅ `lib/features/auth/mock_users_demo.dart` - Demo users widget

### Updated Files:
- ✅ `lib/features/auth/login_screen.dart` - Shows mock users demo
- ✅ `lib/features/auth/login_provider.dart` - Uses repository
- ✅ `lib/features/auth/signup/signup_provider.dart` - Uses repository

## 🔄 Easy Replacement Process

### Step 1: Update Configuration
```dart
// lib/config/app_config.dart
static const bool useMockAuth = false;  // Switch to real API
```

### Step 2: Update API Base URL
```dart
// lib/core/network/api_endpoints.dart
static const String baseUrl = 'https://your-production-api.com/api/v1';
```

### Step 3: Test
- All existing UI code remains unchanged
- Repository automatically switches to DioClient
- Mock users demo automatically hides

## ✅ Benefits Achieved

1. **Zero Backend Dependency**: Test authentication without server
2. **Realistic User Experience**: 1-second network delay simulation
3. **Multiple Test Scenarios**: 4 different user types and roles
4. **Easy Switching**: One config change to use real API
5. **Developer Friendly**: Copy-paste credentials, debug functions
6. **Production Ready**: Auto-hides in production builds

## 🎉 Status: COMPLETE

The mock login system is fully functional and ready for:
- ✅ Development and testing
- ✅ Demonstrations and presentations  
- ✅ UI/UX development without backend
- ✅ Easy migration to real API when ready

**Next Steps**: Start developing other features using the authenticated user context, knowing that switching to real API will be seamless!
