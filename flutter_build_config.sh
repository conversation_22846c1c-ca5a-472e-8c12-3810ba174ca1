#!/bin/bash

# ========================================
# FLUTTER BUILD OPTIMIZATION SCRIPT
# ========================================

echo "🚀 Starting Flutter Build Optimization..."

# ========================================
# ENVIRONMENT SETUP
# ========================================

# Set Flutter build mode
export FLUTTER_BUILD_MODE=debug

# Optimize Dart VM settings
export DART_VM_OPTIONS="--old_gen_heap_size=2048 --enable-asserts"

# Set Android build optimization flags
export GRADLE_OPTS="-Xmx4096m -XX:+UseG1GC -XX:+UseStringDeduplication -Dorg.gradle.daemon=true -Dorg.gradle.parallel=true -Dorg.gradle.caching=true"

# ========================================
# PRE-BUILD CLEANUP (Optional)
# ========================================

cleanup_build() {
    echo "🧹 Cleaning build cache..."
    flutter clean
    rm -rf android/build
    rm -rf android/.gradle
    rm -rf build
    rm -rf .dart_tool
    echo "✅ Cleanup completed"
}

# ========================================
# DEPENDENCY OPTIMIZATION
# ========================================

optimize_dependencies() {
    echo "📦 Optimizing dependencies..."
    flutter pub get
    flutter pub deps
    echo "✅ Dependencies optimized"
}

# ========================================
# BUILD FUNCTIONS
# ========================================

# Fast debug build
build_debug_fast() {
    echo "🔨 Building debug APK (optimized)..."
    flutter build apk \
        --debug \
        --target-platform android-arm64 \
        --split-per-abi \
        --tree-shake-icons \
        --dart-define=flutter.inspector.structuredErrors=true
}

# Release build with optimizations
build_release_optimized() {
    echo "🔨 Building release APK (fully optimized)..."
    flutter build apk \
        --release \
        --target-platform android-arm64 \
        --split-per-abi \
        --tree-shake-icons \
        --obfuscate \
        --split-debug-info=build/debug-info
}

# ========================================
# MAIN EXECUTION
# ========================================

case "$1" in
    "clean")
        cleanup_build
        ;;
    "deps")
        optimize_dependencies
        ;;
    "debug")
        build_debug_fast
        ;;
    "release")
        build_release_optimized
        ;;
    "full")
        cleanup_build
        optimize_dependencies
        build_debug_fast
        ;;
    *)
        echo "Usage: $0 {clean|deps|debug|release|full}"
        echo ""
        echo "Commands:"
        echo "  clean   - Clean all build cache"
        echo "  deps    - Optimize dependencies"
        echo "  debug   - Fast debug build"
        echo "  release - Optimized release build"
        echo "  full    - Complete optimized build process"
        exit 1
        ;;
esac

echo "✅ Build optimization completed!"
