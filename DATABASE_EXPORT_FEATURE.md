# Database Export Feature Documentation

## Overview
Added comprehensive SQLite schema export functionality with date information to the Coffee POS application. This feature allows users to export database schemas with or without data, manage exported files, and maintain database backups.

## Features Added

### 1. Database Export Utility (`lib/database/database_export_utility.dart`)
A comprehensive utility class that provides:

#### Core Export Functions
- **`exportSchemaWithDate()`** - Main export function with date stamping
- **`exportSchemaOnly()`** - Export table structures only (no data)
- **`exportSchemaWithData()`** - Export complete database with all data
- **`getExportedFiles()`** - List all previously exported files
- **`deleteExportedFile()`** - Delete specific export files

#### Export Features
- **Date-stamped filenames** - Automatic date/time in filename (YYYY-MM-DD_HH-MM-SS)
- **Custom filenames** - Option to specify custom export filename
- **Comprehensive metadata** - Export includes database version, table count, export date
- **Table information** - Column details, types, constraints, primary keys
- **Data export** - Optional inclusion of all table data as INSERT statements
- **Error handling** - Robust error handling with detailed logging

### 2. Database Export Screen (`lib/screens/database_export_screen.dart`)
A user-friendly interface for managing database exports:

#### UI Features
- **Export Options Card** - Clean interface for export configuration
- **Custom Filename Input** - Optional custom naming for exports
- **Two Export Modes**:
  - Schema Only (blue button) - Structure without data
  - Schema + Data (green button) - Complete database backup
- **Progress Indicator** - Shows export progress
- **File Management** - List of all exported files with details
- **File Operations** - Copy path to clipboard, delete files
- **Success/Error Feedback** - Clear user feedback with snackbars

#### File List Features
- **File Details** - Shows filename, size, modification date
- **Sorted by Date** - Newest files first
- **Context Menu** - Copy path, delete options
- **Empty State** - Friendly message when no exports exist

### 3. Navigation Integration
Updated the main drawer (`lib/widgets/common_drawer.dart`) to include:
- **Database submenu** - Organized database-related features
- **Export Schema option** - Direct access to new export functionality
- **Maintained existing** - Database List and Export DB File options

## Fixed Issues

### Workspace Database Schema Issue
Fixed critical database schema mismatch in workspace functionality:

#### Problem
- `WorkSpaceSettings` table used `settingId` as primary key
- `WorkspaceSettingsDto` was mapping to `'id'` field
- Caused "table WorkSpaceSettings has no column named id" error

#### Solution
- Updated `WorkspaceSettingsDto.toMap()` to use correct column name (`wSSettingId`)
- Updated `WorkspaceSettingsDto.fromMap()` to read from correct column
- Fixed all DAO operations to use proper column references
- Maintained backward compatibility

## Usage Examples

### Programmatic Usage
```dart
// Export schema only with automatic date filename
final schemaPath = await DatabaseExportUtility.exportSchemaOnly();

// Export with custom filename
final customPath = await DatabaseExportUtility.exportSchemaOnly(
  customFileName: 'my_backup.sql'
);

// Export complete database with data
final fullPath = await DatabaseExportUtility.exportSchemaWithData();

// Get list of exported files
final files = await DatabaseExportUtility.getExportedFiles();

// Delete an export file
final deleted = await DatabaseExportUtility.deleteExportedFile(filePath);
```

### UI Usage
1. **Access via Drawer**: Database → Export Schema
2. **Choose Export Type**: Schema Only or Schema + Data
3. **Optional Custom Name**: Enter custom filename
4. **Export**: Tap appropriate export button
5. **Manage Files**: View, copy path, or delete exported files

## Export File Format

### Header Information
```sql
-- Coffee POS Database Schema Export
-- Generated on: 2024-01-15T10:30:45.123Z
-- Export Date: 2024-01-15_10-30-45
-- Include Data: true/false
-- Database Version: 2
-- Total Tables: 11
```

### Table Structure
```sql
-- ----------------------------------------
-- Table: Products
-- ----------------------------------------
CREATE TABLE Products (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  shopId TEXT NOT NULL,
  productId TEXT UNIQUE NOT NULL,
  -- ... more columns
);

-- Table Info:
-- Column: id | Type: INTEGER | NotNull: 0 | Default: null | PK: 1
-- Column: shopId | Type: TEXT | NotNull: 1 | Default: null | PK: 0
-- ... more column info
```

### Data (if included)
```sql
-- Data for table Products (5 rows)
INSERT INTO Products (id, shopId, productId, productName) VALUES (1, 'shop1', 'prod1', 'Coffee');
INSERT INTO Products (id, shopId, productId, productName) VALUES (2, 'shop1', 'prod2', 'Tea');
-- ... more data
```

## File Storage

### Export Directory
- **Android/iOS**: `Application Documents/database_exports/`
- **Desktop**: `Application Documents/database_exports/`
- **Auto-created**: Directory created automatically if it doesn't exist

### Filename Patterns
- **Auto-generated**: `coffee_pos_schema_YYYY-MM-DD_HH-MM-SS.sql`
- **Custom**: `{custom_name}.sql`
- **Date format**: ISO-like format for easy sorting

## Testing

### Test Coverage
- **Unit Tests**: `test/database_export_test.dart`
- **Integration Tests**: `test/workspace_integration_test.dart`
- **Test Scenarios**:
  - Schema-only export
  - Schema with data export
  - Custom filename handling
  - Date-based filename generation
  - File management operations
  - Error handling

### Test Results
- ✅ All workspace integration tests pass
- ✅ All export functionality tests pass
- ✅ Database schema fixes verified

## Benefits

### For Users
1. **Database Backup** - Easy backup of database structure and data
2. **Migration Support** - Export for database migration purposes
3. **Debugging Aid** - Schema inspection for troubleshooting
4. **Version Control** - Track database changes over time
5. **Data Recovery** - Backup data for recovery scenarios

### For Developers
1. **Schema Documentation** - Automatic schema documentation
2. **Development Aid** - Easy schema inspection during development
3. **Testing Support** - Export test data for debugging
4. **Migration Scripts** - Generate migration-ready SQL files
5. **Database Analysis** - Analyze database structure and content

## Future Enhancements

### Potential Improvements
1. **Import Functionality** - Import exported schema files
2. **Selective Export** - Export specific tables only
3. **Compression** - ZIP compression for large exports
4. **Cloud Backup** - Upload exports to cloud storage
5. **Scheduled Exports** - Automatic periodic exports
6. **Email Export** - Send exports via email
7. **Export Templates** - Predefined export configurations

### Technical Enhancements
1. **Streaming Export** - Handle very large databases
2. **Progress Callbacks** - Detailed progress reporting
3. **Export Validation** - Verify export integrity
4. **Incremental Export** - Export only changes since last export
5. **Export Encryption** - Encrypt sensitive export files

## Conclusion

The database export feature provides a comprehensive solution for database backup, migration, and analysis needs. It combines powerful programmatic functionality with an intuitive user interface, making database management accessible to both technical and non-technical users.

The feature is production-ready, well-tested, and follows the existing codebase patterns and preferences.
