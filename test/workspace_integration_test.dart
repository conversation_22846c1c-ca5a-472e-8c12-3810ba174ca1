import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/models/user_model.dart';
import 'package:coffee_cofe/services/mock_auth_service.dart';

void main() {
  group('Workspace Integration Tests', () {
    test('User model should include workspace fields', () {
      final user = User(
        userId: 'test_001',
        userName: 'Test User',
        emailAddress: '<EMAIL>',
        mobileNo: '1234567890',
        token: 'test_token',
        workspaceId: 'ws_001',
        workspaceName: 'Test Workspace',
        role: 'admin',
      );

      expect(user.workspaceId, equals('ws_001'));
      expect(user.workspaceName, equals('Test Workspace'));
      expect(user.role, equals('admin'));
    });

    test('User.fromJson should parse workspace fields correctly', () {
      final json = {
        'user_id': 'test_001',
        'user_name': 'Test User',
        'email_address': '<EMAIL>',
        'mobile_no': '1234567890',
        'token': 'test_token',
        'workspace_id': 'ws_001',
        'workspace_name': 'Test Workspace',
        'role': 'admin',
      };

      final user = User.fromJson(json);

      expect(user.userId, equals('test_001'));
      expect(user.userName, equals('Test User'));
      expect(user.workspaceId, equals('ws_001'));
      expect(user.workspaceName, equals('Test Workspace'));
      expect(user.role, equals('admin'));
    });

    test('User.fromJson should handle alternative workspace field names', () {
      final json = {
        'user_id': 'test_001',
        'user_name': 'Test User',
        'workspaceId': 'ws_002', // Alternative field name
        'workspaceName': 'Alternative Workspace', // Alternative field name
        'role': 'user',
      };

      final user = User.fromJson(json);

      expect(user.workspaceId, equals('ws_002'));
      expect(user.workspaceName, equals('Alternative Workspace'));
      expect(user.role, equals('user'));
    });

    test('MockAuthService login should return workspace information', () async {
      final response = await MockAuthService.login(
        identifier: '<EMAIL>',
        password: '123456',
      );

      expect(response.success, isTrue);
      expect(response.data?.response?.workspaceId, isNotNull);
      expect(response.data?.response?.workspaceName, isNotNull);
      expect(response.data?.response?.role, isNotNull);
    });

    test('MockAuthService register should return workspace information', () async {
      final response = await MockAuthService.register(
        userName: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '9999999999',
      );

      expect(response.success, isTrue);
      expect(response.data?.response?.workspaceId, isNotNull);
      expect(response.data?.response?.workspaceName, isNotNull);
      expect(response.data?.response?.role, isNotNull);
    });

    test('Workspace model should be created with correct data', () {
      final workspace = Workspace(
        userRole: 'admin',
        workspaceId: 'ws_001',
        workspaceName: 'Test Workspace',
        workspaceUserId: 'user_001',
        workspaceUserName: 'Test User',
      );

      expect(workspace.userRole, equals('admin'));
      expect(workspace.workspaceId, equals('ws_001'));
      expect(workspace.workspaceName, equals('Test Workspace'));
      expect(workspace.workspaceUserId, equals('user_001'));
      expect(workspace.workspaceUserName, equals('Test User'));
    });
  });
}
