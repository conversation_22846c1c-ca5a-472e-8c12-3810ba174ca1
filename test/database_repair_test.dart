import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/database/database_repair_utility.dart';
import 'package:coffee_cofe/database/app_database.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  group('Database Repair Tests', () {
    setUpAll(() {
      // Initialize FFI for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    test('Check database health', () async {
      // Check current database health
      await DatabaseRepairUtility.printHealthReport();
      
      final missingColumns = await DatabaseRepairUtility.checkDatabaseHealth();
      
      // Print results
      print('Missing columns found: ${missingColumns.length} tables affected');
      missingColumns.forEach((table, columns) {
        print('Table $table missing: ${columns.join(', ')}');
      });
    });

    test('Repair database', () async {
      // Run complete database repair
      await DatabaseRepairUtility.runCompleteRepair();
      
      // Check health after repair
      print('\n--- After Repair ---');
      await DatabaseRepairUtility.printHealthReport();
      
      final missingColumnsAfter = await DatabaseRepairUtility.checkDatabaseHealth();
      
      // Verify repair was successful
      expect(missingColumnsAfter.isEmpty, true, 
        reason: 'Database should have no missing columns after repair');
    });

    test('Test Sales table columns', () async {
      final db = await AppDatabase().database;
      
      // Test that we can query the Sales table with new columns
      try {
        final result = await db.rawQuery('''
          SELECT 
            id, salesId, totalAmount, subtotalAmount, 
            cgstAmount, sgstAmount, igstAmount, cessAmount, 
            totalTaxAmount, isReverseCharge, stateCode, taxConfiguration
          FROM Sales 
          LIMIT 1
        ''');
        
        print('Sales table query successful: ${result.length} rows');
        expect(true, true); // Test passes if no exception
      } catch (e) {
        fail('Sales table query failed: $e');
      }
    });

    test('Test Discount table columns', () async {
      final db = await AppDatabase().database;
      
      // Test that we can query the Discount table with new columns
      try {
        final result = await db.rawQuery('''
          SELECT 
            id, discountID, discountCouponName, priority,
            discountType, couponCode, maxUsageCount, currentUsageCount,
            minOrderAmount, maxDiscountAmount, isStackable
          FROM Discount 
          ORDER BY priority DESC, discountCreatedDate DESC
          LIMIT 1
        ''');
        
        print('Discount table query successful: ${result.length} rows');
        expect(true, true); // Test passes if no exception
      } catch (e) {
        fail('Discount table query failed: $e');
      }
    });

    test('Test SalesTransaction table columns', () async {
      final db = await AppDatabase().database;
      
      // Test that we can query the SalesTransaction table with new columns
      try {
        final result = await db.rawQuery('''
          SELECT 
            id, salesId, productId, productAmount, baseAmount,
            cgst, sgst, igst, cess, gstRate, cessRate,
            hsnCode, taxableAmount, totalTaxAmount, customTaxApplied
          FROM salesTransaction 
          LIMIT 1
        ''');
        
        print('SalesTransaction table query successful: ${result.length} rows');
        expect(true, true); // Test passes if no exception
      } catch (e) {
        fail('SalesTransaction table query failed: $e');
      }
    });
  });
}
