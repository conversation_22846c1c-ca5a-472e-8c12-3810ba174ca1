import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/services/simple_gst_service.dart';
import 'package:coffee_cofe/database/entities/sales_transaction_dto.dart';
import 'package:coffee_cofe/features/settings/settings_provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  group('Simple GST Service Tests', () {
    late SimpleGSTService gstService;
    late SettingsProvider settingsProvider;

    setUpAll(() {
      // Initialize FFI for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() {
      gstService = SimpleGSTService();
      settingsProvider = SettingsProvider();
      settingsProvider.isTaxEnabled = true;
      settingsProvider.applyTaxAllProducts = true;
    });

    test('Calculate GST for single item with 18% rate', () async {
      final cartItems = [
        SalesTransactionDto(
          productId: 'product1',
          productName: 'Coffee',
          productAmount: 100.0,
          productQty: 1,
          cgst: 9.0,
          sgst: 9.0,
        ),
      ];

      final result = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0,
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 100.0);
      expect(result.taxableAmount, 100.0);
      expect(result.totalGST, 18.0);
      expect(result.cgst, 9.0);
      expect(result.sgst, 9.0);
      expect(result.grandTotal, 118.0);
      expect(result.hasGST, true);
      expect(result.gstItems.length, 1);
      expect(result.gstItems.first.gstRate, 18.0);
    });

    test('Calculate GST with discount applied', () async {
      final cartItems = [
        SalesTransactionDto(
          productId: 'product1',
          productName: 'Coffee',
          productAmount: 100.0,
          productQty: 1,
          cgst: 9.0,
          sgst: 9.0,
        ),
      ];

      final result = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 10.0,
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 100.0);
      expect(result.discountAmount, 10.0);
      expect(result.taxableAmount, 90.0);
      expect(result.totalGST, 16.2); // 18% of 90
      expect(result.grandTotal, 106.2); // 90 + 16.2
    });

    test('Calculate GST for multiple items', () async {
      final cartItems = [
        SalesTransactionDto(
          productId: 'product1',
          productName: 'Coffee',
          productAmount: 100.0,
          productQty: 1,
          cgst: 9.0,
          sgst: 9.0,
        ),
        SalesTransactionDto(
          productId: 'product2',
          productName: 'Tea',
          productAmount: 50.0,
          productQty: 1,
          cgst: 2.5,
          sgst: 2.5,
        ),
      ];

      final result = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0,
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 150.0);
      expect(result.taxableAmount, 150.0);
      expect(result.totalGST, 20.5); // 18 + 2.5
      expect(result.grandTotal, 170.5);
      expect(result.gstItems.length, 2);
    });

    test('Calculate GST when tax is disabled', () async {
      settingsProvider.isTaxEnabled = false;

      final cartItems = [
        SalesTransactionDto(
          productId: 'product1',
          productName: 'Coffee',
          productAmount: 100.0,
          productQty: 1,
          cgst: 9.0,
          sgst: 9.0,
        ),
      ];

      final result = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0,
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 100.0);
      expect(result.totalGST, 0.0);
      expect(result.grandTotal, 100.0);
      expect(result.hasGST, false);
      expect(result.gstItems.isEmpty, true);
    });

    test('Calculate GST with zero amount items', () async {
      final cartItems = [
        SalesTransactionDto(
          productId: 'product1',
          productName: 'Free Sample',
          productAmount: 0.0,
          productQty: 1,
          cgst: 9.0,
          sgst: 9.0,
        ),
      ];

      final result = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0,
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 0.0);
      expect(result.totalGST, 0.0);
      expect(result.grandTotal, 0.0);
    });

    test('Format GST amount correctly', () {
      final formatted = gstService.formatGSTAmount(123.456);
      expect(formatted, '₹123.46');
    });

    test('Get GST summary for zero GST', () {
      final result = SimpleGSTResult(
        subtotal: 100.0,
        discountAmount: 0.0,
        taxableAmount: 100.0,
        totalGST: 0.0,
        grandTotal: 100.0,
        gstItems: [],
      );

      final summary = gstService.getGSTSummary(result);
      expect(summary, 'No GST applicable');
    });

    test('Get GST summary with GST', () {
      final result = SimpleGSTResult(
        subtotal: 100.0,
        discountAmount: 0.0,
        taxableAmount: 100.0,
        totalGST: 18.0,
        grandTotal: 118.0,
        gstItems: [],
      );

      final summary = gstService.getGSTSummary(result);
      expect(summary, 'Total GST: ₹18.00');
    });

    test('Calculate product GST correctly', () {
      final gst = gstService.calculateProductGST(
        productAmount: 100.0,
        gstRate: 18.0,
      );
      expect(gst, 18.0);
    });

    test('Get CGST/SGST breakdown', () {
      final breakdown = gstService.getCGSTSGSTBreakdown(18.0);
      expect(breakdown['cgst'], 9.0);
      expect(breakdown['sgst'], 9.0);
    });

    test('SimpleGSTResult properties work correctly', () {
      final result = SimpleGSTResult(
        subtotal: 100.0,
        discountAmount: 10.0,
        taxableAmount: 90.0,
        totalGST: 16.2,
        grandTotal: 106.2,
        gstItems: [
          SimpleGSTItem(
            productName: 'Coffee',
            gstRate: 18.0,
            taxableAmount: 90.0,
            gstAmount: 16.2,
          ),
        ],
      );

      expect(result.cgst, 8.1);
      expect(result.sgst, 8.1);
      expect(result.hasGST, true);
      expect(result.averageGSTRate, 18.0);
    });

    test('SimpleGSTItem properties work correctly', () {
      final item = SimpleGSTItem(
        productName: 'Coffee',
        gstRate: 18.0,
        taxableAmount: 100.0,
        gstAmount: 18.0,
      );

      expect(item.cgst, 9.0);
      expect(item.sgst, 9.0);
      expect(item.toString().contains('Coffee'), true);
      expect(item.toString().contains('18.0%'), true);
    });

    test('SimpleGSTConfig serialization works', () {
      final config = SimpleGSTConfig(
        businessName: 'Test Business',
        gstin: '22AAAAA0000A1Z5',
        defaultGSTRate: 18.0,
        enableGST: true,
      );

      final map = config.toMap();
      expect(map['businessName'], 'Test Business');
      expect(map['gstin'], '22AAAAA0000A1Z5');
      expect(map['defaultGSTRate'], 18.0);
      expect(map['enableGST'], 1);

      final configFromMap = SimpleGSTConfig.fromMap(map);
      expect(configFromMap.businessName, 'Test Business');
      expect(configFromMap.gstin, '22AAAAA0000A1Z5');
      expect(configFromMap.defaultGSTRate, 18.0);
      expect(configFromMap.enableGST, true);
    });
  });

  group('Simple GST Edge Cases', () {
    late SimpleGSTService gstService;
    late SettingsProvider settingsProvider;

    setUp(() {
      gstService = SimpleGSTService();
      settingsProvider = SettingsProvider();
      settingsProvider.isTaxEnabled = true;
      settingsProvider.applyTaxAllProducts = true;
    });

    test('Handle empty cart items', () async {
      final result = await gstService.calculateGST(
        cartItems: [],
        discountAmount: 0,
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 0.0);
      expect(result.totalGST, 0.0);
      expect(result.grandTotal, 0.0);
      expect(result.gstItems.isEmpty, true);
    });

    test('Handle discount greater than subtotal', () async {
      final cartItems = [
        SalesTransactionDto(
          productId: 'product1',
          productName: 'Coffee',
          productAmount: 50.0,
          productQty: 1,
          cgst: 9.0,
          sgst: 9.0,
        ),
      ];

      final result = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 100.0, // Greater than subtotal
        settingsProvider: settingsProvider,
      );

      expect(result.subtotal, 50.0);
      expect(result.discountAmount, 100.0);
      expect(result.taxableAmount, -50.0);
      expect(result.grandTotal, -50.0); // This might need business logic adjustment
    });
  });
}
