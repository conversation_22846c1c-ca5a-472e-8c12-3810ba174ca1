import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:coffee_cofe/screens/tax_settings_screen.dart';
import 'package:coffee_cofe/screens/discount_management_screen.dart';
import 'package:coffee_cofe/screens/create_discount_screen.dart';
import 'package:coffee_cofe/screens/add_product_screen.dart';
import 'package:coffee_cofe/features/settings/settings_provider.dart';

void main() {
  group('Screen Build Tests', () {
    Widget createTestWidget(Widget child) {
      return MaterialApp(
        home: ChangeNotifierProvider(
          create: (context) => SettingsProvider(),
          child: child,
        ),
      );
    }

    testWidgets('TaxSettingsScreen builds and has key components', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const TaxSettingsScreen()));

      // Verify the screen builds
      expect(find.byType(TaxSettingsScreen), findsOneWidget);

      // Verify key elements are present
      expect(find.text('Enable Tax'), findsOneWidget);
      expect(find.text('Mode of Tax'), findsOneWidget);
      expect(find.text('Apply Tax For'), findsOneWidget);
      expect(find.text('GST (%)'), findsOneWidget);
      expect(find.text('Update'), findsOneWidget);

      // Verify radio buttons
      expect(find.text('Include Tax'), findsOneWidget);
      expect(find.text('Exclude Tax'), findsOneWidget);
      expect(find.text('All Products'), findsOneWidget);
      expect(find.text('Specific Products'), findsOneWidget);
    });

    testWidgets('DiscountManagementScreen builds and has table headers', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const DiscountManagementScreen()));

      // Verify the screen builds
      expect(find.byType(DiscountManagementScreen), findsOneWidget);

      // Verify header elements
      expect(find.text('Mode'), findsOneWidget);
      expect(find.text('Min'), findsOneWidget);
      expect(find.text('Max'), findsOneWidget);
    });

    testWidgets('CreateDiscountScreen builds and has form elements', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const CreateDiscountScreen()));

      // Verify the screen builds
      expect(find.byType(CreateDiscountScreen), findsOneWidget);

      // Verify form elements
      expect(find.text('Discount Type'), findsOneWidget);
      expect(find.text('Discount Mode'), findsOneWidget);
      expect(find.text('Discount On'), findsOneWidget);
    });

    testWidgets('AddProductScreen builds and has form fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const AddProductScreen()));

      // Verify the screen builds
      expect(find.byType(AddProductScreen), findsOneWidget);

      // Verify form elements
      expect(find.text('Product Id'), findsOneWidget);
      expect(find.text('Product Name'), findsOneWidget);
      expect(find.text('Category'), findsOneWidget);
      expect(find.text('Price'), findsOneWidget);
      expect(find.text('Add Tax'), findsOneWidget);
      expect(find.text('Add'), findsOneWidget);
      expect(find.text('More'), findsOneWidget);
    });

    testWidgets('All screens can be instantiated', (WidgetTester tester) async {
      // Test that all screens can be created without errors
      expect(() => const TaxSettingsScreen(), returnsNormally);
      expect(() => const DiscountManagementScreen(), returnsNormally);
      expect(() => const CreateDiscountScreen(), returnsNormally);
      expect(() => const AddProductScreen(), returnsNormally);
    });

    testWidgets('Tax Settings form interactions work', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const TaxSettingsScreen()));

      // Test switch toggle
      final switchFinder = find.byType(Switch);
      expect(switchFinder, findsOneWidget);

      // Test radio button interactions
      final includeRadio = find.byWidgetPredicate(
        (widget) => widget is Radio<String> && widget.value == 'Include Tax',
      );
      expect(includeRadio, findsOneWidget);

      await tester.tap(includeRadio);
      await tester.pump();

      // Test GST input field
      final gstField = find.byWidgetPredicate(
        (widget) => widget is TextFormField,
      );
      expect(gstField, findsAtLeastNWidgets(1));
    });

    testWidgets('Create Discount form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const CreateDiscountScreen()));

      // Try to submit empty form
      final createButton = find.text('Create Discount');
      expect(createButton, findsOneWidget);

      await tester.tap(createButton);
      await tester.pump();

      // Form should show validation errors for required fields
      // The form validation will prevent submission
    });

    testWidgets('Add Product form interactions work', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const AddProductScreen()));

      // Test checkbox interaction
      final addTaxCheckbox = find.byType(Checkbox);
      expect(addTaxCheckbox, findsOneWidget);

      await tester.tap(addTaxCheckbox);
      await tester.pump();

      // Test category selection
      final categoryField = find.text('None');
      expect(categoryField, findsOneWidget);

      await tester.tap(categoryField);
      await tester.pumpAndSettle();

      // Category dialog should appear
      expect(find.text('Select Category'), findsOneWidget);
    });

    testWidgets('All screens have proper AppBar styling', (WidgetTester tester) async {
      final screens = [
        const TaxSettingsScreen(),
        const DiscountManagementScreen(),
        const CreateDiscountScreen(),
        const AddProductScreen(),
      ];

      for (final screen in screens) {
        await tester.pumpWidget(createTestWidget(screen));

        // Verify AppBar exists and has purple color
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.backgroundColor, const Color(0xFF6A1B9A));
        expect(appBar.foregroundColor, Colors.white);

        // Verify back button exists
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      }
    });

    testWidgets('All screens have consistent styling', (WidgetTester tester) async {
      final screens = [
        const TaxSettingsScreen(),
        const DiscountManagementScreen(),
        const CreateDiscountScreen(),
        const AddProductScreen(),
      ];

      for (final screen in screens) {
        await tester.pumpWidget(createTestWidget(screen));

        // Verify background color
        final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
        expect(scaffold.backgroundColor, Colors.grey[50]);

        // Verify padding is applied
        expect(
            find.byWidgetPredicate(
              (widget) => widget is Padding && widget.padding == const EdgeInsets.all(16.0),
            ),
            findsAtLeastNWidgets(1));
      }
    });
  });
}
