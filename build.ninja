# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.25

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: runner
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/Desktop/untitled$ folder/coffee_cofe/
# =============================================================================
# Object build statements for EXECUTABLE target coffee_cofe


#############################################
# Order-only phony target for coffee_cofe

build cmake_object_order_depends_target_coffee_cofe: phony || cmake_object_order_depends_target_file_selector_linux_plugin cmake_object_order_depends_target_url_launcher_linux_plugin flutter/flutter_assemble

build CMakeFiles/coffee_cofe.dir/main.cc.o: CXX_COMPILER__coffee_cofe_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/main.cc || cmake_object_order_depends_target_coffee_cofe
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DNDEBUG
  DEP_FILE = CMakeFiles/coffee_cofe.dir/main.cc.o.d
  FLAGS = -O3 -DNDEBUG -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include" -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/include" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = CMakeFiles/coffee_cofe.dir
  OBJECT_FILE_DIR = CMakeFiles/coffee_cofe.dir

build CMakeFiles/coffee_cofe.dir/my_application.cc.o: CXX_COMPILER__coffee_cofe_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/my_application.cc || cmake_object_order_depends_target_coffee_cofe
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DNDEBUG
  DEP_FILE = CMakeFiles/coffee_cofe.dir/my_application.cc.o.d
  FLAGS = -O3 -DNDEBUG -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include" -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/include" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = CMakeFiles/coffee_cofe.dir
  OBJECT_FILE_DIR = CMakeFiles/coffee_cofe.dir

build CMakeFiles/coffee_cofe.dir/flutter/generated_plugin_registrant.cc.o: CXX_COMPILER__coffee_cofe_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/generated_plugin_registrant.cc || cmake_object_order_depends_target_coffee_cofe
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DNDEBUG
  DEP_FILE = CMakeFiles/coffee_cofe.dir/flutter/generated_plugin_registrant.cc.o.d
  FLAGS = -O3 -DNDEBUG -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/include" -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/include" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = CMakeFiles/coffee_cofe.dir
  OBJECT_FILE_DIR = CMakeFiles/coffee_cofe.dir/flutter


# =============================================================================
# Link build statements for EXECUTABLE target coffee_cofe


#############################################
# Link the executable intermediates_do_not_run/coffee_cofe

build intermediates_do_not_run/coffee_cofe: CXX_EXECUTABLE_LINKER__coffee_cofe_Release CMakeFiles/coffee_cofe.dir/main.cc.o CMakeFiles/coffee_cofe.dir/my_application.cc.o CMakeFiles/coffee_cofe.dir/flutter/generated_plugin_registrant.cc.o | plugins/file_selector_linux/libfile_selector_linux_plugin.so plugins/url_launcher_linux/liburl_launcher_linux_plugin.so /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so /usr/lib/x86_64-linux-gnu/libgtk-3.so /usr/lib/x86_64-linux-gnu/libgdk-3.so /usr/lib/x86_64-linux-gnu/libz.so /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /usr/lib/x86_64-linux-gnu/libpango-1.0.so /usr/lib/x86_64-linux-gnu/libharfbuzz.so /usr/lib/x86_64-linux-gnu/libatk-1.0.so /usr/lib/x86_64-linux-gnu/libcairo-gobject.so /usr/lib/x86_64-linux-gnu/libcairo.so /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /usr/lib/x86_64-linux-gnu/libgio-2.0.so /usr/lib/x86_64-linux-gnu/libgobject-2.0.so /usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble plugins/file_selector_linux/libfile_selector_linux_plugin.so plugins/url_launcher_linux/liburl_launcher_linux_plugin.so
  FLAGS = -O3 -DNDEBUG
  LINK_LIBRARIES = -Wl,-rpath,"/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux:/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux:/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral:"  plugins/file_selector_linux/libfile_selector_linux_plugin.so  plugins/url_launcher_linux/liburl_launcher_linux_plugin.so  "/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so"  /usr/lib/x86_64-linux-gnu/libgtk-3.so  /usr/lib/x86_64-linux-gnu/libgdk-3.so  /usr/lib/x86_64-linux-gnu/libz.so  /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /usr/lib/x86_64-linux-gnu/libpango-1.0.so  /usr/lib/x86_64-linux-gnu/libharfbuzz.so  /usr/lib/x86_64-linux-gnu/libatk-1.0.so  /usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /usr/lib/x86_64-linux-gnu/libcairo.so  /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /usr/lib/x86_64-linux-gnu/libgio-2.0.so  /usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = CMakeFiles/coffee_cofe.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = intermediates_do_not_run/coffee_cofe
  TARGET_PDB = coffee_cofe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe" && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe" && /usr/bin/cmake --regenerate-during-build -S"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux" -B"/home/<USER>/Desktop/untitled folder/coffee_cofe"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe" && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe" && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe" && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for flutter_assemble

build flutter/flutter_assemble: phony flutter/CMakeFiles/flutter_assemble /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_


#############################################
# Utility command for edit_cache

build flutter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/flutter" && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build flutter/edit_cache: phony flutter/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build flutter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/flutter" && /usr/bin/cmake --regenerate-during-build -S"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux" -B"/home/<USER>/Desktop/untitled folder/coffee_cofe"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build flutter/rebuild_cache: phony flutter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build flutter/list_install_components: phony


#############################################
# Utility command for install

build flutter/CMakeFiles/install.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/flutter" && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build flutter/install: phony flutter/CMakeFiles/install.util


#############################################
# Utility command for install/local

build flutter/CMakeFiles/install/local.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/flutter" && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build flutter/install/local: phony flutter/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build flutter/CMakeFiles/install/strip.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/flutter" && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build flutter/install/strip: phony flutter/CMakeFiles/install/strip.util


#############################################
# Phony custom command for flutter/CMakeFiles/flutter_assemble

build flutter/CMakeFiles/flutter_assemble | ${cmake_ninja_workdir}flutter/CMakeFiles/flutter_assemble: phony /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/flutter_linux.h


#############################################
# Custom command for /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so

build /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_ | ${cmake_ninja_workdir}flutter/_phony_: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/flutter" && /usr/bin/cmake -E env FLUTTER_ROOT=/home/<USER>/fvm/versions/3.24.5 "PROJECT_DIR=/home/<USER>/Desktop/untitled folder/coffee_cofe" DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=/home/<USER>/Desktop/untitled folder/coffee_cofe/.dart_tool/package_config.json" "FLUTTER_TARGET=/home/<USER>/Desktop/untitled folder/coffee_cofe/lib/main.dart" /home/<USER>/fvm/versions/3.24.5/packages/flutter_tools/bin/tool_backend.sh linux-x64 Release
  DESC = Generating /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_engine.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_call.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_method_response.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_value.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/fl_view.h, /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/flutter_linux/flutter_linux.h, _phony_
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/generated_plugins.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target file_selector_linux_plugin


#############################################
# Order-only phony target for file_selector_linux_plugin

build cmake_object_order_depends_target_file_selector_linux_plugin: phony || flutter/flutter_assemble

build plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o: CXX_COMPILER__file_selector_linux_plugin_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/file_selector_plugin.cc || cmake_object_order_depends_target_file_selector_linux_plugin
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DFLUTTER_PLUGIN_IMPL -DNDEBUG -Dfile_selector_linux_plugin_EXPORTS
  DEP_FILE = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o.d
  FLAGS = -O3 -DNDEBUG -fPIC -fvisibility=hidden -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir

build plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o: CXX_COMPILER__file_selector_linux_plugin_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/messages.g.cc || cmake_object_order_depends_target_file_selector_linux_plugin
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DFLUTTER_PLUGIN_IMPL -DNDEBUG -Dfile_selector_linux_plugin_EXPORTS
  DEP_FILE = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o.d
  FLAGS = -O3 -DNDEBUG -fPIC -fvisibility=hidden -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target file_selector_linux_plugin


#############################################
# Link the shared library plugins/file_selector_linux/libfile_selector_linux_plugin.so

build plugins/file_selector_linux/libfile_selector_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__file_selector_linux_plugin_Release plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/file_selector_plugin.cc.o plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir/messages.g.cc.o | /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so /usr/lib/x86_64-linux-gnu/libgtk-3.so /usr/lib/x86_64-linux-gnu/libgdk-3.so /usr/lib/x86_64-linux-gnu/libz.so /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /usr/lib/x86_64-linux-gnu/libpango-1.0.so /usr/lib/x86_64-linux-gnu/libharfbuzz.so /usr/lib/x86_64-linux-gnu/libatk-1.0.so /usr/lib/x86_64-linux-gnu/libcairo-gobject.so /usr/lib/x86_64-linux-gnu/libcairo.so /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /usr/lib/x86_64-linux-gnu/libgio-2.0.so /usr/lib/x86_64-linux-gnu/libgobject-2.0.so /usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -O3 -DNDEBUG
  LINK_LIBRARIES = -Wl,-rpath,"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral"  "/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so"  /usr/lib/x86_64-linux-gnu/libgtk-3.so  /usr/lib/x86_64-linux-gnu/libgdk-3.so  /usr/lib/x86_64-linux-gnu/libz.so  /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /usr/lib/x86_64-linux-gnu/libpango-1.0.so  /usr/lib/x86_64-linux-gnu/libharfbuzz.so  /usr/lib/x86_64-linux-gnu/libatk-1.0.so  /usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /usr/lib/x86_64-linux-gnu/libcairo.so  /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /usr/lib/x86_64-linux-gnu/libgio-2.0.so  /usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/file_selector_linux/CMakeFiles/file_selector_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libfile_selector_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/file_selector_linux/libfile_selector_linux_plugin.so
  TARGET_PDB = file_selector_linux_plugin.so.dbg


#############################################
# Utility command for edit_cache

build plugins/file_selector_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux" && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/file_selector_linux/edit_cache: phony plugins/file_selector_linux/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build plugins/file_selector_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux" && /usr/bin/cmake --regenerate-during-build -S"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux" -B"/home/<USER>/Desktop/untitled folder/coffee_cofe"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/file_selector_linux/rebuild_cache: phony plugins/file_selector_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build plugins/file_selector_linux/list_install_components: phony


#############################################
# Utility command for install

build plugins/file_selector_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux" && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/file_selector_linux/install: phony plugins/file_selector_linux/CMakeFiles/install.util


#############################################
# Utility command for install/local

build plugins/file_selector_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux" && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/file_selector_linux/install/local: phony plugins/file_selector_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build plugins/file_selector_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/file_selector_linux/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux" && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/file_selector_linux/install/strip: phony plugins/file_selector_linux/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/generated_plugins.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target url_launcher_linux_plugin


#############################################
# Order-only phony target for url_launcher_linux_plugin

build cmake_object_order_depends_target_url_launcher_linux_plugin: phony || flutter/flutter_assemble

build plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/messages.g.cc.o: CXX_COMPILER__url_launcher_linux_plugin_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/messages.g.cc || cmake_object_order_depends_target_url_launcher_linux_plugin
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DFLUTTER_PLUGIN_IMPL -DNDEBUG -Durl_launcher_linux_plugin_EXPORTS
  DEP_FILE = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/messages.g.cc.o.d
  FLAGS = -O3 -DNDEBUG -fPIC -fvisibility=hidden -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir

build plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/url_launcher_plugin.cc.o: CXX_COMPILER__url_launcher_linux_plugin_Release /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/url_launcher_plugin.cc || cmake_object_order_depends_target_url_launcher_linux_plugin
  DEFINES = -DAPPLICATION_ID=\"com.example.coffee_cofe\" -DFLUTTER_PLUGIN_IMPL -DNDEBUG -Durl_launcher_linux_plugin_EXPORTS
  DEP_FILE = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/url_launcher_plugin.cc.o.d
  FLAGS = -O3 -DNDEBUG -fPIC -fvisibility=hidden -Wall -Werror -O3 -pthread
  INCLUDES = -I"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral" -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target url_launcher_linux_plugin


#############################################
# Link the shared library plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

build plugins/url_launcher_linux/liburl_launcher_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__url_launcher_linux_plugin_Release plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/messages.g.cc.o plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir/url_launcher_plugin.cc.o | /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so /usr/lib/x86_64-linux-gnu/libgtk-3.so /usr/lib/x86_64-linux-gnu/libgdk-3.so /usr/lib/x86_64-linux-gnu/libz.so /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /usr/lib/x86_64-linux-gnu/libpango-1.0.so /usr/lib/x86_64-linux-gnu/libharfbuzz.so /usr/lib/x86_64-linux-gnu/libatk-1.0.so /usr/lib/x86_64-linux-gnu/libcairo-gobject.so /usr/lib/x86_64-linux-gnu/libcairo.so /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /usr/lib/x86_64-linux-gnu/libgio-2.0.so /usr/lib/x86_64-linux-gnu/libgobject-2.0.so /usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  LANGUAGE_COMPILE_FLAGS = -O3 -DNDEBUG
  LINK_LIBRARIES = -Wl,-rpath,"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral"  "/home/<USER>/Desktop/untitled folder/coffee_cofe/linux/flutter/ephemeral/libflutter_linux_gtk.so"  /usr/lib/x86_64-linux-gnu/libgtk-3.so  /usr/lib/x86_64-linux-gnu/libgdk-3.so  /usr/lib/x86_64-linux-gnu/libz.so  /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /usr/lib/x86_64-linux-gnu/libpango-1.0.so  /usr/lib/x86_64-linux-gnu/libharfbuzz.so  /usr/lib/x86_64-linux-gnu/libatk-1.0.so  /usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /usr/lib/x86_64-linux-gnu/libcairo.so  /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /usr/lib/x86_64-linux-gnu/libgio-2.0.so  /usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /usr/lib/x86_64-linux-gnu/libglib-2.0.so
  OBJECT_DIR = plugins/url_launcher_linux/CMakeFiles/url_launcher_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = liburl_launcher_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = plugins/url_launcher_linux/liburl_launcher_linux_plugin.so
  TARGET_PDB = url_launcher_linux_plugin.so.dbg


#############################################
# Utility command for edit_cache

build plugins/url_launcher_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux" && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build plugins/url_launcher_linux/edit_cache: phony plugins/url_launcher_linux/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build plugins/url_launcher_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux" && /usr/bin/cmake --regenerate-during-build -S"/home/<USER>/Desktop/untitled folder/coffee_cofe/linux" -B"/home/<USER>/Desktop/untitled folder/coffee_cofe"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/url_launcher_linux/rebuild_cache: phony plugins/url_launcher_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build plugins/url_launcher_linux/list_install_components: phony


#############################################
# Utility command for install

build plugins/url_launcher_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/url_launcher_linux/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux" && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/url_launcher_linux/install: phony plugins/url_launcher_linux/CMakeFiles/install.util


#############################################
# Utility command for install/local

build plugins/url_launcher_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/url_launcher_linux/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux" && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/url_launcher_linux/install/local: phony plugins/url_launcher_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build plugins/url_launcher_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/url_launcher_linux/all
  COMMAND = cd "/home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux" && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/url_launcher_linux/install/strip: phony plugins/url_launcher_linux/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build coffee_cofe: phony intermediates_do_not_run/coffee_cofe

build file_selector_linux_plugin: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

build flutter_assemble: phony flutter/flutter_assemble

build libfile_selector_linux_plugin.so: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

build liburl_launcher_linux_plugin.so: phony plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

build url_launcher_linux_plugin: phony plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/untitled folder/coffee_cofe

build all: phony intermediates_do_not_run/coffee_cofe flutter/all plugins/file_selector_linux/all plugins/url_launcher_linux/all

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/untitled folder/coffee_cofe/flutter

build flutter/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/file_selector_linux

build plugins/file_selector_linux/all: phony plugins/file_selector_linux/libfile_selector_linux_plugin.so

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/untitled folder/coffee_cofe/plugins/url_launcher_linux

build plugins/url_launcher_linux/all: phony plugins/url_launcher_linux/liburl_launcher_linux_plugin.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/generated_plugins.cmake /usr/share/cmake-3.25/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.25/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.25/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.25/Modules/Compiler/GNU.cmake /usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.25/Modules/FindPackageMessage.cmake /usr/share/cmake-3.25/Modules/FindPkgConfig.cmake /usr/share/cmake-3.25/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.25/Modules/Platform/Linux.cmake /usr/share/cmake-3.25/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.25.1/CMakeCXXCompiler.cmake CMakeFiles/3.25.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/linux/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/linux/CMakeLists.txt /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Desktop/untitled$ folder/coffee_cofe/linux/flutter/generated_plugins.cmake /usr/share/cmake-3.25/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.25/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.25/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.25/Modules/Compiler/GNU.cmake /usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.25/Modules/FindPackageMessage.cmake /usr/share/cmake-3.25/Modules/FindPkgConfig.cmake /usr/share/cmake-3.25/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.25/Modules/Platform/Linux.cmake /usr/share/cmake-3.25/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.25.1/CMakeCXXCompiler.cmake CMakeFiles/3.25.1/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
