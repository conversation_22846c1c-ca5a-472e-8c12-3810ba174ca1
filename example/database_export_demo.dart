import 'dart:io';
import 'package:coffee_cofe/database/database_export_utility.dart';

/// Demo script showing how to use the database export functionality
void main() async {
  print('=== Coffee POS Database Export Demo ===\n');

  try {
    // Demo 1: Export schema only with automatic date filename
    print('1. Exporting schema only with automatic filename...');
    final schemaPath = await DatabaseExportUtility.exportSchemaOnly();
    print('   ✅ Schema exported to: ${schemaPath.split('/').last}');
    print('   📁 Full path: $schemaPath\n');

    // Demo 2: Export schema with custom filename
    print('2. Exporting schema with custom filename...');
    final customPath = await DatabaseExportUtility.exportSchemaOnly(
      customFileName: 'my_coffee_schema_backup.sql'
    );
    print('   ✅ Custom schema exported to: ${customPath.split('/').last}');
    print('   📁 Full path: $customPath\n');

    // Demo 3: Export complete database with data
    print('3. Exporting complete database with data...');
    final fullPath = await DatabaseExportUtility.exportSchemaWithData(
      customFileName: 'complete_database_backup.sql'
    );
    print('   ✅ Complete database exported to: ${fullPath.split('/').last}');
    print('   📁 Full path: $fullPath\n');

    // Demo 4: List all exported files
    print('4. Listing all exported files...');
    final exportedFiles = await DatabaseExportUtility.getExportedFiles();
    if (exportedFiles.isNotEmpty) {
      print('   📋 Found ${exportedFiles.length} exported files:');
      for (int i = 0; i < exportedFiles.length; i++) {
        final file = exportedFiles[i];
        final stat = file.statSync();
        final fileName = file.path.split('/').last;
        final size = _formatFileSize(stat.size);
        final date = stat.modified.toIso8601String().split('T')[0];
        print('   ${i + 1}. $fileName ($size) - $date');
      }
    } else {
      print('   📭 No exported files found');
    }
    print('');

    // Demo 5: Show sample export content
    print('5. Sample export file content preview...');
    if (exportedFiles.isNotEmpty) {
      final sampleFile = File(exportedFiles.first.path);
      final content = await sampleFile.readAsString();
      final lines = content.split('\n');
      
      print('   📄 First 20 lines of ${exportedFiles.first.path.split('/').last}:');
      print('   ${'=' * 60}');
      for (int i = 0; i < 20 && i < lines.length; i++) {
        print('   ${(i + 1).toString().padLeft(2)}: ${lines[i]}');
      }
      if (lines.length > 20) {
        print('   ... (${lines.length - 20} more lines)');
      }
      print('   ${'=' * 60}');
    }
    print('');

    // Demo 6: File management operations
    print('6. File management demo...');
    if (exportedFiles.length > 1) {
      final fileToDelete = exportedFiles.last.path;
      final fileName = fileToDelete.split('/').last;
      
      print('   🗑️  Deleting file: $fileName');
      final deleted = await DatabaseExportUtility.deleteExportedFile(fileToDelete);
      
      if (deleted) {
        print('   ✅ File deleted successfully');
        
        // Verify deletion
        final updatedFiles = await DatabaseExportUtility.getExportedFiles();
        print('   📊 Files remaining: ${updatedFiles.length}');
      } else {
        print('   ❌ Failed to delete file');
      }
    } else {
      print('   ℹ️  Not enough files for deletion demo');
    }
    print('');

    // Demo 7: Export directory information
    print('7. Export directory information...');
    if (exportedFiles.isNotEmpty) {
      final exportDir = File(exportedFiles.first.path).parent;
      final dirStat = exportDir.statSync();
      
      print('   📁 Export directory: ${exportDir.path}');
      print('   📊 Directory size: ${_formatFileSize(_getDirectorySize(exportDir))}');
      print('   📅 Last modified: ${dirStat.modified.toIso8601String().split('T')[0]}');
    }
    print('');

    print('=== Demo completed successfully! ===');
    print('');
    print('💡 Tips:');
    print('   • Use schema-only exports for database structure documentation');
    print('   • Use complete exports for full database backups');
    print('   • Custom filenames help organize exports by purpose');
    print('   • Regular exports help with version control and recovery');
    print('');
    print('🚀 Access the export feature in the app:');
    print('   Main Menu → Database → Export Schema');

  } catch (e, stack) {
    print('❌ Demo failed with error: $e');
    print('Stack trace: $stack');
  }
}

/// Format file size in human-readable format
String _formatFileSize(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
}

/// Get total size of directory
int _getDirectorySize(Directory dir) {
  int totalSize = 0;
  try {
    final files = dir.listSync(recursive: true);
    for (final file in files) {
      if (file is File) {
        totalSize += file.lengthSync();
      }
    }
  } catch (e) {
    // Ignore errors
  }
  return totalSize;
}
