# API Service Repository Implementation

This document outlines the implementation of the API service repository pattern for handling API calls in the Coffee POS application.

## Architecture Overview

The implementation follows a clean architecture pattern with the following layers:

1. **Network Layer** (`lib/core/network/`)
   - `dio_client.dart` - HTTP client configuration with interceptors
   - `api_endpoints.dart` - Centralized API endpoint definitions
   - `api_response.dart` - Generic response wrapper and exception handling

2. **Repository Layer** (`lib/repositories/`)
   - `auth_repository.dart` - Authentication-related API calls
   - Future repositories for products, sales, employees, etc.

3. **Provider Layer** (`lib/providers/`)
   - `auth_provider.dart` - Enhanced authentication provider using repository
   - Existing providers updated to use repositories

4. **Service Manager** (`lib/services/`)
   - `api_service_manager.dart` - Central manager for all repositories

## Features Implemented

### Authentication Repository (`auth_repository.dart`)

✅ **Login** - User authentication with mobile/email and password
✅ **Register** - New user registration
✅ **Logout** - User logout with token cleanup
✅ **Refresh Token** - Automatic token refresh
✅ **Forgot Password** - Password reset email
✅ **Reset Password** - Password reset with token
✅ **Verify OTP** - OTP verification
✅ **Get Profile** - Fetch user profile
✅ **Authentication Check** - Check if user is authenticated

### Network Configuration

✅ **Dio Client** with:
- Request/Response interceptors
- Automatic token injection
- Error handling
- Timeout configuration
- Debug logging
- Token expiration handling

✅ **API Response Wrapper**:
- Generic response handling
- Success/Error states
- Status codes
- Error messages
- Validation errors

✅ **Exception Handling**:
- `ApiException` - API-related errors
- `NetworkException` - Connectivity issues
- `TimeoutException` - Request timeouts

## Usage Examples

### 1. Basic Login Implementation

```dart
// In your provider or service
final authRepository = AuthRepository();

Future<bool> login(String mobile, String password) async {
  try {
    final response = await authRepository.login(
      identifier: mobile,
      password: password,
    );
    
    if (response.success) {
      // Handle successful login
      final user = response.data?.response;
      return true;
    } else {
      // Handle login failure
      print('Login failed: ${response.message}');
      return false;
    }
  } catch (e) {
    // Handle exceptions
    print('Login error: $e');
    return false;
  }
}
```

### 2. Using the Enhanced Auth Provider

```dart
// In your widget
Consumer<AuthProvider>(
  builder: (context, authProvider, child) {
    return Column(
      children: [
        if (authProvider.errorMessage != null)
          Text(
            authProvider.errorMessage!,
            style: TextStyle(color: Colors.red),
          ),
        ElevatedButton(
          onPressed: authProvider.isLoading 
            ? null 
            : () async {
                final success = await authProvider.login();
                if (success) {
                  // Navigate to home screen
                }
              },
          child: authProvider.isLoading 
            ? CircularProgressIndicator()
            : Text('Login'),
        ),
      ],
    );
  },
)
```

### 3. Using Service Manager

```dart
// Initialize in main.dart
void main() {
  ApiServiceManager.initialize();
  runApp(MyApp());
}

// Use in any service or provider
final authRepo = ApiServiceManager.instance.auth;
final response = await authRepo.login(identifier: mobile, password: password);
```

## Configuration

### 1. Update API Base URL

Edit `lib/core/network/api_endpoints.dart`:

```dart
class ApiEndpoints {
  static const String baseUrl = 'https://your-api-server.com/api/v1';
  // ... rest of endpoints
}
```

### 2. Customize Network Settings

Edit `lib/core/network/dio_client.dart`:

```dart
_dio.options = BaseOptions(
  baseUrl: ApiEndpoints.baseUrl,
  connectTimeout: const Duration(seconds: 30), // Adjust as needed
  receiveTimeout: const Duration(seconds: 30),
  sendTimeout: const Duration(seconds: 30),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add custom headers here
  },
);
```

## Adding New Repositories

### 1. Create Repository Class

```dart
// lib/repositories/product_repository.dart
class ProductRepository {
  final DioClient _dioClient = DioClient.instance;

  Future<ApiResponse<List<Product>>> getProducts() async {
    try {
      final response = await _dioClient.get<List<Product>>(
        ApiEndpoints.products,
        fromJson: (json) => (json as List)
            .map((item) => Product.fromJson(item))
            .toList(),
      );
      return response;
    } catch (e) {
      return ApiResponse.error(message: e.toString());
    }
  }
}
```

### 2. Add to Service Manager

```dart
// lib/services/api_service_manager.dart
class ApiServiceManager {
  late final AuthRepository _authRepository;
  late final ProductRepository _productRepository; // Add this

  ApiServiceManager._internal() {
    _authRepository = AuthRepository();
    _productRepository = ProductRepository(); // Add this
  }

  AuthRepository get auth => _authRepository;
  ProductRepository get products => _productRepository; // Add this
}
```

### 3. Use in Provider

```dart
class ProductProvider with ChangeNotifier {
  final ProductRepository _productRepository = ApiServiceManager.instance.products;
  
  Future<void> loadProducts() async {
    final response = await _productRepository.getProducts();
    if (response.success) {
      // Handle success
    }
  }
}
```

## Error Handling Best Practices

### 1. Repository Level

```dart
Future<ApiResponse<User>> getProfile() async {
  try {
    final response = await _dioClient.get<User>(
      ApiEndpoints.profile,
      fromJson: (json) => User.fromJson(json),
    );
    return response;
  } catch (e) {
    if (e is ApiException) {
      return ApiResponse.error(
        message: e.message,
        statusCode: e.statusCode,
        errors: e.errors,
      );
    } else if (e is NetworkException) {
      return ApiResponse.error(
        message: 'No internet connection. Please check your network.',
      );
    } else {
      return ApiResponse.error(
        message: 'An unexpected error occurred.',
      );
    }
  }
}
```

### 2. Provider Level

```dart
Future<bool> fetchUserProfile() async {
  try {
    final response = await _authRepository.getProfile();
    if (response.success && response.data != null) {
      _currentUser = response.data!;
      notifyListeners();
      return true;
    } else {
      _setError(response.message);
      return false;
    }
  } catch (e) {
    _setError('Failed to fetch profile');
    return false;
  }
}
```

## Testing

### 1. Mock Repository for Testing

```dart
class MockAuthRepository implements AuthRepository {
  @override
  Future<ApiResponse<LoginResponse>> login({
    required String identifier,
    required String password,
  }) async {
    // Return mock response
    return ApiResponse.success(
      message: 'Login successful',
      data: LoginResponse(/* mock data */),
    );
  }
}
```

## Migration from Existing Code

### 1. Update Existing Providers

Replace direct API calls with repository calls:

```dart
// Before
final response = await http.post(url, body: data);

// After
final response = await _authRepository.login(identifier: mobile, password: password);
```

### 2. Update UI Components

Use the enhanced providers with better error handling and loading states.

## Next Steps

1. **Create Product Repository** - Handle product CRUD operations
2. **Create Sales Repository** - Handle sales and billing operations
3. **Create Employee Repository** - Handle employee management
4. **Add Offline Support** - Cache responses for offline usage
5. **Add Request Retry Logic** - Automatic retry for failed requests
6. **Add Request Caching** - Cache frequently accessed data

## Benefits

✅ **Centralized API Management** - All API calls in one place
✅ **Consistent Error Handling** - Standardized error responses
✅ **Automatic Token Management** - Handles authentication automatically
✅ **Type Safety** - Generic responses with proper typing
✅ **Easy Testing** - Mockable repositories
✅ **Scalable Architecture** - Easy to add new endpoints
✅ **Provider Integration** - Works seamlessly with existing providers
