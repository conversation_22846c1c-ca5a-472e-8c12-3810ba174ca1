# Mock Login System Guide

This guide explains how to use the temporary/fake login JSON system that can be easily replaced with real API calls.

## 🎯 Overview

The mock login system provides a complete authentication flow using fake JSON data, making it easy to:
- Test login functionality without a backend
- Develop and debug authentication features
- Easily switch to real API when ready
- Demonstrate the app with realistic user data

## 🔧 Configuration

### Quick Setup
1. **Enable Mock Mode**: Set `AppConfig.useMockAuth = true` in `lib/config/app_config.dart`
2. **Show Demo Users**: Set `AppConfig.showMockUserList = true` to display available test users
3. **Run the app**: Mock users will be shown on the login screen

### Configuration File: `lib/config/app_config.dart`
```dart
class AppConfig {
  static const bool useMockAuth = true;        // Enable/disable mock authentication
  static const bool enableDebugMode = true;    // Show debug information
  static const bool showMockUserList = true;   // Show mock users on login screen
  
  // When ready for production:
  // static const bool useMockAuth = false;
}
```

## 👥 Available Mock Users

The system comes with 4 pre-configured test users:

### 1. <PERSON> (Admin)
- **Email**: `<EMAIL>`
- **Mobile**: `1234567890`
- **Password**: `123456`
- **Role**: Admin

### 2. Jane Smith (User)
- **Email**: `<EMAIL>`
- **Mobile**: `9876543210`
- **Password**: `password`
- **Role**: User

### 3. Admin User (Admin)
- **Email**: `<EMAIL>`
- **Mobile**: `5555555555`
- **Password**: `admin123`
- **Role**: Admin

### 4. Test User (User)
- **Email**: `<EMAIL>`
- **Mobile**: `1111111111`
- **Password**: `test`
- **Role**: User

## 🚀 How to Test

### Login Testing
1. Open the app and go to the login screen
2. You'll see a "Demo Users" card showing all available test users
3. Tap any credential to copy it to clipboard
4. Use the credentials to test login functionality
5. Both email and mobile number work as login identifiers

### Registration Testing
1. Go to the signup screen
2. Enter new user details
3. The system will create a new mock user
4. You can immediately login with the new credentials

### Error Testing
1. Try logging in with invalid credentials
2. Try registering with existing email/mobile
3. The system will show appropriate error messages

## 📁 File Structure

```
lib/
├── config/
│   └── app_config.dart              # Configuration settings
├── services/
│   └── mock_auth_service.dart       # Mock authentication service
├── repositories/
│   └── auth_repository.dart         # Repository with mock/real API switching
├── features/auth/
│   ├── login_screen.dart           # Login screen with mock users demo
│   ├── mock_users_demo.dart        # Widget showing available test users
│   └── login_provider.dart         # Provider using the repository
└── core/network/
    ├── api_response.dart           # Response wrapper
    └── dio_client.dart             # HTTP client (for real API)
```

## 🔄 Switching to Real API

When you're ready to use real API calls:

### Step 1: Update Configuration
```dart
// lib/config/app_config.dart
class AppConfig {
  static const bool useMockAuth = false;  // Disable mock auth
  static const bool showMockUserList = false;  // Hide demo users
}
```

### Step 2: Update API Endpoints
```dart
// lib/core/network/api_endpoints.dart
class ApiEndpoints {
  static const String baseUrl = 'https://your-real-api.com/api/v1';
  // ... rest of endpoints
}
```

### Step 3: Test Real API
- The `AuthRepository` will automatically switch to using `DioClient`
- All existing UI and provider code remains unchanged
- Only the data source changes from mock to real API

## 🛠 Customization

### Adding New Mock Users
```dart
// lib/services/mock_auth_service.dart
static final List<Map<String, dynamic>> _mockUsers = [
  // Add your custom mock users here
  {
    "userId": "user_005",
    "userName": "Custom User",
    "emailAddress": "<EMAIL>",
    "mobileNo": "**********",
    "password": "custom123",
    "role": "manager",
    "isActive": true,
    "workspaceId": "ws_001",
    "token": "mock_token_custom_123",
    "refreshToken": "refresh_token_custom_456"
  },
];
```

### Customizing Network Delay
```dart
// lib/services/mock_auth_service.dart
static const Duration _networkDelay = Duration(seconds: 2); // Simulate slower network
```

### Adding Error Simulation
```dart
// lib/config/app_config.dart
static const bool simulateNetworkErrors = true; // Test error handling
```

## 🎨 UI Features

### Mock Users Demo Widget
- Shows all available test users
- Tap to copy credentials
- Color-coded by user role
- Only visible in development mode
- Automatically hidden in production

### Login Screen Enhancements
- Displays demo users when `showMockUserList = true`
- Copy-to-clipboard functionality
- Visual indicators for demo mode
- Seamless integration with existing UI

## 🔍 Debugging

### Print Mock Users
```dart
// Call this anywhere to see available users
MockAuthService.printMockUsers();
```

### Print App Configuration
```dart
// Call this to see current configuration
AppConfig.printConfig();
```

### Check Current Mode
```dart
if (AppConfig.useMockAuth) {
  print('Using mock authentication');
} else {
  print('Using real API authentication');
}
```

## ✅ Benefits

1. **Easy Development**: Test authentication without backend setup
2. **Realistic Testing**: Multiple user roles and scenarios
3. **Quick Switching**: One configuration change to switch to real API
4. **No Code Changes**: UI and business logic remain unchanged
5. **Error Testing**: Simulate various error conditions
6. **Demo Ready**: Perfect for demonstrations and presentations

## 🚨 Important Notes

1. **Security**: Mock passwords are visible in code - only for development!
2. **Data Persistence**: Mock users reset when app restarts
3. **Production**: Always set `useMockAuth = false` for production builds
4. **Testing**: Use mock mode for unit tests and integration tests

## 🔄 Migration Path

1. **Phase 1**: Develop with mock authentication
2. **Phase 2**: Implement real API endpoints
3. **Phase 3**: Test with real API in development
4. **Phase 4**: Deploy with real API in production

The mock system is designed to make this migration seamless with minimal code changes!
